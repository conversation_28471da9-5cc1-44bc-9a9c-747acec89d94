package com.crrc.network;


import static com.crrc.common.Constant.BaseUrl;
import static com.crrc.common.Constant.RESPONSE_CODE;

import java.io.IOException;

import com.crrc.common.BaseResponse;
import com.crrc.network.errorhandler.ExceptionHandle;
import io.reactivex.functions.Function;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * <AUTHOR>
 * 自定义Demo域名下的获取retrofit实例
 */
public class NetworkApi extends com.crrc.network.base.NetworkApi {

    private static volatile NetworkApi sInstance;

    public static NetworkApi getInstance() {
        if (sInstance == null) {
            synchronized (NetworkApi.class) {
                if (sInstance == null) {
                    sInstance = new NetworkApi();
                }
            }
        }
        return sInstance;
    }

    public static <T> T getService(Class<T> service) {
        return getInstance().getRetrofit(service).create(service);
    }

    @Override
    protected Interceptor getInterceptor() {
        return new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                Request.Builder builder = chain.request().newBuilder();
                return chain.proceed(builder.build());
            }
        };
    }

    @Override
    protected <T> Function<T, T> getAppErrorHandler() {
        return new Function<T, T>() {
            @Override
            public T apply(T response) throws Exception {
                if (response instanceof BaseResponse && ((BaseResponse) response).baseResponseCode != RESPONSE_CODE) {
                    ExceptionHandle.ServerException exception = new ExceptionHandle.ServerException();
                    exception.code = ((BaseResponse) response).baseResponseCode;
                    exception.message = ((BaseResponse) response).baseResponseReason != null ? ((BaseResponse) response).baseResponseReason : "";
                    throw exception;
                }
                return response;
            }
        };
    }


    @Override
    public String getDev() {
        return BaseUrl;
    }
}
