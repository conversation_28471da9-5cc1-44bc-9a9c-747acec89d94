package com.crrc.network.api;


import static com.crrc.common.Constant.MOBILE_TERMINAL;
import static com.crrc.common.Constant.RULE;

import com.crrc.common.BaseResponse;
import com.crrc.common.bean.response.AlarmResponse;
import com.crrc.common.bean.response.Article;
import com.crrc.common.bean.response.CaseDetailResponse;
import com.crrc.common.bean.response.CaseListResponse;
import com.crrc.common.bean.response.CurrentDirsResponse;
import com.crrc.common.bean.response.DeviceDetailResponse;
import com.crrc.common.bean.response.DeviceInfo;
import com.crrc.common.bean.response.DirsListResponse;
import com.crrc.common.bean.response.EmergencyDetailResponse;
import com.crrc.common.bean.response.EmergencyEventListResponse;
import com.crrc.common.bean.response.EmergencyOrderListResponse;
import com.crrc.common.bean.response.FaultDetailResponse;
import com.crrc.common.bean.response.FaultOrderListResponse;
import com.crrc.common.bean.response.FilesByLabelResponse;
import com.crrc.common.bean.response.LabelListResponse;
import com.crrc.common.bean.response.LineDevice;
import com.crrc.common.bean.response.LineInfoResponse;
import com.crrc.common.bean.response.LineTool;
import com.crrc.common.bean.response.LoginBean;
import com.crrc.common.bean.response.MembersResponse;
import com.crrc.common.bean.response.OrderFilterParamResponse;
import com.crrc.common.bean.response.PickMaterialResponse;
import com.crrc.common.bean.response.PlanOrderListResponse;
import com.crrc.common.bean.response.ProcedureResponse;
import com.crrc.common.bean.response.SearchFileResponse;
import com.crrc.common.bean.response.SearchResponse;
import com.crrc.common.bean.response.StationAlarmResponse;
import com.crrc.common.bean.response.TaskOverviewResponse;
import com.crrc.common.bean.response.TempOrderListResponse;
import com.crrc.common.bean.response.UserInfoResponse;
import com.crrc.common.bean.response.WorkOrderCountResponse;
import com.crrc.common.bean.response.WorkOrderDetailResponse;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Query;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

public interface ApiInterface2 {


    // 测试

    /**
     * 下载文件
     *
     * @param fileUrl 文件url
     * @return a
     */
    @Streaming
    @GET
    Observable<ResponseBody> downLoad(@Url String fileUrl);


    @GET(RULE + "/?apipost_id=1672ffee363006")
    Observable<BaseResponse<List<Article>>> getArticles(@Query("page") int page, @Query("pageSize") int pageSize);

    //#######################首页接口######################//

    /**
     * 获取任务总览信息
     *
     * @return 任务总览数据
     */
    @POST(RULE + "/home/<USER>" + "?apipost_id=1c8cff3db63006")
    Observable<BaseResponse<TaskOverviewResponse>> getTaskOverview();

    /**
     * 搜索
     *
     * @param body keyword
     * @return 搜索结果
     */
    @POST(RULE + "/home/<USER>")
    Observable<BaseResponse<SearchResponse>> search(@Body RequestBody body);

    @POST(RULE + "/home/<USER>")
    Observable<BaseResponse<DeviceDetailResponse>> scan(@Body RequestBody body);
    //#######################线路图接口######################//

    /**
     * 线路统计信息
     *
     * @return 线路统计信息
     */

    @POST(RULE + "/mobileTerminal/line/statistics" + "?apipost_id=1d05bd14b51002")
    Observable<BaseResponse<List<LineInfoResponse>>> getLineInfo();

    /**
     * 线路报警统计信息
     *
     * @param body id
     * @return
     */
    @POST(RULE + "/mobileTerminal/statistics/alarm" + "?apipost_id=24c9ebc9751002")
    Observable<BaseResponse<List<StationAlarmResponse>>> getStationAlarmInfo(@Body RequestBody body);

    /**
     * 备品备件信息
     *
     * @param body storeId
     * @return 备品备件信息
     */
    @POST(RULE + "/mobileTerminal/line/statistics/device" + "?apipost_id=24ca30e1b51007")
    Observable<BaseResponse<List<LineDevice>>> getLineDeviceInfo(@Body RequestBody body);

    /**
     * 工器具信息
     *
     * @param body storeId
     * @return
     */
    @POST(RULE + "/mobileTerminal/line/statistics/tools" + "?apipost_id=24ca594535100b")
    Observable<BaseResponse<List<LineTool>>> getLineToolsInfo(@Body RequestBody body);


    //#######################工单接口######################//

    /**
     * 工单筛选参数
     *
     * @return 筛选到的工单列表参数
     */
    @POST(RULE + "/order/opt" + "?apipost_id=4d8ac4637f002")
    Observable<BaseResponse<OrderFilterParamResponse>> getFilterParamList();


    /**
     * 获取计划工单列表
     *
     * @return 计划工单列表数据
     */
    @POST(MOBILE_TERMINAL + "/order/list/plan" + "?apipost_id=1c9ceed9f6300d")
    Observable<BaseResponse<PlanOrderListResponse>> getPlanOrderList(@Body RequestBody body);

    @POST(MOBILE_TERMINAL + "/order/list/fault" + "?apipost_id=1cd97f4bb5900f")
    Observable<BaseResponse<FaultOrderListResponse>> getFaultOrderList(@Body RequestBody body);

    @POST(MOBILE_TERMINAL + "/order/list/emergency" + "?apipost_id=1cdff4cb759010")
    Observable<BaseResponse<EmergencyOrderListResponse>> getEmergencyOrderList(@Body RequestBody body);

    @POST(MOBILE_TERMINAL + "/order/list/temp" + "?apipost_id=1ce11629f59011")
    Observable<BaseResponse<TempOrderListResponse>> getTempOrderList(@Body RequestBody body);

    /**
     * 获取工单数量统计
     *
     * @return 工单数量统计数据
     */
    @POST(MOBILE_TERMINAL + "/order/list/view" + "?apipost_id=1dde0fa1b59002")
    Observable<BaseResponse<WorkOrderCountResponse>> getWorkOrderCount();

    /**
     * 获取工单详情
     *
     * @return 工单详情数据
     */
    @POST(MOBILE_TERMINAL + "/order/detail" + "?apipost_id=1de0545f759003")
    Observable<BaseResponse<WorkOrderDetailResponse>> getWorkOrderDetail(@Body RequestBody body);

    /**
     * 获取领料清单
     *
     * @param body 工单ID
     * @return 领料清单数据
     */
    @POST(MOBILE_TERMINAL + "/order/pick" + "?apipost_id=1df17504f59006")
    Observable<BaseResponse<PickMaterialResponse>> getPickMaterialList(@Body RequestBody body);

    /**
     * 工单回退
     *
     * @param body 工单ID
     * @return 回退结果
     */
    @POST(MOBILE_TERMINAL + "/order/rollback" + "?apipost_id=1e1dd967359007")
    Observable<BaseResponse<Object>> rollbackWorkOrder(@Body RequestBody body);

    /**
     * 工单作废
     *
     * @param body 工单ID
     * @return 作废结果
     */
    @POST(MOBILE_TERMINAL + "/order/cancel" + "?apipost_id=1e28b72cf59008")
    Observable<BaseResponse<Object>> cancelWorkOrder(@Body RequestBody body);
    /**
     * 工单接单
     *
     * @param body 工单ID
     * @return 接单结果
     */
    @POST(MOBILE_TERMINAL + "/order/accept" + "?apipost_id=1e28b72cf59008")
    Observable<BaseResponse<Object>> acceptWorkOrder(@Body RequestBody body);
    /**
     * 工单到场
     *
     * @param body 工单ID
     * @return 到场结果
     */
    @POST(MOBILE_TERMINAL + "/order/arrive" + "?apipost_id=1e28b72cf59008")
    Observable<BaseResponse<Object>> arriveWorkOrder(@Body RequestBody body);
    /**
     * 工单处理
     *
     * @param body 工单ID
     * @return 处理结果
     */
    @POST(MOBILE_TERMINAL + "/order/deal" + "?apipost_id=1e28b72cf59008")
    Observable<BaseResponse<Object>> dealWorkOrder(@Body RequestBody body);
    /**
     * 工单确认
     *
     * @param body 工单ID
     * @return 确认结果
     */
    @POST(MOBILE_TERMINAL + "/order/confirm" + "?apipost_id=1e28b72cf59008")
    Observable<BaseResponse<Object>> confirmWorkOrder(@Body RequestBody body);

    /**
     * 故障处置-维修规程
     * @param body 工单Id
     * @return
     */
    @POST(MOBILE_TERMINAL + "/order/repair/procedure" + "?apipost_id=3806d321f51002")
    Observable<BaseResponse<ProcedureResponse>> procedure(@Body RequestBody body);
    /**
     * 故障处置-维修确认
     * @param body 工单id content维修内容 备注note 维修流程手册路径url 维修流程procedures
     * @return
     */

    @POST(MOBILE_TERMINAL + "/order/repair/confirm" + "")
    Observable<BaseResponse<Object>> procedureConfirm(@Body RequestBody body);
    /**
     * 故障处置-通过
     * @param body id，procedureId
     * @return
     */

    @POST(MOBILE_TERMINAL + "/order/repair/procedure/status/pass" + "?apipost_id=383dd6da351005")
    Observable<BaseResponse<Object>> procedurePass(@Body RequestBody body);
    /**
     * 故障处置-通过
     * @param body id，procedureId
     * @return
     */

    @POST(MOBILE_TERMINAL + "/order/repair/procedure/status/fail" + "")
    Observable<BaseResponse<Object>> procedureFail(@Body RequestBody body);

    //#######################工单处理案例库接口######################

    /**
     * 获取案例库列表
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 案例库列表数据
     */
    @POST(RULE + "/case/list" + "?apipost_id=38009721f59002")
    Observable<BaseResponse<CaseListResponse>> getCaseList(
            @Query("pageNum") int pageNum,
            @Query("pageSize") int pageSize
    );

    /**
     * 获取案例详情
     *
     * @param id 案例ID
     * @return 案例详情数据
     */
    @POST(RULE + "/case/detail" + "?apipost_id=38efe188759002")
    Observable<BaseResponse<CaseDetailResponse>> getCaseDetail(@Query("id") String id);

    /**
     * 工单指派人员获取
     */
    @POST(RULE + "/order/assign/opt" + "?apipost_id=b3b807337f002")
    Observable<BaseResponse<MembersResponse>> assignOpt(@Body RequestBody body);

    /**
     * 工单人员指派提交
     */
    @POST(RULE + "/order/assign" + "?apipost_id=b3bc7d7f7f003")
    Observable<BaseResponse<Object>> assign(@Body RequestBody body);
//    ############################故障上报接口#############################

    /**
     * 获取设备信息  用于故障提报
     *
     * @return
     */
    @POST(RULE + "/mobileTerminal/home/<USER>" + "?apipost_id=c92577e37f005")
    Observable<BaseResponse<Map<String, Map<String, List<DeviceInfo>>>>> device();

    /**
     * 获取报警现象
     *
     * @param body
     * @return
     */
    @POST(RULE + "/mobileTerminal/home/<USER>/info" + "?apipost_id=dc49974b7f002")
    Observable<BaseResponse<List<AlarmResponse>>> alarmInfo(@Body RequestBody body);

    /**
     * 获取故障详情
     *
     * @param body
     * @return
     */
    @POST(RULE + "/mobileTerminal/home/<USER>/detail" + "?apipost_id=dd4e57977f005")
    Observable<BaseResponse<FaultDetailResponse>> faultDetail(@Body RequestBody body);

    /**
     * 提交故障
     * @param body
     * @return
     */
    @POST(RULE + "/mobileTerminal/home/<USER>/report" + "?apipost_id=11621003b7f002")
    Observable<BaseResponse<Object>> submitFault(@Body RequestBody body);
//    ############################应急检测接口#############################

    /**
     * 获取应急检测列表
     */
    @POST(RULE + "/operation/emergencyEvent/list" + "?apipost_id=116321a2f7f011")
    Observable<BaseResponse<EmergencyEventListResponse>> getEmergencyEventList(@Body RequestBody body);

    /**
     * 获取应急详情
     */
    @POST(RULE + "/operation/emergencyEvent/status" + "?apipost_id=11b4b4a6351024")
    Observable<BaseResponse<EmergencyDetailResponse>> getEmergencyEventStatus(@Body RequestBody body);

    //    ############################用户信息接口#############################

    /**
     * 获取用户信息
     * @param body userId
     * @return
     */
    @POST(RULE + "/mobileTerminal/user/userById" + "?apipost_id=26149311bd001f")
    Observable<BaseResponse<UserInfoResponse>> getUserById (@Body RequestBody body);

    /**
     *上传用户头像
     * @param body files(图像)+userId
     * @return
     */
    @Multipart
    @POST(RULE+ "/config/user/loadUserAvatar")
    Observable<BaseResponse<Object>> loadUserAvatar(@Part MultipartBody.Part files, @Part("userId") RequestBody body);

    /**
     *下载用户头像
     * @param body
     * @return
     */
    @POST(RULE + "/config/user/getUserAvatar" )
    Observable<BaseResponse<Object>> getUserAvatar (@Body RequestBody body);

    /**
     * 登录
     * @param body username+ password
     * @return
     */
    @POST(RULE + "/config/user/login"+"?apipost_id=260d254df5100f" )
    Observable<BaseResponse<LoginBean>> login (@Body RequestBody body);

    /**
     * 退出登录
     * @param
     * @return
     */
    @POST(RULE + "/config/user/logout"+"?apipost_id=260d4c99f51013")
    Observable<BaseResponse<Object>> logout ();

    /**
     * 获取当前文件路径
     * @param id 当前文件夹的id
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/file/getCurrentDirs")
    Observable<BaseResponse<CurrentDirsResponse>> getCurrentDirs(@Field("id") String id);

    /**
     * 获取当前文件夹的文件列表
     * @param dirIds 文件id路径
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/file/list")
    Observable<BaseResponse<DirsListResponse>> getDirsList(@Field("dirIds") String dirIds);
    /**
     * 获取文件标签列表
     * @param
     * @return
     */
    @POST(RULE + "/config/file/getLabelList")
    Observable<BaseResponse<LabelListResponse>> getLabelList();

    /**
     * 根据标签获取文件
     * @param labelId 标签id
     * @return
     */
    @POST(RULE + "/config/file/getFilesByLabel")
    Observable<BaseResponse<FilesByLabelResponse>> getFilesByLabel(@Field("labelId") String labelId);
    /**
     * 模糊查询文件名
     * @param fileName 文件名
     * @return
     */
    @POST(RULE + "/config/file/searchFileName")
    Observable<BaseResponse<SearchFileResponse>> searchFileName(@Field("fileName") String fileName);

    /**
     * 文件下载
     * @param url 文件url
     * @return
     */
    @POST(RULE + "/config/file/downLoad")
    Observable<BaseResponse<Object>> downLoadFile(@Field("url") String url);

}
