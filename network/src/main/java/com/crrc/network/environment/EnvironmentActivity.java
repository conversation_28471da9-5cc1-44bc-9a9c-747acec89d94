package com.crrc.network.environment;

import android.app.Application;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Process;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.PreferenceManager;

import com.crrc.network.R;



/**
 * <AUTHOR>
 * 设置网络环境
 */
public class EnvironmentActivity extends AppCompatActivity {

    /**
     * 开发环境
     */
    public static final String NETWORK_DEV = "0";

    /**
     * 生产环境
     */
    public static final String NETWORK_RELEASE = "1";

    public static final String NETWORK_ENVIRONMENT_PREF_KEY = "network_environment_type";

    private static String sCurrentNetworkEnvironment = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_environment);
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.content, new MyPreferenceFragment())
                .commit();
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        sCurrentNetworkEnvironment = prefs.getString(EnvironmentActivity.NETWORK_ENVIRONMENT_PREF_KEY, NETWORK_DEV);
    }

    public static class MyPreferenceFragment extends PreferenceFragmentCompat implements Preference.OnPreferenceChangeListener {
        @Override
        public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
            addPreferencesFromResource(R.xml.environment_preference);
            findPreference(NETWORK_ENVIRONMENT_PREF_KEY).setOnPreferenceChangeListener(this);
        }

        @Override
        public boolean onPreferenceChange(Preference preference, Object o) {
            if (!sCurrentNetworkEnvironment.equalsIgnoreCase(String.valueOf(o))) {
                Toast.makeText(getContext(), "您已经更改了网络环境，您退出当前页面的时候APP将会重启切换环境！", Toast.LENGTH_SHORT).show();
            }
            return true;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        String newValue = prefs.getString(EnvironmentActivity.NETWORK_ENVIRONMENT_PREF_KEY, NETWORK_DEV);
        if (!sCurrentNetworkEnvironment.equalsIgnoreCase(newValue)) {
            Process.killProcess(Process.myPid());
        } else {
            finish();
        }
    }

    public static boolean isOfficialEnvironment(Application application) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(application);
        String environment = prefs.getString(EnvironmentActivity.NETWORK_ENVIRONMENT_PREF_KEY, NETWORK_DEV);
        return NETWORK_DEV.equalsIgnoreCase(environment);
    }
}