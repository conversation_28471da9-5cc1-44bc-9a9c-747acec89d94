package com.crrc.network.observer;


import static com.crrc.common.Constant.RESPONSE_CODE;

import android.util.Log;

import com.crrc.common.BaseResponse;
import com.crrc.network.errorhandler.ExceptionHandle;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * 这个主要用在非Model中
 * @param <T>
 */
 public abstract class BaseObserverForBasic<T > implements Observer<T>{
    private Disposable disposable;
    @Override
    public void onSubscribe(Disposable d) {
            disposable =d;
    }

    @Override
    public void onNext(T t) {
        if (t instanceof BaseResponse){
            BaseResponse baseResponse = (BaseResponse) t;
            if (baseResponse.baseResponseCode == RESPONSE_CODE) {
                onSuccess(t);
            }else {
                Log.d("aa",baseResponse.baseResponseReason);
                onFail(new Exception(baseResponse.baseResponseReason));
            }
        }else {
            onFail(new Exception("类型不匹配"));
        }
    }
    protected abstract void onSuccess(T t);
    protected abstract void onFail(Throwable throwable);
    @Override
    public void onError(Throwable e) {
        if (e instanceof ExceptionHandle.ResponseThrowable) {
            ExceptionHandle.ResponseThrowable throwable = new ExceptionHandle.ResponseThrowable(e
                    , ((ExceptionHandle.ResponseThrowable) e).code,
                    ((ExceptionHandle.ResponseThrowable) e).message);
            Log.e("onError", "异常: code = " + throwable.code + ", message = " + throwable.message);
            onFail(throwable);
        } else {
            onFail(e);
        }
    }

    @Override
    public void onComplete() {

    }
}
