package com.crrc.network.observer;

import com.crrc.common.base.BaseModel;
import com.crrc.common.base.IBaseModelListener;
import com.crrc.common.observer.MvvmDataObserver;
import com.crrc.network.errorhandler.ExceptionHandle;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * 自定义BaseObserver
 * model页面 数据请求发起，到返回数据给MvvmDataObserver的onSuccess或者onError
 */
public class BaseObserver<T> implements Observer<T> {
    BaseModel baseModel;
    MvvmDataObserver<T> mvvmDataObserver;

    public BaseObserver(BaseModel baseModel, MvvmDataObserver<T> mvvmDataObserver) {
        this.baseModel = baseModel;
        this.mvvmDataObserver = mvvmDataObserver;
    }

    @Override
    public void onSubscribe(Disposable d) {
        if (baseModel != null) {
            baseModel.addDisposable(d);
        }
    }

    @Override
    public void onNext(T t) {
        mvvmDataObserver.onSuccess(t, false);
    }

    @Override
    public void onError(Throwable e) {
        if (e instanceof ExceptionHandle.ResponseThrowable) {
            ExceptionHandle.ResponseThrowable throwable = new ExceptionHandle.ResponseThrowable(e, ((ExceptionHandle.ResponseThrowable) e).code);
            throwable.message = ((ExceptionHandle.ResponseThrowable) e).message;
            mvvmDataObserver.onFailure(throwable);
        } else {
            mvvmDataObserver.onFailure(new ExceptionHandle.ResponseThrowable(e, ExceptionHandle.ERROR.UNKNOWN));
        }
    }

    @Override
    public void onComplete() {
    }
}
