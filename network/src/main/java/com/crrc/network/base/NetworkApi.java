package com.crrc.network.base;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import com.crrc.network.commoninterceptor.CommonRequestInterceptor;
import com.crrc.network.commoninterceptor.CommonResponseInterceptor;
import com.crrc.network.commoninterceptor.DynamicTimeoutInterceptor;
import com.crrc.network.environment.EnvironmentActivity;
import com.crrc.network.environment.IEnvironment;
import com.crrc.network.errorhandler.HttpErrorHandler;
import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.ObservableTransformer;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import okhttp3.Cache;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;


/**
 * <AUTHOR>
 * 当前NetworkApi可以被不同域名或分类请求所继承
 * 支持多域名 开发测试环境下App更改域名
 */
public abstract class NetworkApi implements IEnvironment {
    private static INetworkRequiredInfo iNetworkRequiredInfo;
    private static HashMap<String, Retrofit> retrofitHashMap = new HashMap<>();
    private String mBaseUrl;
    private OkHttpClient mOkHttpClient;
    private static final int OKHTTP_TIMEOUT = 2;

    /**
     * 默认dev环境
     */
    private static boolean mIsDev;

    public NetworkApi() {
        if (!mIsDev) {
//            mBaseUrl = getRelease();
        }
        mBaseUrl = getDev();
    }

    public static void init(INetworkRequiredInfo networkRequiredInfo) {
        iNetworkRequiredInfo = networkRequiredInfo;
        mIsDev = EnvironmentActivity.isOfficialEnvironment(networkRequiredInfo.getApplicationContext());
    }

    /**
     * 创建一个retrofit实例，并存入map
     *
     * @param service retrofit接口
     * @return 返回一个retrofit实例
     */
    protected Retrofit getRetrofit(Class service) {
        if (retrofitHashMap.get(mBaseUrl + service.getName()) != null) {
            return retrofitHashMap.get(mBaseUrl + service.getName());
        }
        Retrofit.Builder retrofitBuilder = new Retrofit.Builder();
        retrofitBuilder.baseUrl(mBaseUrl);
        retrofitBuilder.client(getOkHttpClient());
        retrofitBuilder.addConverterFactory(GsonConverterFactory.create());
        retrofitBuilder.addCallAdapterFactory(RxJava2CallAdapterFactory.create());
        Retrofit retrofit = retrofitBuilder.build();
        retrofitHashMap.put(mBaseUrl + service.getName(), retrofit);
        return retrofit;
    }

    /**
     * 创建OkHttpClient
     *
     * @return 返回一个创建好的OkHttpClient实例 内容包含缓存大小、请求拦截器、返回数据拦截器
     */
    private OkHttpClient getOkHttpClient() {
        if (mOkHttpClient == null) {
            OkHttpClient.Builder okHttpClientBuilder = new OkHttpClient.Builder()
                    .connectTimeout(OKHTTP_TIMEOUT, TimeUnit.MINUTES)
                    .readTimeout(OKHTTP_TIMEOUT, TimeUnit.MINUTES)
                    .writeTimeout(OKHTTP_TIMEOUT, TimeUnit.MINUTES)
                    ;
            if (getInterceptor() != null) {
                okHttpClientBuilder.addInterceptor(getInterceptor());
            }
            /**
             * 缓存大小10M
             */
            int cacheSize = 100 * 1024 * 1024;
            okHttpClientBuilder.cache(new Cache(iNetworkRequiredInfo.getApplicationContext().getCacheDir(), cacheSize));
            okHttpClientBuilder.addInterceptor(new CommonRequestInterceptor(iNetworkRequiredInfo));
            okHttpClientBuilder.addInterceptor(new DynamicTimeoutInterceptor());
            okHttpClientBuilder.addInterceptor(new CommonResponseInterceptor());
            if (iNetworkRequiredInfo != null && (iNetworkRequiredInfo.isDebug())) {
                HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
//                //开发或isDebug 调试取消文件上传需要设置为HEADERS,不能设置为body,否则取消文件上传会失效,其他情况可设置为body
                httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
                okHttpClientBuilder.addInterceptor(httpLoggingInterceptor);
            }
            mOkHttpClient = okHttpClientBuilder.build();
        }
        return mOkHttpClient;
    }


    /**
     * 简化使用retrofit的步骤 subscribeOn observeOn
     * 并且添加自定义异常处理器
     *
     * @param observer 接口的数据观察者
     * @param <T>      可以是任何数据观察者
     * @return 返回一个可观察对象
     */
    public <T> ObservableTransformer<T, T> applySchedulers(final Observer<T> observer) {
        return new ObservableTransformer<T, T>() {
            @Override
            public ObservableSource<T> apply(Observable<T> upstream) {
                Observable<T> observable = (Observable<T>) upstream
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .map(getAppErrorHandler())
                        .onErrorResumeNext(new HttpErrorHandler<T>());
                observable.subscribe(observer);
                return observable;
            }
        };
    }

    /**
     * 自定义针对不同域名下的拦截器 一般用于添加不同的请求头
     *
     * @return 自定义针对不同域名下的拦截器
     */
    protected abstract Interceptor getInterceptor();


    /**
     * 自定义针对不同的域名下接口的错误处理
     *
     * @return 返回自定义针对不同域名的错误处理器
     */
    protected abstract <T> Function<T, T> getAppErrorHandler();
}
