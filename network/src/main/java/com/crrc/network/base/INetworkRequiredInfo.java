package com.crrc.network.base;

import android.app.Application;


/**
 * <AUTHOR>
 */
public interface INetworkRequiredInfo {
    /**
     * 获取App版本号
     *
     * @return 获取App版本号
     */
    String getAppVersionName();

    /**
     * 获取App版本号
     *
     * @return 获取App版本号
     */
    String getAppVersionCode();

    /**
     * 是否Debug
     *
     * @return 是否Debug
     */
    boolean isDebug();

    /**
     * 返回Application
     *
     * @return 返回Application
     */
    Application getApplicationContext();
}
