package com.crrc.network.commoninterceptor;


import static com.crrc.common.Constant.AUTHORIZATION;
import static com.crrc.common.Constant.BEARER;
import static com.crrc.common.Constant.KEY_TOKEN;
import static com.crrc.common.Constant.LOGIN_TAG;
import static com.crrc.common.Constant.TOKEN_INFO;

import androidx.annotation.NonNull;

import java.io.IOException;

import com.crrc.common.bean.response.TokenIndoData;
import com.crrc.common.utils.MMkvUtil;
import com.crrc.network.base.INetworkRequiredInfo;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * <AUTHOR>
 * 网络请求数据拦截器
 */
public class CommonRequestInterceptor implements Interceptor {

    private INetworkRequiredInfo requiredInfo;

    public CommonRequestInterceptor(INetworkRequiredInfo requiredInfo) {
        this.requiredInfo = requiredInfo;
    }

    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {

        Request original = chain.request();
        Request request = original.newBuilder().build();
        Request.Builder builder = chain.request().newBuilder();
        if (!request.toString().contains(LOGIN_TAG)) {
            //String tokenStr = MMkvUtil.decodeParcelables(TOKEN_INFO, TokenIndoData.class).getAccess_token();
            String tokenStr = MMkvUtil.decodeString(KEY_TOKEN);
            builder.addHeader(AUTHORIZATION, BEARER + tokenStr);
            builder.header("User-agent","mobile");
          //  builder.addHeader("Source", "portal");
        }else {
            builder.header("User-agent","mobile");
           // builder.addHeader("Source", "mobile");
        }
        return chain.proceed(builder.build());
    }
}
