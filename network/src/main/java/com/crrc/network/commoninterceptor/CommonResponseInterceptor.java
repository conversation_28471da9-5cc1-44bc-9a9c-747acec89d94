package com.crrc.network.commoninterceptor;



import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Response;

/**
 * <AUTHOR>
 * 自定义网络请求返回数据拦截器
 */
public class CommonResponseInterceptor implements Interceptor {


    @Override
    public Response intercept(Chain chain) throws IOException {
        long requestTime = System.currentTimeMillis();
        Response response = chain.proceed(chain.request());
        return response;
    }
}
