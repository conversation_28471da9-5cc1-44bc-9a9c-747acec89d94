package com.crrc.network.errorhandler;

import static com.crrc.common.Constant.CERTIFICATE_VERIFICATION_FAILED;
import static com.crrc.common.Constant.CONNECTION_FAILED;
import static com.crrc.common.Constant.CONNECTION_TIMED_OUT;
import static com.crrc.common.Constant.PARSE_ERROR;
import static com.crrc.common.Constant.UNKNOWN_MISTAKE;

import android.net.ParseException;

import com.google.gson.JsonParseException;

import org.apache.http.conn.ConnectTimeoutException;
import org.json.JSONException;

import java.io.IOException;
import java.net.ConnectException;

import com.crrc.common.R;
import com.crrc.common.BaseApplication;
import com.crrc.common.BaseResponse;
import com.crrc.common.bean.response.EvenBusGoLoginData;
import com.crrc.common.utils.EventBusUtils;
import com.crrc.common.utils.GsonUtil;
import okhttp3.ResponseBody;
import retrofit2.HttpException;

/**
 * <AUTHOR>
 */
public class ExceptionHandle {

    public static boolean HTTP_404ED = false;
    private static final int UNAUTHORIZED = 401;
    private static final int FORBIDDEN = 403;
    private static final int NOT_FOUND = 404;
    private static final int REQUEST_TIMEOUT = 408;
    private static final int INTERNAL_SERVER_ERROR = 500;
    private static final int BAD_GATEWAY = 502;
    private static final int SERVICE_UNAVAILABLE = 503;
    private static final int GATEWAY_TIMEOUT = 504;
    private static BaseResponse baseResponse;

    public static ResponseThrowable handleException(Throwable e) {
        ResponseThrowable ex;
        if (e instanceof HttpException) {
            HttpException httpException = (HttpException) e;
            ex = new ResponseThrowable(e, ERROR.PARSE_ERROR);
            switch (httpException.code()) {
                case UNAUTHORIZED:
                    ex.message = BaseApplication.sApplication.getString(R.string.login_timed_out);
                    if (!HTTP_404ED) {
                        EvenBusGoLoginData evenBusGoLoginData = new EvenBusGoLoginData("");
                        EventBusUtils.post(evenBusGoLoginData);
                        HTTP_404ED = true;
                    }
                    break;
                case FORBIDDEN:
                case NOT_FOUND:
                case REQUEST_TIMEOUT:
                case GATEWAY_TIMEOUT:
                case INTERNAL_SERVER_ERROR:
                case BAD_GATEWAY:
                case SERVICE_UNAVAILABLE:
                default:
                    ex.message = "";
                    ResponseBody responseBody = httpException.response().errorBody();
                    if (responseBody != null) {
                        try {
                            baseResponse = GsonUtil.GsonToBean(responseBody.string(), BaseResponse.class);
                            if (baseResponse != null) {
                                ex.message =  baseResponse.baseResponseReason ;
                            } else {
                                ex.message = ((HttpException) e).code() + " " + ((HttpException) e).message() ;
                            }
                        } catch (IOException ioException) {
                            ioException.printStackTrace();
                        }
                    }
                    break;
            }
            return ex;
        } else if (e instanceof ServerException) {
            ServerException resultException = (ServerException) e;
            ex = new ResponseThrowable(resultException, resultException.code);
            ex.message = resultException.message;
            return ex;
        } else if (e instanceof JsonParseException
                || e instanceof JSONException
                || e instanceof ParseException) {
            ex = new ResponseThrowable(e, ERROR.PARSE_ERROR);
            ex.message = PARSE_ERROR;
            return ex;
        } else if (e instanceof ConnectException) {
            ex = new ResponseThrowable(e, ERROR.NETWORK_ERROR);
            ex.message = CONNECTION_FAILED;
            return ex;
        } else if (e instanceof javax.net.ssl.SSLHandshakeException) {
            ex = new ResponseThrowable(e, ERROR.SSL_ERROR);
            ex.message = CERTIFICATE_VERIFICATION_FAILED;
            return ex;
        } else if (e instanceof ConnectTimeoutException) {
            ex = new ResponseThrowable(e, ERROR.TIMEOUT_ERROR);
            ex.message = CONNECTION_TIMED_OUT;
            return ex;
        } else if (e instanceof java.net.SocketTimeoutException) {
            ex = new ResponseThrowable(e, ERROR.TIMEOUT_ERROR);
            ex.message = CONNECTION_TIMED_OUT;
            return ex;
        } else {
            ex = new ResponseThrowable(e, ERROR.UNKNOWN);
            ex.message = UNKNOWN_MISTAKE;
            return ex;
        }
    }


    /**
     * 约定异常
     */
    public static class ERROR {
        /**
         * 未知错误
         */
        public static final int UNKNOWN = 1000;
        /**
         * 解析错误
         */
        public static final int PARSE_ERROR = 1001;
        /**
         * 网络错误
         */
        public static final int NETWORK_ERROR = 1002;
        /**
         * 协议出错
         */
        public static final int HTTP_ERROR = 1003;

        /**
         * 证书出错
         */
        public static final int SSL_ERROR = 1005;

        /**
         * 连接超时
         */
        public static final int TIMEOUT_ERROR = 1006;
    }

    public static class ResponseThrowable extends Exception {
        public int code;
        public String message;

        public ResponseThrowable(Throwable throwable, int code) {
            super(throwable);
            this.code = code;
        }
        public ResponseThrowable(Throwable cause, int code, String message) {
            super(message, cause);
            this.code = code;
            this.message = message;
        }
        @Override
        public String getMessage() {
            return message != null ? message : super.getMessage();
        }
    }

    public static class ServerException extends RuntimeException {
        public int code;
        public String message;
    }
}

