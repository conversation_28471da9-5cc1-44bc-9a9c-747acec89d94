plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

android {
    namespace 'com.crrc.siom'
    compileSdk 35
    defaultConfig {
        applicationId "com.crrc.siom"
        minSdk 28
        targetSdk 35
        versionCode 1
        versionName "1.0"
        ndk {
            //设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
            abiFilters "armeabi", "armeabi-v7a", "arm64-v8a"
        }
    }
    signingConfigs {
        release {
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }
    buildTypes {

        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        viewBinding true
        compose true
        buildConfig true
    }

    composeOptions {
        kotlinCompilerExtensionVersion "1.5.1"
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
        debug {
            java.srcDirs = ['src/main/java', 'build/generated/data_binding_base_class_source_out/debug/out']
        }
    }


    android.applicationVariants.configureEach { variant ->
        variant.outputs.configureEach {
            if ('debug' == variant.buildType.name) {

            } else {
                outputFileName = "SIOM${variant.flavorName}v${variant.versionCode}-${releaseTime()}.apk"
            }

        }
    }
}


def static releaseTime() {
    return new Date().format("yyyy-MM-dd_HH-mm")
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    androidTestImplementation libs.androidx.espresso.core
    implementation project(':network')
    implementation project(':common')
    implementation libs.xcrash.android.lib
    implementation "androidx.media3:media3-exoplayer:1.2.0"
    implementation "androidx.media3:media3-ui:1.2.0"
    implementation 'com.guolindev.permissionx:permissionx:1.8.1'

    // ZXing 二维码扫描库
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation "androidx.work:work-runtime-ktx:2.9.0"
    //饼图
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
}