<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.crrc.siom">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.NFC"/>
    <!--允许程序访问WiFi网络信息-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!--允许程序访问CellID或WiFi热点来获取粗略的位置-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- 相机权限，用于扫描二维码 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
    <!-- 特殊权限 -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" 
        android:maxSdkVersion="22"/>
    <application
        android:name=".AppApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:networkSecurityConfig="@xml/network_security_config"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SIOM"
        tools:targetApi="31" >
        <meta-data android:name="com.amap.api.v2.apikey" android:value="a961c5307db4b0f5edbd0807e4e40376"/>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:exported="false" />
        <activity
            android:name=".ui.test.ArticleListActivity"
            android:exported="false" />
        <activity
            android:name=".ui.test.TestActivity"
            android:exported="false" />
        <activity
            android:name=".ui.workorder.WorkOrderActivity"
            android:exported="false" />
        <activity
            android:name=".ui.workorder.WorkOrderDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.workorder.PickMaterialActivity"
            android:exported="false" />
        <activity
            android:name=".ui.emergency.EmergencyMonitorActivity"
            android:exported="false" />
        <activity
            android:name=".ui.emergency.EmergencyDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.workorder.CreateTempWorkOrderActivity"
            android:exported="false" />
        <activity
            android:name=".ui.workorder.ProcessWorkOrderActivity"
            android:exported="false" />
        <activity
            android:name=".ui.workorder.pages.process.VideoPlayerActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden" />
        <activity
            android:name=".ui.document.DocumentManageActivity"
            android:exported="false" />
        <activity
            android:name=".ui.document.DocumentPreviewActivity"
            android:exported="false" />
        <activity
            android:name=".ui.dashboard.detail.ChatDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.submitFault.SubmitFaultActivity"
            android:exported="false" />
        <activity
            android:name=".ui.line.LineMapJavaActivity"
            android:exported="false" />
        <activity
            android:name=".ui.qrcode.QRScannerActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.device.DeviceDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.caselibrary.CaseLibraryActivity"
            android:exported="false" />
        <activity
            android:name=".ui.profile.StatisticsDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.dashboard.userinfo.UserInfoActivity"
            android:exported="false" />
        <activity
            android:name=".ui.dashboard.groupInfo.GroupInfoActivity"
            android:exported="false" />
        <service
            android:name=".service.WebSocketService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />
    </application>

</manifest>