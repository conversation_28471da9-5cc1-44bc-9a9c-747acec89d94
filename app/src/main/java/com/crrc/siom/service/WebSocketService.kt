package com.crrc.siom.service

import android.R
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkRequest
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.model.ChatMessage
import com.crrc.siom.data.model.GroupChatMessage
import com.crrc.siom.data.model.HistoryMessage
import com.crrc.siom.data.model.NotificationMessage
import com.crrc.siom.data.model.ParticipantListMessageResponse
import com.crrc.siom.manager.ChatMessageCenter
import com.crrc.siom.manager.WebSocketManager
import com.crrc.siom.manager.WebSocketManagerListener
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import android.os.Process
import android.app.ActivityManager
import com.crrc.common.utils.PrefsUtil
import com.crrc.common.utils.ToastUtil
import com.crrc.siom.AppApplication
import com.crrc.siom.data.model.HistoryMessageResponse
import com.crrc.siom.data.model.NotificationDelMessage
import com.crrc.siom.data.model.NotificationListMessage
import com.crrc.siom.data.model.NotificationReadMessage
import com.crrc.siom.data.model.ParticipantListMessage
import com.crrc.siom.data.model.ReceiveMessage

/**
 * ChatService: 前台服务，托管 WebSocketManager，实现保活和自动重连
 */
class WebSocketService : Service() {
    private lateinit var wsManager: WebSocketManager
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val binder = ChatBinder()
    private val channelId = "chat_service_channel"
    private val notificationId = 1001
    private var isRegistered = false
    private var TAG: String = "MessageService";

    override fun onCreate() {
        super.onCreate()
        startForegroundServiceWithNotification()
        printProcessInfo(this,"ServiceProcess")
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onDestroy() {
        super.onDestroy()
        wsManager.release()
        unregisterNetworkCallback()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val userId = SessionManager().getUserId()
        Log.d(TAG, "onStartCommand: $userId")
        val ipAddress = PrefsUtil.getInstance(AppApplication.sApplication).baseUrl ?: ""
        val wsUrl = if (ipAddress.startsWith("http://")) {
            "ws://" + ipAddress.removePrefix("http://").substringBefore(":") + ":9114/"
        }  else {
            "ws://$ipAddress:9114/"
        }

//        val wsUrl = "ws://**************:9001/"
        wsManager = WebSocketManager.getInstance(wsUrl, object : WebSocketManagerListener {
            override fun onConnected() {
                isRegistered = false
                sendRegister(userId.toString())
            }

            override fun onDisconnected(reason: String) {
                isRegistered = false
                ToastUtil.show("断开链接")
            }

            override fun onMessage(message: String) {
                val gson = Gson()
                val jsonObj = try {
                    gson.fromJson(message, JsonObject::class.java)
                } catch (e: Exception) {
                    null
                }
                Log.d(TAG,jsonObj.toString())
                if (jsonObj != null) {
                    val type = if (jsonObj.has("type")) jsonObj.get("type").asString else "UNKNOWN"
                    when (type) {
                        "REGISTER_SUCCESS" -> {
                            val respUserId = jsonObj.get("userId")?.asString ?: ""
                            Log.d(TAG, "onMessage: $respUserId")
                            Log.d(TAG, "onMessage: $userId")
                            isRegistered = if (respUserId == userId) {
                                Handler(Looper.getMainLooper()).post {
                                    Toast.makeText(
                                        applicationContext,
                                        "注册成功",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                                true
                            } else {
                                Handler(Looper.getMainLooper()).post {
                                    Toast.makeText(
                                        applicationContext,
                                        "注册失败",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                                false
                            }
                        }

                        else -> {
                            if (!isRegistered) {
                                return
                            }
                            when (type) {
                                "HEARTBEAT_RESPONSE" -> {
                                    // 处理心跳响应
                                }
                                "NOTICE"  ->{
                                    val notificationMsg = gson.fromJson(jsonObj, NotificationMessage::class.java)
                                    ChatMessageCenter.emitNewMessage(notificationMsg)
                                }
                                "SINGLE_SENDING" -> {
                                    val chatMessage = gson.fromJson(jsonObj, ChatMessage::class.java)
                                    ChatMessageCenter.emitNewMessage(chatMessage)
                                }
                                "GROUP_SENDING" -> {
                                    val groupMessage = gson.fromJson(jsonObj, GroupChatMessage::class.java)
                                    ChatMessageCenter.emitNewMessage(groupMessage)
                                }
                                "PARTICIPANT_LIST_UPDATE" ->{
                                    val participantListMessage = gson.fromJson(jsonObj, ParticipantListMessageResponse::class.java)
                                    ChatMessageCenter.emitNewMessage(participantListMessage)
                                }
                                "HISTORY_MESSAGE_RESPONSE" -> {
                                    val historyMessage = gson.fromJson(jsonObj, HistoryMessageResponse::class.java)
                                    ChatMessageCenter.emitNewMessage(historyMessage)
                                }
                                "FORCE_LOGOUT" -> {
                                    // 处理强制下线
                                }
                                else -> {
                                }
                            }
                        }
                    }
                }
            }

            override fun onError(t: Throwable) {
                isRegistered = false
            }
        })
        registerNetworkCallback()
//        wsManager.connect()
        return super.onStartCommand(intent, flags, startId)
    }




    /**
     * Binder，供外部调用
     */
    inner class ChatBinder : Binder() {
        fun connect() = wsManager.connect()
        fun disconnect() = wsManager.disconnect()
        fun sendMessage(msg: String) = if (isRegistered) wsManager.sendMessage(msg) else false
        fun isConnected(): Boolean = wsManager.sendMessage("") // 简单判断
        fun sendSingleMessage(
            chatMessage: ChatMessage,
        ): Boolean {
            val json = Gson().toJson(chatMessage)
            Log.d(TAG, "sendSingleMessage: $json")
            return if (isRegistered) wsManager.sendMessage(json) else false
        }
        fun sendHistoryMessage(
            historyMessage: HistoryMessage,
        ): Boolean {
            val json = Gson().toJson(historyMessage)
            Log.d(TAG, "sendHistoryMessage: $json")
            return if (isRegistered) wsManager.sendMessage(json) else false
        }
        fun sendReceiveMessage(
            receiveMessage: ReceiveMessage,
        ): Boolean {
            val json = Gson().toJson(receiveMessage)
            Log.d(TAG, "ReceiveMessage: $json")
            return if (isRegistered) wsManager.sendMessage(json) else false
        }
        fun sendParticipantListMessage(
            participantListMessage: ParticipantListMessage,
        ): Boolean {
            val json = Gson().toJson(participantListMessage)
            Log.d(TAG, "GET_PARTICIPANT_LIST: $json")
            return if (isRegistered) wsManager.sendMessage(json) else false
        }
        fun sendNotificationHistoryList(
            notificationListMessage: NotificationListMessage,
        ): Boolean {
            val json = Gson().toJson(notificationListMessage)
            Log.d(TAG, "GET_UNREAD_NOTIFICATIONS: $json")
            return if (isRegistered) wsManager.sendMessage(json) else false
        }
        fun sendNotificationRead(
            notificationReadMessage: NotificationReadMessage,
        ): Boolean {
            val json = Gson().toJson(notificationReadMessage)
            Log.d(TAG, "NOTICE_READ: $json")
            return if (isRegistered) wsManager.sendMessage(json) else false
        }
        fun delNotification(
            notificationDelMessage: NotificationDelMessage,
        ): Boolean {
            val json = Gson().toJson(notificationDelMessage)
            Log.d(TAG, "NOTICE_READ: $json")
            return if (isRegistered) wsManager.sendMessage(json) else false
        }
    }

    /**
     * 启动前台通知
     */
    private fun startForegroundServiceWithNotification() {
        createNotificationChannel()
        val notification: Notification = NotificationCompat.Builder(this, channelId)
            .setContentTitle("聊天服务运行中")
            .setContentText("正在保持与服务器的连接…")
            .setSmallIcon(R.drawable.stat_notify_chat)
            .setOngoing(true)
            .build()
        startForeground(notificationId, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "聊天服务",
                NotificationManager.IMPORTANCE_LOW
            )
            val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
    }

    /**
     * 网络变化监听，断网/恢复自动重连
     */
    private var connectivityManager: ConnectivityManager? = null
    private var networkCallback: ConnectivityManager.NetworkCallback? = null

    private fun registerNetworkCallback() {
        connectivityManager = getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        val request = NetworkRequest.Builder().build()
        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                serviceScope.launch { wsManager.connect() }
            }
            override fun onLost(network: Network) {
                serviceScope.launch { wsManager.disconnect() }
            }
        }
        connectivityManager?.registerNetworkCallback(request, networkCallback!!)
    }

    private fun unregisterNetworkCallback() {
        networkCallback?.let {
            connectivityManager?.unregisterNetworkCallback(it)
        }
    }

    /**
     * 发送注册/登录消息
     */
    private fun sendRegister(userId: String) {
        val map = mutableMapOf<String, Any>(
            "type" to "REGISTER",
            "userId" to userId
        )
        val json = Gson().toJson(map)
        wsManager.sendMessage(json)
    }

    companion object {
        fun start(context: Context) {
            val intent = Intent(context, WebSocketService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        fun stop(context: Context) {
            val intent = Intent(context, WebSocketService::class.java)
            context.stopService(intent)
        }
    }

}

fun printProcessInfo(context: Context, tag: String = "ProcessTest") {
    val pid = Process.myPid()
    val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    val processName = manager.runningAppProcesses.firstOrNull { it.pid == pid }?.processName
    Log.d(tag, "pid=$pid, processName=$processName")
}