package com.crrc.siom

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.crrc.siom.ui.dashboard.DashboardScreen
import com.crrc.siom.ui.home.HomeScreen
import com.crrc.siom.ui.notifications.NotificationsScreen
import com.crrc.siom.ui.profile.ProfileScreen
import com.crrc.siom.ui.qrcode.QRScannerActivity
import com.crrc.siom.ui.theme.SiomTheme
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.ui.graphics.Color
import com.crrc.siom.ui.device.DeviceDetailActivity
import com.crrc.siom.ui.theme.PrimaryDark
import com.crrc.siom.service.WebSocketService
import com.crrc.siom.manager.ChatKeepAliveWorker
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.crrc.common.Constant.QR_FLAG
import java.util.concurrent.TimeUnit


class MainActivity : ComponentActivity() {
    // 二维码扫描结果处理
    private val qrScannerResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val qrCodeResult = result.data?.getStringExtra(QRScannerActivity.RESULT_QR_CODE)
            qrCodeResult?.let { code ->
                handleQRCodeResult(code)
            }
        }
    }
    private fun handleQRCodeResult(result: String) {
//        Toast.makeText(this, "扫描结果: $result", Toast.LENGTH_LONG).show()
        DeviceDetailActivity.start(this,result,QR_FLAG)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SiomTheme {
                MainScreen()
            }
        }

        WebSocketService.start(AppApplication.sApplication)
        //启动保活 Worker（每15分钟检查一次）
        val workRequest = PeriodicWorkRequestBuilder<ChatKeepAliveWorker>(15, TimeUnit.MINUTES)
            .build()
        WorkManager.getInstance(AppApplication.sApplication).enqueueUniquePeriodicWork(
            "chat_keep_alive",
            androidx.work.ExistingPeriodicWorkPolicy.KEEP,
            workRequest
        )
    }

    // QRScannerActivity结果处理
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == QRScannerActivity.REQUEST_SCAN_QR_CODE && resultCode == Activity.RESULT_OK) {
            val qrCodeResult = data?.getStringExtra(QRScannerActivity.RESULT_QR_CODE)
            qrCodeResult?.let { code ->
                handleQRCodeResult(code)
            }
        }
    }

    companion object {
        // 启动二维码扫描器
        fun startQRScanner(activity: Activity) {
            if (activity is MainActivity) {
                val intent = Intent(activity, QRScannerActivity::class.java)
                activity.qrScannerResultLauncher.launch(intent)
            } else {
                QRScannerActivity.start(activity)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen() {
    val navController = rememberNavController()
    val items = listOf(
        Screen.Home,
        Screen.Dashboard,
        Screen.Notifications,
        Screen.Profile
    )

    Scaffold(
        bottomBar = {
            NavigationBar(
                containerColor = MaterialTheme.colorScheme.primaryContainer  // 白色背景
            ) {
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                val currentDestination = navBackStackEntry?.destination
                items.forEach { screen ->
                    val isSelected = currentDestination?.hierarchy?.any { it.route == screen.route } == true
                    
                    NavigationBarItem(
                        icon = { 
                            Icon(
                                screen.icon, 
                                contentDescription = null,
                                tint = if (isSelected) 
                                       PrimaryDark  
                                       else 
                                       MaterialTheme.colorScheme.onSurfaceVariant  // 未选中时使用灰色
                            ) 
                        },
                        label = { 
                            Text(
                                text = screen.label,
                                color = if (isSelected) 
                                       PrimaryDark  
                                       else 
                                       MaterialTheme.colorScheme.onSurfaceVariant  // 未选中时使用灰色
                            ) 
                        },
                        selected = isSelected,
                        colors = NavigationBarItemDefaults.colors(
                            selectedIconColor = PrimaryDark,  // 选中时图标使用深绿色
                            unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,  // 未选中时图标使用灰色
                            selectedTextColor = PrimaryDark,  // 选中时文本使用深绿色
                            unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,  // 未选中时文本使用灰色
                            indicatorColor = Color.Transparent 
                        ),
                        onClick = {
                            navController.navigate(screen.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.Home.route,
            modifier = Modifier.padding(innerPadding),
            enterTransition = { 
                slideIntoContainer(
                    AnimatedContentTransitionScope.SlideDirection.Left,
                    animationSpec = tween(200)
                ) + fadeIn(animationSpec = tween(200))
            },
            exitTransition = {
                slideOutOfContainer(
                    AnimatedContentTransitionScope.SlideDirection.Left,
                    animationSpec = tween(200)
                ) + fadeOut(animationSpec = tween(200))
            },
            popEnterTransition = {
                slideIntoContainer(
                    AnimatedContentTransitionScope.SlideDirection.Right,
                    animationSpec = tween(200)
                ) + fadeIn(animationSpec = tween(200))
            },
            popExitTransition = {
                slideOutOfContainer(
                    AnimatedContentTransitionScope.SlideDirection.Right,
                    animationSpec = tween(200)
                ) + fadeOut(animationSpec = tween(200))
            }
        ) {
            composable(Screen.Home.route) { HomeScreen() }
            composable(Screen.Dashboard.route) { DashboardScreen() }
            composable(Screen.Notifications.route) { NotificationsScreen() }
            composable(Screen.Profile.route) { ProfileScreen() }
        }
    }
}

sealed class Screen(
    val route: String,
    val label: String,
    val icon: ImageVector
) {
    object Home : Screen("home", "首页", Icons.Default.Home)
    object Dashboard : Screen("dashboard", "通讯录", Icons.Default.Person)
    object Notifications : Screen("notifications", "通知", Icons.Default.Notifications)
    object Profile : Screen("profile", "我的", Icons.Default.AccountCircle)
}