package com.crrc.siom.ui.line;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

public class StationDetailAdapter extends FragmentStateAdapter {

    private final String stationName;
    private AlarmFragment alarmFragment;
    private StorageFragment storageFragment;

    public StationDetailAdapter(FragmentActivity fragmentActivity, String stationName) {
        super(fragmentActivity);
        this.stationName = stationName;
        this.alarmFragment = AlarmFragment.newInstance(stationName);
        this.storageFragment = StorageFragment.newInstance(stationName);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return alarmFragment;
            case 1:
                return storageFragment;
            default:
                return alarmFragment;
        }
    }

    @Override
    public int getItemCount() {
        return 2;
    }

    public AlarmFragment getAlarmFragment() {
        if (alarmFragment == null) {
            alarmFragment = AlarmFragment.newInstance(stationName);
        }
        return alarmFragment;
    }

    public StorageFragment getStorageFragment() {
        if (storageFragment == null) {
            storageFragment = StorageFragment.newInstance(stationName);
        }
        return storageFragment;
    }
} 