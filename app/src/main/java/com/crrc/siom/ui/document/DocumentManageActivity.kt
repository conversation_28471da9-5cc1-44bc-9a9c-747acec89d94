package com.crrc.siom.ui.document

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material3.ExperimentalMaterial3Api
import com.crrc.siom.ui.theme.SiomTheme

class DocumentManageActivity : ComponentActivity() {
    companion object {
        fun start(context: Context, dirId: String) {
            context.startActivity(
                Intent(context, DocumentManageActivity::class.java)
                    .apply {
                        putExtra("dirId", dirId)
                    })
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        val dirId = intent.getStringExtra("dirId") ?: ""
        super.onCreate(savedInstanceState)
        setContent {
            SiomTheme {
                DocumentManageScreen(dirId)
            }
        }
    }
} 