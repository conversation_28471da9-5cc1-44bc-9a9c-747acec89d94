package com.crrc.siom.ui.document

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.widget.ImageView
import android.widget.MediaController
import android.widget.Toast
import android.widget.VideoView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import coil.load
import java.io.File

class DocumentPreviewActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val filePath = intent.getStringExtra(EXTRA_FILE_PATH)
        val type = intent.getStringExtra(EXTRA_TYPE)
        if (filePath.isNullOrEmpty() || type.isNullOrEmpty()) {
            Toast.makeText(this, "预览文件参数错误", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        val file = File(filePath)
        if (!file.exists()) {
            Toast.makeText(this, "文件不存在", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        window.statusBarColor = Color.BLACK
        if (isImage(type)) {
            val imageView = ImageView(this)
            imageView.setBackgroundColor(Color.BLACK)
            imageView.scaleType = ImageView.ScaleType.FIT_CENTER
            val uri = FileProvider.getUriForFile(this, "$packageName.fileprovider", file)
            imageView.load(uri)
            setContentView(imageView)
        } else if (isVideo(type)) {
            val videoView = VideoView(this)
            val uri = FileProvider.getUriForFile(this, "$packageName.fileprovider", file)
            videoView.setVideoURI(uri)
            videoView.setMediaController(MediaController(this).apply { setAnchorView(videoView) })
            videoView.setOnPreparedListener { it.isLooping = true; videoView.start() }
            setContentView(videoView)
        } else {
            Toast.makeText(this, "不支持的预览类型", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    companion object {
        private const val EXTRA_FILE_PATH = "extra_file_path"
        private const val EXTRA_TYPE = "extra_type"
        fun start(context: Context, file: File, type: String) {
            val intent = Intent(context, DocumentPreviewActivity::class.java)
            intent.putExtra(EXTRA_FILE_PATH, file.absolutePath)
            intent.putExtra(EXTRA_TYPE, type)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            context.startActivity(intent)
        }
        fun isImage(type: String): Boolean {
            val imageTypes = listOf("jpg", "jpeg", "png", "bmp", "gif", "webp")
            return imageTypes.contains(type.lowercase())
        }
        fun isVideo(type: String): Boolean {
            val videoTypes = listOf("mp4", "avi", "mov", "mkv", "3gp")
            return videoTypes.contains(type.lowercase())
        }
    }
} 