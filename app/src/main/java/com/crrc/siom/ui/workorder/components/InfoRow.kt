package com.crrc.siom.ui.workorder.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp

/**
 * 信息行组件
 * 
 * 用于显示列表页键值对形式的信息
 * 
 * @param label 标签
 * @param value 值
 * @param labelStyle 标签文本样式
 * @param valueStyle 值文本样式
 * @param labelWidth 标签宽度
 */
@Composable
fun InfoRow(
    label: String, 
    value: String,
    labelStyle: TextStyle = MaterialTheme.typography.bodySmall.copy(
        color = MaterialTheme.colorScheme.onSurfaceVariant
    ),
    valueStyle: TextStyle = MaterialTheme.typography.bodySmall,
    labelWidth: Modifier = Modifier.width(60.dp)
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
    ) {
        Text(
            text = label,
            style = labelStyle,
            modifier = labelWidth
        )
        Text(
            text = value,
            style = valueStyle
        )
    }
} 