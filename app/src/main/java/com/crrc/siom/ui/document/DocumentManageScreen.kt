package com.crrc.siom.ui.document

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Bookmark
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.crrc.common.bean.response.DocumentRecord
import com.crrc.siom.ui.components.SearchField
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.LabelListResponse
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.widget.Toast
import androidx.core.content.FileProvider
import com.crrc.common.utils.PrefsUtil
import com.crrc.siom.AppApplication
import com.crrc.siom.utils.DownloadUtil
import java.io.File
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import android.Manifest
import android.util.Log

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DocumentManageScreen(
    dirId: String? = null,
    viewModel: DocumentManageViewModel = viewModel(),
) {
    val documents by viewModel.documents.collectAsState()
    val currentPath by viewModel.currentPath.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val isSidebarOpen by viewModel.isSidebarOpen.collectAsState()
    val tags by viewModel.tags.collectAsState()
    val selectedTag by viewModel.selectedTag.collectAsState()
    val context = LocalContext.current

    val drawerState = rememberDrawerState(
        initialValue = if (isSidebarOpen) DrawerValue.Open else DrawerValue.Closed
    )
    
    // 同步抽屉状态和视图模型中的状态
    LaunchedEffect(isSidebarOpen) {
        if (isSidebarOpen) {
            drawerState.open()
        } else {
            drawerState.close()
        }
    }
    LaunchedEffect(dirId) {
        if (dirId.isNullOrEmpty()) return@LaunchedEffect
        viewModel.enterFileDirectory(dirId.toString())
    }

    // 动态申请通知权限（仅Android 13+）
    val notificationPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = { granted ->
            if (!granted) {
                Toast.makeText(context, "未授予通知权限，下载进度可能无法显示", Toast.LENGTH_SHORT).show()
            }
        }
    )
    LaunchedEffect(Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            notificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }
    }

    // 使用 ModalNavigationDrawer 来实现侧边栏
    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            DocumentSidebar(
                tags = tags,
                selectedTag = selectedTag,
                onTagSelected = {
                    viewModel.selectTag(it)
                    viewModel.toggleSidebar()
                }
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            // 顶部栏
            TopAppBar(
                title = { Text("文档管理") },
                navigationIcon = {
                    IconButton(onClick = {
                        if (!viewModel.goBackDirectory()){
                            (context as? Activity)?.finish()
                        }
                    }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding( vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = { viewModel.toggleSidebar() }) {
                    Icon(
                        imageVector = Icons.Default.Menu,
                        contentDescription = "展开侧边栏"
                    )
                }
                
                // 搜索框
                SearchField(
                    value = searchQuery,
                    onValueChange = { viewModel.updateSearchQuery(it) },
                    onSearch = { /* TODO: 实现搜索功能 */ },
                    modifier = Modifier.weight(1f).padding(end = 16.dp)
                )
            }

            // 当前路径
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = selectedTag?.let { "标签：${it.description ?: it.id}" } ?: "当前位置：$currentPath",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                // 文档列表
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        count = documents.size,
                        key = { index -> documents[index].id }
                    ) { index ->
                        DocumentItem(
                            document = documents[index],
                            onClick = {
                                if (documents[index].isDir) {
                                    viewModel.enterDirectory(documents[index].id)
                                } else {
                                    val ipAddress = PrefsUtil.getInstance(AppApplication.sApplication).baseUrl ?: ""
                                    val fileName = documents[index].name + (documents[index].suffix?.let { "." + it } ?: "")
                                    val fileUrl = "${ipAddress}config/file/mobile/downLoad?id=${documents[index].id}"
                                    val file = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), fileName)
                                    if (isImage(documents[index].suffix) || isVideo(documents[index].suffix)) {
                                        DocumentPreviewActivity.start(context, file, documents[index].suffix)
                                    } else {
                                        // 其它类型
                                        if (file.exists()) {
                                            openFileWithIntent(context, file, getMimeType(fileName))
                                        } else {
                                            val downloadId = DownloadUtil.downloadFile(context, fileUrl, fileName)
                                            if (downloadId != -1L) {
                                                DownloadUtil.registerDownloadReceiver(context, downloadId, fileName) { downloadedFile ->
                                                    if (downloadedFile != null) {
                                                        Toast.makeText(context, "下载成功", Toast.LENGTH_SHORT).show()
                                                        openFileWithIntent(context, downloadedFile, getMimeType(downloadedFile.name))
                                                    } else {
                                                        Toast.makeText(context, "下载失败", Toast.LENGTH_SHORT).show()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DocumentItem(
    document: DocumentRecord,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图标
        Icon(
            imageVector = if (document.type == "dir")
                Icons.Default.Folder else Icons.Default.Description,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 文件信息
        Column {
            Text(
                text = document.name,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = document.putTime,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun DocumentSidebar(
    tags: List<LabelListResponse>,
    selectedTag: LabelListResponse?,
    onTagSelected: (LabelListResponse?) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .width(250.dp)
            .background(MaterialTheme.colorScheme.surface)
            .padding(16.dp)
    ) {
        // 标签标题
        Text(
            text = "文件标签",
            style = MaterialTheme.typography.titleLarge,
            modifier = Modifier.padding(vertical = 16.dp)
        )
        // 标签列表
        LazyColumn {
            items(tags.size) { index ->
                val tag = tags[index]
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onTagSelected(tag) }
                        .padding(vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 标签图标
                    Icon(
                        imageVector = Icons.Default.Bookmark,
                        contentDescription = null,
                        tint = if (tag == selectedTag) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.onSurface
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    // 标签名称
                    Text(
                        text = tag.description ?: tag.id,
                        style = MaterialTheme.typography.bodyLarge,
                        color = if (tag == selectedTag) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
    }
}

fun isImage(type: String): Boolean {
    val imageTypes = listOf("jpg", "jpeg", "png", "bmp", "gif", "webp")
    return imageTypes.contains(type.lowercase())
}

fun isVideo(type: String): Boolean {
    val videoTypes = listOf("mp4", "avi", "mov", "mkv", "3gp")
    return videoTypes.contains(type.lowercase())
}

fun getMimeType(fileName: String): String {
    return when (fileName.substringAfterLast('.', "").lowercase()) {
        "pdf" -> "application/pdf"
        "doc", "docx" -> "application/msword"
        "xls", "xlsx" -> "application/vnd.ms-excel"
        "ppt", "pptx" -> "application/vnd.ms-powerpoint"
        "txt" -> "text/plain"
        "jpg", "jpeg", "png", "bmp", "gif", "webp" -> "image/*"
        "mp4", "avi", "mov", "mkv", "3gp" -> "video/*"
        else -> "*/*"
    }
}

fun openFileWithIntent(context: Context, file: File, mimeType: String) {
    val uri: Uri = FileProvider.getUriForFile(context, context.packageName + ".fileprovider", file)
    val intent = Intent(Intent.ACTION_VIEW)
    intent.setDataAndType(uri, mimeType)
    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
    try {
        context.startActivity(intent)
    } catch (e: Exception) {
        Toast.makeText(context, "未找到可打开此文件的应用", Toast.LENGTH_SHORT).show()
    }
}


