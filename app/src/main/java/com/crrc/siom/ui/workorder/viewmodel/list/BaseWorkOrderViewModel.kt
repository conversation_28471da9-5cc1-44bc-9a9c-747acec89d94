package com.crrc.siom.ui.workorder.viewmodel.list

import kotlinx.coroutines.flow.StateFlow

/**
 * 工单ViewModel基础接口
 */
interface BaseWorkOrderViewModel<T> {
    // 工单列表数据
    val workOrders: StateFlow<List<T>>

    // 加载状态
    val isLoading: StateFlow<Boolean>

    // 加载更多状态
    val isLoadingMore: StateFlow<Boolean>

    // 是否有更多数据
    val hasMoreData: StateFlow<Boolean>

    // 错误信息
    val error: StateFlow<String?>

    // 当前页码
    val currentPage: StateFlow<Int>

    // 刷新数据
    fun refresh()

    // 加载更多数据
    fun loadMore()
}