package com.crrc.siom.ui.workorder.pages.process

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.ExposedDropdownMenuDefaults.outlinedTextFieldColors
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.Constant.EXPERT_GROUP_TYPE
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.model.ChatMessage
import com.crrc.siom.data.model.GroupChatMessage
import com.crrc.siom.data.model.HistoryMessageResponse
import com.crrc.siom.data.model.Message
import com.crrc.siom.ui.dashboard.detail.ChatDetailViewModel
import kotlin.collections.forEach

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExpertGroupPage(
    workOrderId: String,
    viewModel: ChatDetailViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val messages by viewModel.chatMessages.collectAsState()
    var newMessage by remember { mutableStateOf("") }
    var showMoreOptions by remember { mutableStateOf(false) }
    val focusManager = LocalFocusManager.current
    val currentUserId = SessionManager().getUserId().toString()
    val listState = rememberLazyListState()

    // 只在下拉到顶部时加载更多
    var lastIndexRequested by remember { mutableStateOf(-1) }

    LaunchedEffect(Unit) {
        snapshotFlow {
            Pair(
                listState.firstVisibleItemIndex,
                listState.firstVisibleItemScrollOffset
            )
        }.collect { (index, offset) ->
            val lastIndex = viewModel.chatMessages.value.lastIndex  // 从 viewModel 获取最新消息数
            if (
                lastIndex >= 0 &&
                index == lastIndex &&
                index != lastIndexRequested &&
                !viewModel.isLoadingHistory &&
                viewModel.hasMoreHistory
            ) {
                lastIndexRequested = index
                viewModel.getHistoryMessage()
            }
        }
    }



    Box(modifier = modifier.fillMaxSize()) {
        Column(modifier = Modifier.fillMaxSize()) {
            // 消息列表
            LazyColumn(
                state = listState,
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                reverseLayout = true
            ) {
                // 顶部加载指示器
                if (viewModel.isLoadingHistory) {
                    item {
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(modifier = Modifier.size(24.dp))
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
                items(messages.size) { index ->
                    val message = messages[index]
                    when (message) {
                        is ChatMessage -> {
                            MessageItem(
                                messageList = message.messageList,
                                currentUserId = currentUserId
                            )
                        }

                        is HistoryMessageResponse -> {
                            MessageItem(
                                messageList = message.messageList,
                                currentUserId = currentUserId
                            )
                        }

                        is GroupChatMessage -> {
                            MessageItem(
                                messageList = message.messageList,
                                currentUserId = currentUserId
                            )
                        }

                        else -> {}
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }

            // 底部输入栏
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shadowElevation = 4.dp
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 语音/键盘切换按钮
                    IconButton(onClick = { /* TODO: 切换语音输入 */ }) {
                        Icon(
                            Icons.Default.Mic,
                            contentDescription = "语音输入",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    OutlinedTextField(
                        value = newMessage,
                        onValueChange = { newMessage = it },
                        modifier = Modifier.weight(1f),
                        placeholder = { Text("输入消息...") },
                        maxLines = 3,
                        colors = outlinedTextFieldColors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                        )
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Box {
                        Row {
                            // 加号按钮
                            IconButton(onClick = { showMoreOptions = !showMoreOptions }) {
                                Icon(
                                    Icons.Default.Add,
                                    contentDescription = "更多选项",
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }

                            // 发送按钮
                            if (newMessage.isNotBlank()) {
                                IconButton(
                                    onClick = {
                                        viewModel.sendMessage(newMessage, EXPERT_GROUP_TYPE)
                                        newMessage = ""
                                        focusManager.clearFocus()
                                    }
                                ) {
                                    Icon(
                                        Icons.Default.Send,
                                        contentDescription = "发送",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }

        // 底部弹出菜单
        if (showMoreOptions) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.4f))
                    .clickable { showMoreOptions = false }
            )

            Surface(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
            ) {
                Column(modifier = Modifier.padding(vertical = 16.dp)) {
                    // 菜单选项
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        // 图片选项
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            IconButton(
                                modifier = Modifier
                                    .size(60.dp)
                                    .background(
                                        color = MaterialTheme.colorScheme.surfaceVariant,
                                        shape = CircleShape
                                    ),
                                onClick = {
                                    showMoreOptions = false
                                    /* TODO: 实现图片上传 */
                                }
                            ) {
                                Icon(
                                    Icons.Default.Image,
                                    contentDescription = "图片",
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                            Spacer(modifier = Modifier.height(8.dp))
                            Text("图片", style = MaterialTheme.typography.bodySmall)
                        }

                        // 拍照选项
                        // ... 类似的代码

                        // 视频选项
                        // ... 类似的代码
                    }
                }
            }
        }
    }
}

@Composable
private fun MessageItem(
    messageList: List<Message>,
    currentUserId: String
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        messageList.forEach { message ->
            val isFromMe = message.senderId == currentUserId
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = if (isFromMe) Arrangement.End else Arrangement.Start,
                verticalAlignment = Alignment.Top
            ) {
                if (!isFromMe) {
                    // 头像
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = message.senderName.firstOrNull()?.toString() ?: "",
                            color = MaterialTheme.colorScheme.primary,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                }

                // 消息内容
                when (message.type) {
                    "text" -> {
                        Box(
                            modifier = Modifier
                                .clip(
                                    RoundedCornerShape(
                                        topStart = if (isFromMe) 16.dp else 4.dp,
                                        topEnd = if (isFromMe) 4.dp else 16.dp,
                                        bottomStart = 16.dp,
                                        bottomEnd = 16.dp
                                    )
                                )
                                .then(
                                    if (isFromMe) {
                                        Modifier.background(MaterialTheme.colorScheme.primary)
                                    } else {
                                        Modifier
                                            .background(Color.White)
                                            .border(
                                                width = 1.dp,
                                                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
                                                shape = RoundedCornerShape(
                                                    topStart = if (isFromMe) 16.dp else 4.dp,
                                                    topEnd = if (isFromMe) 4.dp else 16.dp,
                                                    bottomStart = 16.dp,
                                                    bottomEnd = 16.dp
                                                )
                                            )
                                    }
                                )
                                .padding(horizontal = 12.dp, vertical = 8.dp)
                        ) {
                            Text(
                                text = message.text,
                                color = if (isFromMe) Color.White else MaterialTheme.colorScheme.onSurface
                            )
                        }
                    }

                    "image" -> {
                        // TODO: 实现图片显示
                        Box(
                            modifier = Modifier
                                .size(200.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.surfaceVariant),
                            contentAlignment = Alignment.Center
                        ) {
                            Text("[图片]")
                        }
                    }

                    "file" -> {
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.surfaceVariant)
                                .padding(horizontal = 12.dp, vertical = 8.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Icon(
                                    Icons.Default.AttachFile,
                                    contentDescription = "文件",
                                    tint = MaterialTheme.colorScheme.primary
                                )
                                Text(
                                    text = message.text,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }
                    }

                    else -> {
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.surfaceVariant)
                                .padding(horizontal = 12.dp, vertical = 8.dp)
                        ) {
                            Text(
                                text = "[未知消息类型]",
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(4.dp))
        }
    }
}