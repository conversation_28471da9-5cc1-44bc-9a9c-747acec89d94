package com.crrc.siom.ui.workorder.viewmodel

import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.OrderFilterParamResponse
import com.crrc.common.bean.response.OrderFilterParamResponse.FilterParam
import com.crrc.siom.data.repository.WorkOrderRepository
import com.crrc.siom.data.repository.WorkOrderRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class FilterViewModel : ViewModel() {
    private val workOrderRepository: WorkOrderRepository = WorkOrderRepositoryImpl()

    // 筛选参数原始数据
    private val _filterParams = MutableStateFlow<OrderFilterParamResponse?>(null)
    val filterParams: StateFlow<OrderFilterParamResponse?> = _filterParams.asStateFlow()

    // 选中的状态ID列表
    private val _selectedStatusIds = MutableStateFlow<List<String>>(emptyList())
    val selectedStatusIds: StateFlow<List<String>> = _selectedStatusIds.asStateFlow()

    // 选中的车站ID列表
    private val _selectedStationIds = MutableStateFlow<List<String>>(emptyList())
    val selectedStationIds: StateFlow<List<String>> = _selectedStationIds.asStateFlow()

    // 选中的优先级ID列表
    private val _selectedPriorityIds = MutableStateFlow<List<String>>(emptyList())
    val selectedPriorityIds: StateFlow<List<String>> = _selectedPriorityIds.asStateFlow()

    // 状态选项列表
    private val _statusOptions = MutableStateFlow<List<FilterParam>>(emptyList())
    val statusOptions: StateFlow<List<FilterParam>> = _statusOptions.asStateFlow()

    // 车站选项列表
    private val _stationOptions = MutableStateFlow<List<FilterParam>>(emptyList())
    val stationOptions: StateFlow<List<FilterParam>> = _stationOptions.asStateFlow()

    // 优先级选项列表
    private val _priorityOptions = MutableStateFlow<List<FilterParam>>(emptyList())
    val priorityOptions: StateFlow<List<FilterParam>> = _priorityOptions.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // 新增：存储所有选中项的完整对象
    var selectedParams: Map<String, List<FilterParam>> = emptyMap()

    // 初始化时加载筛选参数
    init {
        loadFilterParams()
    }

    fun loadFilterParams() {
        _isLoading.value = true
        _error.value = null

        workOrderRepository.getFilterParamList { params, error ->
            _isLoading.value = false

            if (error != null) {
                _error.value = error
            } else if (params != null) {
                _filterParams.value = params

                // 处理状态选项
                _statusOptions.value = params.status ?: emptyList()
                _selectedStatusIds.value = params.status
                    ?.filter { it.isSelected }
                    ?.map { it.id }
                    ?: emptyList()

                // 处理车站选项
                _stationOptions.value = params.station ?: emptyList()
                _selectedStationIds.value = params.station
                    ?.filter { it.isSelected }
                    ?.map { it.id }
                    ?: emptyList()

                // 处理优先级选项
                _priorityOptions.value = params.priority ?: emptyList()
                _selectedPriorityIds.value = params.priority
                    ?.filter { it.isSelected }
                    ?.map { it.id }
                    ?: emptyList()
            }
        }
    }

    // 切换状态选中状态
    fun toggleStatusSelection(id: String) {
        val current = _selectedStatusIds.value.toMutableList()
        if (current.contains(id)) {
            current.remove(id)
        } else {
            current.add(id)
        }
        _selectedStatusIds.value = current
    }

    // 切换车站选中状态
    fun toggleStationSelection(id: String) {
        val current = _selectedStationIds.value.toMutableList()
        if (current.contains(id)) {
            current.remove(id)
        } else {
            current.add(id)
        }
        _selectedStationIds.value = current
    }

    // 切换优先级选中状态
    fun togglePrioritySelection(id: String) {
        val current = _selectedPriorityIds.value.toMutableList()
        if (current.contains(id)) {
            current.remove(id)
        } else {
            current.add(id)
        }
        _selectedPriorityIds.value = current
    }

    // 构建筛选参数Map
    fun buildFilterParams(): Map<String, Any> {
        // 筛选状态选项并设置selected字段为true
        val selectedStatus = _statusOptions.value.filter { _selectedStatusIds.value.contains(it.id) }
            .map { param ->
                FilterParam().apply {
                    setId(param.id)
                    setName(param.name)
                    setSelected(true)
                }
            }

        // 筛选车站选项并设置selected字段为true
        val selectedStations = _stationOptions.value.filter { _selectedStationIds.value.contains(it.id) }
            .map { param ->
                FilterParam().apply {
                    setId(param.id)
                    setName(param.name)
                    setSelected(true)
                }
            }

        // 筛选优先级选项并设置selected字段为true
        val selectedPriority = _priorityOptions.value.filter { _selectedPriorityIds.value.contains(it.id) }
            .map { param ->
                FilterParam().apply {
                    setId(param.id)
                    setName(param.name)
                    setSelected(true)
                }
            }

        selectedParams = mapOf(
            "status" to selectedStatus,
            "station" to selectedStations,
            "priority" to selectedPriority
        )
        return selectedParams
    }

    // 重置所有筛选
    fun resetAllFilters() {
        _selectedStatusIds.value = emptyList()
        _selectedStationIds.value = emptyList()
        _selectedPriorityIds.value = emptyList()
    }
}