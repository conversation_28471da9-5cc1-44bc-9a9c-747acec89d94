package com.crrc.siom.ui.dashboard.groupInfo

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GroupInfoScreen(
    groupId: String,
    onBackClick: () -> Unit
) {
    val viewModel: GroupInfoViewModel = viewModel(
        factory = GroupInfoViewModelFactory(groupId)
    )
    val isLoading by viewModel.isLoading.observeAsState(false)
    val groupMemberInfo by viewModel.groupMemberInfo.observeAsState()
    val errorMessage by viewModel.errorMessage.observeAsState()

    LaunchedEffect(groupId) {
        viewModel.loadGroupMemberInfo(groupId)
    }


    if (isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
        return
    }
    
    if (errorMessage != null && groupMemberInfo == null) {
        // 显示错误
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = errorMessage!!,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(onClick = { viewModel.loadGroupMemberInfo(groupId) }) {
                    Text("重试")
                }
            }
        }
        return
    }
    
    Column(modifier = Modifier.fillMaxSize()) {
        TopAppBar(
            title = { Text("群成员列表") },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                groupMemberInfo?.forEach { member ->
                    InfoItem(member.userName, member.groupName)
                    HorizontalDivider()
                }
            }
        }
    }
}



@Composable
private fun InfoItem(
    userName: String?,
    groupName: String?
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = userName?: "",
            style = MaterialTheme.typography.bodyMedium,
        )
        Text(
            text = groupName?: "",
            style = MaterialTheme.typography.bodyMedium
        )
    }
} 