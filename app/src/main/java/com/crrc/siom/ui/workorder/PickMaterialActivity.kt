package com.crrc.siom.ui.workorder

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.crrc.siom.ui.theme.SiomTheme
import com.crrc.siom.ui.workorder.pages.PickMaterialPage

class PickMaterialActivity : ComponentActivity() {
    companion object {
        const val EXTRA_WORK_ORDER_ID = "work_order_id"

        fun start(context: Context, workOrderId: String) {
            val intent = Intent(context, PickMaterialActivity::class.java).apply {
                putExtra(EXTRA_WORK_ORDER_ID, workOrderId)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val workOrderId = intent.getStringExtra(EXTRA_WORK_ORDER_ID) ?: ""

        setContent {
            SiomTheme {
                PickMaterialPage(
                    workOrderId = workOrderId,
                    onBackClick = {
                        finish()
                    },
                    onPickSuccess = {
                        Toast.makeText(this, "领料成功", Toast.LENGTH_SHORT).show()
                        setResult(RESULT_OK)
                        finish()
                    }
                )
            }
        }
    }
} 