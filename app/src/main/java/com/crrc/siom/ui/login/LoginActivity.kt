package com.crrc.siom.ui.login
import android.os.Handler
import android.os.Looper
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModelProvider
import androidx.compose.runtime.livedata.observeAsState
import androidx.core.content.ContextCompat.startActivity
import com.crrc.common.utils.PrefsUtil
import com.crrc.common.utils.ToastUtil
import com.crrc.siom.AppApplication
import com.crrc.siom.MainActivity
import com.crrc.siom.R
import com.crrc.siom.data.SessionManager
import com.crrc.siom.ui.test.TestActivity
import com.crrc.siom.ui.theme.PrimaryDark
import com.crrc.siom.ui.theme.SiomTheme

class LoginActivity : ComponentActivity() {
    private lateinit var viewModel: LoginViewModel
    private lateinit var sessionManager: SessionManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        sessionManager = SessionManager()

        // 检查用户是否已登录
        if (sessionManager.isLoggedIn()) {
            // 用户已登录，直接进入主页面
            startActivity(Intent(this, MainActivity::class.java))
            finish()
            return
        }

        viewModel = ViewModelProvider(this)[LoginViewModel::class.java]

        setContent {
            SiomTheme {
                LoginScreen(viewModel, sessionManager)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(viewModel: LoginViewModel, sessionManager: SessionManager) {
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var showIpDialog by remember { mutableStateOf(false) }
    var ipAddress by remember { mutableStateOf("") }

    val context = LocalContext.current

    // 观察登录状态
    val isLoading by viewModel.isLoading.observeAsState(false)
    val loginResult by viewModel.loginResult.observeAsState()
    // 处理登录成功
    LaunchedEffect(loginResult) {
        loginResult?.let {
            sessionManager.saveUserLoginInfo(it)
            context.startActivity(Intent(context, MainActivity::class.java))
            (context as? ComponentActivity)?.finish()
        }
    }

    // 当对话框显示时，读取已保存的IP地址
    LaunchedEffect(showIpDialog) {
        if (showIpDialog) {
            ipAddress = PrefsUtil.getInstance(AppApplication.sApplication).baseUrl ?: ""
        }
    }

    // IP设置对话框
    if (showIpDialog) {
        AlertDialog(
            onDismissRequest = { showIpDialog = false },
            title = { Text("设置服务器IP地址") },
            text = {
                Column {
                    OutlinedTextField(
                        value = ipAddress,
                        onValueChange = { ipAddress = it },
                        label = { Text("IP地址") },
                        singleLine = true,
                        placeholder = { Text("请输入服务器IP地址") }
                    )
                    Text(
                        text = "修改后点击确定，将重启应用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        PrefsUtil.getInstance(AppApplication.sApplication).baseUrl = ipAddress
                        showIpDialog = false
                        restartAppWithDelay(AppApplication.sApplication)
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showIpDialog = false }) {
                    Text("取消")
                }
            }
        )
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Logo
            Image(
                painter = painterResource(id = R.drawable.login_back16),
                contentDescription = "Logo",
                modifier = Modifier
                    .size(120.dp)
                    .padding(bottom = 32.dp)
                    .clickable { showIpDialog = true }
            )

            Text(
                text = "SIOM系统登录",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 32.dp).clickable{
//                    context.startActivity(Intent(context, TestActivity::class.java))
                }
            )

            // Username field
            key("username_field") {
                OutlinedTextField(
                    value = username,
                    onValueChange = {
                        username = it
                    },
                    label = { Text("用户名") },
                    leadingIcon = { Icon(Icons.Default.Person, contentDescription = null) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )
                )
            }

            // Password field
            key("password_field") {
                OutlinedTextField(
                    value = password,
                    onValueChange = {
                        password = it
                    },
                    label = { Text("密码") },
                    leadingIcon = { Icon(Icons.Default.Lock, contentDescription = null) },
                    trailingIcon = {
                        IconButton(onClick = { passwordVisible = !passwordVisible }) {
                            Icon(
                                imageVector = if (passwordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (passwordVisible) "隐藏密码" else "显示密码"
                            )
                        }
                    },
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Password,
                        imeAction = ImeAction.Done
                    )
                )
            }



            key("login_button") {
                Button(
                    onClick = {
                        if (username.isNotEmpty() && password.isNotEmpty()) {
                            // 调用ViewModel进行登录
                            viewModel.login(username, password)
                        } else {
                            // 本地验证失败
                            ToastUtil.show( "用户名或密码不能为空")
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = PrimaryDark
                    ),
                    enabled = !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            color = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    } else {
                        Text("登 录")
                    }
                }
            }

            // Forgot password
            TextButton(
                onClick = { /* TODO: 实现忘记密码功能 */ },
                modifier = Modifier.padding(top = 8.dp)
            ) {
                Text("忘记密码?")
            }
        }

        Text(
            text = "版本 1.0.0",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 16.dp)
        )
    }
}



fun restartAppWithDelay(context: Context) {
    Handler(Looper.getMainLooper()).postDelayed({
        restartApp(context)
    }, 500)
}

fun restartApp(context: Context) {
    val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
    intent?.apply {
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        context.startActivity(this)
        Runtime.getRuntime().exit(0)
    }
}

