package com.crrc.siom.ui.workorder.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.OrderFilterParamResponse.FilterParam
import com.crrc.siom.ui.workorder.viewmodel.FilterViewModel
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties

@Composable
fun FilterBar(
    modifier: Modifier = Modifier,
    onFilterChanged: (Map<String, Any>) -> Unit,
    filterViewModel: FilterViewModel
) {
    val statusOptions by filterViewModel.statusOptions.collectAsState()
    val stationOptions by filterViewModel.stationOptions.collectAsState()
    val priorityOptions by filterViewModel.priorityOptions.collectAsState()
    
    val selectedStatusIds by filterViewModel.selectedStatusIds.collectAsState()
    val selectedStationIds by filterViewModel.selectedStationIds.collectAsState()
    val selectedPriorityIds by filterViewModel.selectedPriorityIds.collectAsState()
    
    val isLoading by filterViewModel.isLoading.collectAsState()
    
    // 添加日志检查
    LaunchedEffect(stationOptions) {
        println("车站选项数量: ${stationOptions.size}")
        stationOptions.forEach { 
            println("车站选项: id=${it.getId()}, name=${it.getName()}")
        }
    }
    
    // 当筛选条件发生变化时，通知调用者
    LaunchedEffect(selectedStatusIds, selectedStationIds, selectedPriorityIds) {
        val filterParams = filterViewModel.buildFilterParams()
        onFilterChanged(filterParams)
    }
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 4.dp),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (isLoading) {
            // 显示加载指示器
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("加载筛选选项...", style = MaterialTheme.typography.bodySmall)
        } else {
            // 工单状态筛选
            MultiSelectDropdown(
                title = "工单状态",
                options = statusOptions,
                selectedIds = selectedStatusIds,
                onSelectionChanged = { id -> filterViewModel.toggleStatusSelection(id) },
                modifier = Modifier.width(100.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 车站筛选
            MultiSelectDropdown(
                title = "车站",
                options = stationOptions,
                selectedIds = selectedStationIds,
                onSelectionChanged = { id -> filterViewModel.toggleStationSelection(id) },
                modifier = Modifier.width(60.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 优先级筛选
            if (priorityOptions.isNotEmpty()) {
                MultiSelectDropdown(
                    title = "优先级",
                    options = priorityOptions,
                    selectedIds = selectedPriorityIds,
                    onSelectionChanged = { id -> filterViewModel.togglePrioritySelection(id) },
                    modifier = Modifier.width(80.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 重置按钮
            OutlinedButton(
                onClick = { filterViewModel.resetAllFilters() },
                modifier = Modifier.height(36.dp),
                contentPadding = PaddingValues(horizontal = 8.dp)
            ) {
                Text("重置", style = MaterialTheme.typography.bodySmall)
            }
        }
    }
}

@Composable
private fun MultiSelectDropdown(
    title: String,
    options: List<FilterParam>,
    selectedIds: List<String>,
    onSelectionChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    
    val displayText = remember(selectedIds) {
        if (selectedIds.isEmpty()) title else "$title(${selectedIds.size})"
    }
    
    Column(modifier = modifier) {
        OutlinedButton(
            onClick = { expanded = true },
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = displayText,
                    style = MaterialTheme.typography.bodySmall,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
            }
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier
                .width(200.dp)
                .heightIn(max = 300.dp)
        ) {
            options.forEach { option ->
                val isSelected = selectedIds.contains(option.getId())
                DropdownMenuItem(
                    text = {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = option.getName(),
                                style = MaterialTheme.typography.bodyMedium
                            )
                            if (isSelected) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    },
                    onClick = {
                        onSelectionChanged(option.getId())
                        // 不关闭下拉菜单，允许继续选择
                    },
                    modifier = Modifier.background(
                        if (isSelected) MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
                        else Color.Transparent
                    )
                )
            }
        }
    }
}