package com.crrc.siom.ui.workorder.viewmodel

import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.TempWorkOrderParamResponse
import com.crrc.siom.data.repository.WorkOrderRepository
import com.crrc.siom.data.repository.WorkOrderRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class CreateTempWorkOrderViewModel: ViewModel() {
    private val workOrderRepository: WorkOrderRepository = WorkOrderRepositoryImpl()

    private val _optionParam = MutableStateFlow<TempWorkOrderParamResponse?>(null)
    val optionParam: StateFlow<TempWorkOrderParamResponse?> = _optionParam.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    fun loadOptionParam() {
        _isLoading.value = true
        _error.value = null

        workOrderRepository.getTempWorkOrderParam { response, error ->
            _isLoading.value = false
            if (error != null) {
                _error.value = error
            } else {
                _optionParam.value = response
            }
        }
    }

    fun submitTempWorkOrderParam(onSuccess: () -> Unit, onError: (String) -> Unit) {
        _isLoading.value = true
        _error.value = null

        workOrderRepository.submitTempWorkOrderParam(
            param = _optionParam.value!!,
            callback = {success, errorMsg ->
                _isLoading.value = false
                if (success == true) {
                    onSuccess()
                } else {
                    onError(errorMsg ?: "提交失败，请稍后重试")
                }
            }
        )
    }
}