package com.crrc.siom.ui.workorder

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.crrc.siom.ui.theme.SiomTheme
import com.crrc.siom.ui.workorder.pages.CreateTempWorkOrderPage

class CreateTempWorkOrderActivity : ComponentActivity() {
    
    companion object {
        fun start(context: Context) {
            val intent = Intent(context, CreateTempWorkOrderActivity::class.java)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SiomTheme {
                CreateTempWorkOrderPage(
                    onBackClick = { finish() }
                )
            }
        }
    }
} 