package com.crrc.siom.ui.qrcode;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.crrc.siom.R;
import com.google.zxing.BarcodeFormat;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;
import com.journeyapps.barcodescanner.DefaultDecoderFactory;
import com.journeyapps.barcodescanner.Size;
import com.journeyapps.barcodescanner.ViewfinderView;

import java.util.Collections;

public class QRScannerActivity extends AppCompatActivity {
    private static final String TAG = "QRScannerActivity";
    public static final String RESULT_QR_CODE = "qr_code_result";
    public static final int REQUEST_SCAN_QR_CODE = 100;
    private static final int REQUEST_CAMERA_PERMISSION = 200;

    private DecoratedBarcodeView barcodeView;
    private ImageButton flashlightButton;
    private TextView statusText;
    private boolean flashEnabled = false;

    /**
     * 启动二维码扫描界面
     *
     * @param context 上下文
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, QRScannerActivity.class);
        if (context instanceof Activity) {
            ((Activity) context).startActivityForResult(intent, REQUEST_SCAN_QR_CODE);
        } else {
            context.startActivity(intent);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qr_scanner);

        // 初始化界面
        initViews();
        // 检查相机权限
        checkCameraPermission();
    }

    private void initViews() {
        // 设置工具栏
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowTitleEnabled(true);
            getSupportActionBar().setTitle("扫一扫");
        }
        toolbar.setNavigationOnClickListener(v -> finish());

        // 初始化扫描视图
        barcodeView = findViewById(R.id.barcode_scanner);
        statusText = findViewById(R.id.status_text);
        flashlightButton = findViewById(R.id.flashlight_button);

        // 检查设备是否有闪光灯
        boolean hasFlash = getApplicationContext().getPackageManager()
                .hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH);
        
        if (hasFlash) {
            flashlightButton.setVisibility(View.VISIBLE);
            flashlightButton.setOnClickListener(v -> toggleFlashlight());
        } else {
            flashlightButton.setVisibility(View.GONE);
        }

        // 设置扫描格式仅支持QR码
        barcodeView.getBarcodeView().setDecoderFactory(
                new DefaultDecoderFactory(Collections.singletonList(BarcodeFormat.QR_CODE))
        );

        // 设置扫描结果回调
        barcodeView.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                if (result.getText() != null) {
                    barcodeView.pause();
                    handleQrCodeResult(result.getText());
                }
            }
        });
    }

    private void handleQrCodeResult(String result) {
        Intent intent = new Intent();
        intent.putExtra(RESULT_QR_CODE, result);
        setResult(RESULT_OK, intent);
        finish();
    }

    private void toggleFlashlight() {
        flashEnabled = !flashEnabled;
        if (flashEnabled) {
            barcodeView.setTorchOn();
            flashlightButton.setImageResource(R.drawable.ic_flashlight_on);
        } else {
            barcodeView.setTorchOff();
            flashlightButton.setImageResource(R.drawable.ic_flashlight_off);
        }
    }

    private void checkCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                    new String[]{android.Manifest.permission.CAMERA},
                    REQUEST_CAMERA_PERMISSION);
        } else {
            // 已有权限，设置扫描区域
            setupScanningArea();
        }
    }

    private void setupScanningArea() {
        // 在布局完成后设置扫描区域
        barcodeView.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
            try {
                int screenWidth = barcodeView.getWidth();
                int screenHeight = barcodeView.getHeight();
                int scannerSize = (int) (Math.min(screenWidth, screenHeight) * 0.7f);

                // 计算扫描区域位置
                int centerX = screenWidth / 2;
                int centerY = screenHeight / 2;

                // 设置扫描区域 - 使用正确的方法
               // barcodeView.getBarcodeView().setFramingRectSize(new Size(100,100));
            } catch (Exception e) {
                Log.e(TAG, "设置扫描区域失败", e);
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                setupScanningArea();
            } else {
                Toast.makeText(this, "需要相机权限以进行扫码", Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        barcodeView.resume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        barcodeView.pause();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return barcodeView.onKeyDown(keyCode, event) || super.onKeyDown(keyCode, event);
    }
} 