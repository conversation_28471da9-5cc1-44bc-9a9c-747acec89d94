package com.crrc.siom.ui.workorder

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.crrc.siom.ui.theme.SiomTheme
import com.crrc.siom.ui.workorder.pages.detail.WorkOrderDetailPage

class WorkOrderDetailActivity : ComponentActivity() {
    companion object {
        private const val EXTRA_WORK_ORDER_ID = "work_order_id"
        
        fun start(context: Context, workOrderId: String) {
            val intent = Intent(context, WorkOrderDetailActivity::class.java).apply {
                putExtra(EXTRA_WORK_ORDER_ID, workOrderId)
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val workOrderId = intent.getStringExtra(EXTRA_WORK_ORDER_ID) ?: ""
        
        setContent {
            SiomTheme {
                WorkOrderDetailPage(
                    workOrderId = workOrderId,
                    onBackClick = {
                        setResult(RESULT_OK)
                        finish()
                    }
                )
            }
        }
    }
} 