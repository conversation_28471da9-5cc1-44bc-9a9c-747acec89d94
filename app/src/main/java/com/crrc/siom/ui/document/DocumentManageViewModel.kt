package com.crrc.siom.ui.document

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.DocumentRecord
import com.crrc.common.bean.response.LabelListResponse
import com.crrc.common.utils.ToastUtil
import com.crrc.siom.data.repository.DocumentRepository
import com.crrc.siom.data.repository.DocumentRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class DocumentManageViewModel : ViewModel() {
    private val repository: DocumentRepository = DocumentRepositoryImpl()
    private val _currentPath = MutableStateFlow("/") // 显示用
    val currentPath: StateFlow<String> = _currentPath.asStateFlow()

    private val _dirIds = MutableStateFlow("/") // API请求用

    private val _documents = MutableStateFlow<List<DocumentRecord>>(emptyList())
    val documents: StateFlow<List<DocumentRecord>> = _documents.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // 添加侧边栏状态
    private val _isSidebarOpen = MutableStateFlow(false)
    val isSidebarOpen: StateFlow<Boolean> = _isSidebarOpen.asStateFlow()
    
    // 当前选中的标签
    private val _selectedTag = MutableStateFlow<LabelListResponse?>(null)
    val selectedTag: StateFlow<LabelListResponse?> = _selectedTag.asStateFlow()
    
    // 标签列表
    private val _tags = MutableStateFlow<List<LabelListResponse>>(emptyList())
    val tags: StateFlow<List<LabelListResponse>> = _tags.asStateFlow()

    init {
        loadDocuments("/")
        loadTags()
    }

    fun loadDocuments(dirIds: String) {
        _isLoading.value = true
        repository.getDirsList(dirIds) { response, error ->
            _isLoading.value = false
            if (response != null) {
                _documents.value = response.records ?: emptyList()
                _dirIds.value = dirIds
            } else {
                // 错误处理（可用Toast等）
            }
        }
    }

    fun enterDirectory(dirId: String) {
        _isLoading.value = true
        repository.getCurrentDirs(dirId) { response, error ->
            if (response != null) {
                val dirs = response.records?.dirs ?: "/"
                val dirIds = response.records?.dirIds ?: "/"
                _currentPath.value = dirs
                repository.getDirsList(dirIds) { listResponse, listError ->
                    _isLoading.value = false
                    if (listResponse != null) {
                        _documents.value = listResponse.records ?: emptyList()
                        _dirIds.value = dirIds
                    } else {
                        // 错误处理
                    }
                }
            } else {
                _isLoading.value = false
                // 错误处理
            }
        }
    }

    fun enterFileDirectory(dirId: String) {
        _isLoading.value = true
        repository.getCurrentDirs(dirId) { response, error ->
            if (response != null) {
                val dirs = response.records?.dirs ?.substringBeforeLast('/') ?: "/"
                val dirIds = response.records?.dirIds ?.substringBeforeLast('/') ?: "/"
                _currentPath.value = dirs
                repository.getDirsList(dirIds) { listResponse, listError ->
                    _isLoading.value = false
                    if (listResponse != null) {
                        _documents.value = listResponse.records ?: emptyList()
                        _dirIds.value = dirIds
                    } else {
                        // 错误处理
                    }
                }
            } else {
                _isLoading.value = false
                // 错误处理
            }
        }
    }




    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    // 切换侧边栏状态
    fun toggleSidebar() {
        _isSidebarOpen.value = !_isSidebarOpen.value
    }
    
    // 选择标签
    fun selectTag(tag: LabelListResponse?) {
        _selectedTag.value = tag
        loadDocumentsByTag(tag?.id)
    }
    
    // 根据标签加载文档
    private fun loadDocumentsByTag(tagId: String?) {
        _isLoading.value = true
        if (tagId == null) {
            loadDocuments(_dirIds.value)
            return
        }
        repository.getFilesByLabel(tagId) { response, error ->
            _isLoading.value = false
            if (response != null) {
                _documents.value = response.records ?: emptyList()
            } else {
                ToastUtil.show(error)
            }
        }
    }

    // 搜索文档
    fun searchDocuments(query: String) {
        _isLoading.value = true
        repository.searchFileName(query) { response, error ->
            _isLoading.value = false
            if (response != null) {
                _documents.value = response.records ?: emptyList()
            } else {
                ToastUtil.show(error)
            }
        }
    }

    // 加载标签列表
    private fun loadTags() {
        repository.getLabelList { response, error ->
            if (response != null) {
                _tags.value = response
            } else {
                _tags.value = emptyList()
            }
        }
    }

    // 返回上一级目录，如果已是根目录则返回 false，否则返回 true
    fun goBackDirectory(): Boolean {
        val current = _currentPath.value
        if (current == "/" || current.isEmpty()) {
            return false
        }
        val pathParts = current.trimEnd('/').split("/").filter { it.isNotEmpty() }
        if (pathParts.isEmpty()) {
            _currentPath.value = "/"
            loadDocuments("/")
            return false
        }
        val parentPath = if (pathParts.size == 1) "/" else "/" + pathParts.dropLast(1).joinToString("/")
        _currentPath.value = parentPath
        loadDocuments(parentPath)
        return true
    }
} 