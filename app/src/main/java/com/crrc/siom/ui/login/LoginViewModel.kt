package com.crrc.siom.ui.login

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.LoginBean
import com.crrc.siom.data.repository.UserRepository
import com.crrc.siom.data.repository.UserRepositoryImpl

class LoginViewModel : ViewModel() {
    private val userRepository: UserRepository = UserRepositoryImpl()
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _loginResult = MutableLiveData<LoginBean?>()
    val loginResult: LiveData<LoginBean?> = _loginResult
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    fun login(username: String, password: String) {
        _isLoading.value = true
        _errorMessage.value = null
        
        userRepository.login(username, password) { loginResponse, errorMsg ->
            _isLoading.value = false
            if (loginResponse != null) {
                // 登录成功
                _loginResult.value = loginResponse
            } else {
                // 登录失败
                _errorMessage.value = errorMsg ?: "未知错误"
            }
        }
    }
} 