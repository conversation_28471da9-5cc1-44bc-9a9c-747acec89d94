package com.crrc.siom.ui.device

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material3.ExperimentalMaterial3Api
import com.crrc.siom.ui.theme.SiomTheme

class DeviceDetailActivity : ComponentActivity() {

    companion object {

        fun start(context: Context,param: String,type: String) {
             val intent = Intent(context, DeviceDetailActivity::class.java).apply {
                 putExtra("param", param)
                 putExtra("type", type)
             }
            context.startActivity(intent)
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val param = intent.getStringExtra("param") ?: ""
        val type = intent.getStringExtra("type") ?: ""

        setContent {
            SiomTheme {
                DeviceDetailScreen(param = param,type = type)
            }
        }
    }
} 