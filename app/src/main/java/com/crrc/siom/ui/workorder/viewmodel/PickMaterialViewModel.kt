package com.crrc.siom.ui.workorder.viewmodel

import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.PickMaterialResponse
import com.crrc.common.utils.GsonUtil
import com.crrc.siom.data.repository.WorkOrderRepository
import com.crrc.siom.data.repository.WorkOrderRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class PickMaterialViewModel : ViewModel() {
    private val workOrderRepository: WorkOrderRepository = WorkOrderRepositoryImpl()
    
    // 领料清单
    private val _pickMaterialList = MutableStateFlow<PickMaterialResponse?>(null)
    val pickMaterialList: StateFlow<PickMaterialResponse?> = _pickMaterialList.asStateFlow()
    
    // 已选备品备件
    private val _selectedStandbyItems = MutableStateFlow<List<Int>>(emptyList())
    val selectedStandbyItems: StateFlow<List<Int>> = _selectedStandbyItems.asStateFlow()
    
    // 已选工器具
    private val _selectedToolItems = MutableStateFlow<List<Int>>(emptyList())
    val selectedToolItems: StateFlow<List<Int>> = _selectedToolItems.asStateFlow()
    
    // 物品数量
    private val _itemQuantities = MutableStateFlow<Map<Int, Int>>(emptyMap())
    val itemQuantities: StateFlow<Map<Int, Int>> = _itemQuantities.asStateFlow()
    
    // 物品最大数量(库存)
    private val _itemStock = MutableStateFlow<Map<Int, Int>>(emptyMap())
    val itemStock: StateFlow<Map<Int, Int>> = _itemStock.asStateFlow()
    
    // 错误消息
    private val _quantityError = MutableStateFlow<String?>(null)
    val quantityError: StateFlow<String?> = _quantityError.asStateFlow()
    
    // 备注
    private val _note = MutableStateFlow("")
    val note: StateFlow<String> = _note.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    fun loadPickMaterialList(workOrderId: String) {
        _isLoading.value = true
        _error.value = null
        
        workOrderRepository.getPickMaterialList(
            workOrderId,
            _selectedStandbyItems.value,
            _selectedToolItems.value,
            _note.value
        ) { response, error ->
            _isLoading.value = false
            
            if (error != null) {
                _error.value = error
            } else {
                _pickMaterialList.value = response
                
                // 初始化数量和最大数量
                val quantities = mutableMapOf<Int, Int>()
                val maxQuantities = mutableMapOf<Int, Int>()
                
                response?.standbyItems?.forEach { item ->
                    quantities[item.id] = item.amount
                    maxQuantities[item.id] = item.stock // 最大数量
                }
                
                response?.toolItems?.forEach { item ->
                    quantities[item.id] = item.amount
                    maxQuantities[item.id] = item.stock // 最大数量
                }
                
                _itemQuantities.value = quantities
                _itemStock.value = maxQuantities
                
                // 初始化已选项为空
                _selectedStandbyItems.value = emptyList()
                _selectedToolItems.value = emptyList()
                
                response?.note?.let {
                    if (it.isNotEmpty()) {
                        _note.value = it
                    }
                }
            }
        }
    }
    
    fun toggleStandbyItem(id: Int) {
        val currentList = _selectedStandbyItems.value.toMutableList()
        if (currentList.contains(id)) {
            currentList.remove(id)
            // 取消选择时，将数量设为0
            updateItemQuantity(id, 0)
        } else {
            currentList.add(id)
            // 选择时，将数量设为1
            updateItemQuantity(id, 1)
        }
        _selectedStandbyItems.value = currentList
    }
    
    fun toggleToolItem(id: Int) {
        val currentList = _selectedToolItems.value.toMutableList()
        if (currentList.contains(id)) {
            currentList.remove(id)
            // 取消选择时，将数量设为0
            updateItemQuantity(id, 0)
        } else {
            currentList.add(id)
            // 选择时，将数量设为1
            updateItemQuantity(id, 1)
        }
        _selectedToolItems.value = currentList
    }
    
    fun updateItemQuantity(id: Int, quantity: Int) {
        val maxQuantity = _itemStock.value[id] ?: 0
        
        if (quantity > maxQuantity) {
            _quantityError.value = "物料不足，最大可用数量为 $maxQuantity"
            return
        }
        
        _quantityError.value = null
        
        val currentQuantities = _itemQuantities.value.toMutableMap()
        currentQuantities[id] = quantity
        _itemQuantities.value = currentQuantities
        
        // 如果数量大于0，确保该物品被选中
        if (quantity > 0) {
            // 检查是否是备品备件
            if (_pickMaterialList.value?.standbyItems?.any { it.id == id } == true) {
                if (!_selectedStandbyItems.value.contains(id)) {
                    val newList = _selectedStandbyItems.value.toMutableList()
                    newList.add(id)
                    _selectedStandbyItems.value = newList
                }
            } 
            // 检查是否是工器具
            else if (_pickMaterialList.value?.toolItems?.any { it.id == id } == true) {
                if (!_selectedToolItems.value.contains(id)) {
                    val newList = _selectedToolItems.value.toMutableList()
                    newList.add(id)
                    _selectedToolItems.value = newList
                }
            }
        }
    }
    
    fun clearQuantityError() {
        _quantityError.value = null
    }
    
    fun updateNote(note: String) {
        _note.value = note
    }

    fun confirmPickMaterial(workOrderId: String): Boolean {
        // 检查是否有选中的物品
        if (_selectedStandbyItems.value.isEmpty() && _selectedToolItems.value.isEmpty()) {
            _error.value = "请至少选择一个物品"
            return false
        }

        // 检查选中的物品是否都有数量
        val hasZeroQuantity = _selectedStandbyItems.value.any { _itemQuantities.value[it] == 0 } ||
                _selectedToolItems.value.any { _itemQuantities.value[it] == 0 }

        if (hasZeroQuantity) {
            _error.value = "请为所有选中的物品设置数量"
            return false
        }

        val original = _pickMaterialList.value
        if (original == null) {
            _error.value = "物料清单为空"
            return false
        }

        // 复制并过滤standbyItems
        val selectedStandby = original.standbyItems?.filter { _selectedStandbyItems.value.contains(it.id) }?.map { item ->
            val newItem = PickMaterialResponse.Item()
            newItem.id = item.id
            newItem.name = item.name
            newItem.amount = _itemQuantities.value[item.id] ?: 0
            newItem
        }

        // 复制并过滤toolItems
        val selectedTool = original.toolItems?.filter { _selectedToolItems.value.contains(it.id) }?.map { item ->
            val newItem = PickMaterialResponse.Item()
            newItem.id = item.id
            newItem.name = item.name
            newItem.amount = _itemQuantities.value[item.id] ?: 0
            newItem
        }

        val newPickMaterial = PickMaterialResponse().apply {
            standbyItems = selectedStandby
            toolItems = selectedTool
            note = _note.value
        }

        // 转为JSON
        val orderPickJson = GsonUtil.GsonString(newPickMaterial)
        // 提交
        workOrderRepository.submitPickMaterialList(workOrderId, orderPickJson) { success, error ->
            if (error != null) {
                _error.value = error
            }
        }
        return true
    }
} 