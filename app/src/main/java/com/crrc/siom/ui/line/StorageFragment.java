package com.crrc.siom.ui.line;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.crrc.common.bean.response.LineDevice;
import com.crrc.common.bean.response.LineTool;
import com.crrc.siom.R;
import com.crrc.siom.data.model.StorageItem;

import java.util.ArrayList;
import java.util.List;

public class StorageFragment extends Fragment {
    
    private RecyclerView recyclerView;
    private StorageAdapter adapter;
    private String stationName;
    
    public static StorageFragment newInstance(String stationName) {
        StorageFragment fragment = new StorageFragment();
        Bundle args = new Bundle();
        args.putString("station_name", stationName);
        fragment.setArguments(args);
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            stationName = getArguments().getString("station_name");
        }
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_storage, container, false);
        recyclerView = view.findViewById(R.id.recycler_view);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new StorageAdapter();
        recyclerView.setAdapter(adapter);
        
        return view;
    }
    
    public void updateStorageData(List<?> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        List<StorageItem> storageItems = new ArrayList<>();
        for (Object item : items) {
            if (item instanceof LineDevice) {
                LineDevice device = (LineDevice) item;
                storageItems.add(new StorageItem(
                    device.getName(),
                    device.getBrand(),
                    Integer.parseInt(device.getTotal()),
                    Integer.parseInt(device.getStockNum())
                ));
            } else if (item instanceof LineTool) {
                LineTool tool = (LineTool) item;
                storageItems.add(new StorageItem(
                    tool.getToolName(),
                    tool.getToolType(),
                    Integer.parseInt(tool.getTotal()),
                    Integer.parseInt(tool.getStock())
                ));
            }
        }
        adapter.setItems(storageItems);
    }
    
    private class StorageAdapter extends RecyclerView.Adapter<StorageViewHolder> {
        private List<StorageItem> items = new ArrayList<>();
        
        @NonNull
        @Override
        public StorageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_storage, parent, false);
            return new StorageViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull StorageViewHolder holder, int position) {
            StorageItem item = items.get(position);
            holder.bind(item);
        }
        
        @Override
        public int getItemCount() {
            return items.size();
        }
        
        @SuppressLint("NotifyDataSetChanged")
        public void setItems(List<StorageItem> items) {
            this.items = items;
            notifyDataSetChanged();
        }
        
        public StorageItem getItem(int position) {
            return items.get(position);
        }
    }
    
    private static class StorageViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvItemName;
        private final TextView tvProduceDate;
        private final TextView tvQuantity;
        private final TextView tvOtherInfo;
        
        public StorageViewHolder(@NonNull View itemView) {
            super(itemView);
            tvItemName = itemView.findViewById(R.id.tv_item_name);
            tvProduceDate = itemView.findViewById(R.id.tv_produce_date);
            tvQuantity = itemView.findViewById(R.id.tv_quantity);
            tvOtherInfo = itemView.findViewById(R.id.tv_other_info);
        }
        
        public void bind(StorageItem item) {
            tvItemName.setText(item.getItemName());
            tvProduceDate.setText(item.getProduceDate());
            tvQuantity.setText(String.valueOf(item.getQuantity()));
            tvOtherInfo.setText(String.valueOf(item.getOtherInfo()));
        }
    }
} 