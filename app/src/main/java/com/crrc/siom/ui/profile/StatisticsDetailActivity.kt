package com.crrc.siom.ui.profile

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.crrc.siom.ui.theme.SiomTheme
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.utils.ColorTemplate
import androidx.compose.ui.viewinterop.AndroidView
import java.util.*
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.siom.data.SessionManager
import androidx.compose.ui.graphics.toArgb
import android.app.DatePickerDialog
import android.util.Log
import androidx.compose.foundation.clickable
import androidx.compose.ui.platform.LocalContext
import android.os.Build
import android.app.Application
import android.app.ActivityManager
import android.os.Process

class StatisticsDetailActivity : ComponentActivity() {
    companion object {
        fun start(context: Context) {
            val intent = Intent(context, StatisticsDetailActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        printProcessInfo(this, "ActivityProcess")
        setContent {
            SiomTheme {
                StatisticsDetailContent(onBackClick = { finish() })
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StatisticsDetailContent(onBackClick: () -> Unit) {
    val viewModel: StatisticsViewModel = viewModel()
    val isLoading by viewModel.isLoading.collectAsState()
    val statisticsData by viewModel.statisticsData.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    var timeType by remember { mutableStateOf(2) } // 1:日 2:月 3:年
    var currentDateMillis by remember { mutableStateOf(Calendar.getInstance().timeInMillis) }


    val pieColors = listOf(Color(0xFF42A5F5).toArgb(), Color(0xFF80D6FF).toArgb())
    val legendColors = listOf(Color(0xFF42A5F5), Color(0xFF80D6FF))
    val legendLabels = listOf("未完成", "已完成")

    // 日期选择器弹窗状态
    var showDatePicker by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 当前日期对象和显示文本
    val cal = Calendar.getInstance().apply { timeInMillis = currentDateMillis }
    val timeText = when (timeType) {
        1 -> "${cal.get(Calendar.YEAR)}年${cal.get(Calendar.MONTH)+1}月${cal.get(Calendar.DAY_OF_MONTH)}日"
        2 -> "${cal.get(Calendar.YEAR)}年${cal.get(Calendar.MONTH)+1}月"
        3 -> "${cal.get(Calendar.YEAR)}年"
        else -> ""
    }

    // 每次切换 timeType 或日期时请求数据
    LaunchedEffect(timeType, currentDateMillis) {
        val userId =  SessionManager().getUserId()
        viewModel.loadStatistics(cal, timeType, userId.toString())
    }

    // 日期选择器弹窗逻辑
    if (showDatePicker) {
        DatePickerDialog(
            context,
            { _, year, month, dayOfMonth ->
                val pickedCal = Calendar.getInstance().apply {
                    set(Calendar.YEAR, year)
                    set(Calendar.MONTH, month)
                    set(Calendar.DAY_OF_MONTH, dayOfMonth)
                }
                currentDateMillis = when (timeType) {
                    1 -> pickedCal.timeInMillis
                    2 -> Calendar.getInstance().apply {
                        set(Calendar.YEAR, year)
                        set(Calendar.MONTH, month)
                        set(Calendar.DAY_OF_MONTH, 1)
                    }.timeInMillis
                    3 -> Calendar.getInstance().apply {
                        set(Calendar.YEAR, year)
                        set(Calendar.MONTH, 0)
                        set(Calendar.DAY_OF_MONTH, 1)
                    }.timeInMillis
                    else -> pickedCal.timeInMillis
                }
                showDatePicker = false
            },
            cal.get(Calendar.YEAR),
            cal.get(Calendar.MONTH),
            cal.get(Calendar.DAY_OF_MONTH)
        ).apply {
            setOnCancelListener { showDatePicker = false }
        }.show()
    }

    Column(modifier = Modifier.fillMaxSize()) {
        TopAppBar(
            title = { Text("统计") },
            navigationIcon = {
                IconButton(onBackClick) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )
        Spacer(modifier = Modifier.height(8.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // timeType切换
            val timeTypeOptions = listOf("按日查看", "按月查看", "按年查看")
            var expanded by remember { mutableStateOf(false) }
            Box {
                Button(onClick = { expanded = true }) {
                    Text(timeTypeOptions[timeType - 1])
                }
                DropdownMenu(expanded = expanded, onDismissRequest = { expanded = false }) {
                    timeTypeOptions.forEachIndexed { index, label ->
                        DropdownMenuItem(onClick = {
                            timeType = index + 1
                            expanded = false
                        }, text = { Text(label) })
                    }
                }
            }
            Spacer(modifier = Modifier.width(8.dp))
            // 时间切换
            IconButton(onClick = {
                val cal2 = Calendar.getInstance().apply { timeInMillis = currentDateMillis }
                when (timeType) {
                    1 -> cal2.add(Calendar.DAY_OF_MONTH, -1)
                    2 -> cal2.add(Calendar.MONTH, -1)
                    3 -> cal2.add(Calendar.YEAR, -1)
                }
                currentDateMillis = cal2.timeInMillis
            }) {
                Icon(Icons.Default.ArrowBack, contentDescription = "上一时间")
            }
            Text(
                text = timeText,
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier
                    .padding(horizontal = 8.dp)
                    .clickable { showDatePicker = true }
            )
            IconButton(onClick = {
                val cal2 = Calendar.getInstance().apply { timeInMillis = currentDateMillis }
                when (timeType) {
                    1 -> cal2.add(Calendar.DAY_OF_MONTH, 1)
                    2 -> cal2.add(Calendar.MONTH, 1)
                    3 -> cal2.add(Calendar.YEAR, 1)
                }
                currentDateMillis = cal2.timeInMillis
            }) {
                Icon(Icons.Default.ArrowBack, contentDescription = "下一时间", modifier = Modifier.rotate(180f))
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "工单统计",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(start = 32.dp, bottom = 8.dp)
        )
        // 饼图
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(260.dp),
            contentAlignment = Alignment.Center
        ) {
            when {
                isLoading -> CircularProgressIndicator()
                errorMessage != null -> Text(errorMessage ?: "", color = MaterialTheme.colorScheme.error)
                statisticsData.isEmpty() -> Text("暂无数据", color = MaterialTheme.colorScheme.onSurfaceVariant)
                else -> AndroidView(
                    factory = { context ->
                        PieChart(context).apply {
                            val dataSet = PieDataSet(statisticsData, "")
                            dataSet.colors = pieColors
                            dataSet.valueTextSize = 16f
                            val data = PieData(dataSet)
                            this.data = data
                            this.description.isEnabled = false
                            this.legend.isEnabled = false
                            this.setDrawEntryLabels(false)
                            this.invalidate()
                        }
                    },
                    update = { chart ->
                        val dataSet = PieDataSet(statisticsData, "")
                        dataSet.colors = pieColors
                        dataSet.valueTextSize = 16f
                        val data = PieData(dataSet)
                        chart.data = data
                        chart.invalidate()
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
        Spacer(modifier = Modifier.height(8.dp))
        // 图例
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            legendColors.zip(legendLabels).forEach { (color, label) ->
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .background(color)
                )
                Text(text = " $label", color = color, modifier = Modifier.padding(end = 16.dp))
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewStatisticsDetailContent() {
    StatisticsDetailContent(onBackClick = {})
} 

fun printProcessInfo(context: Context, tag: String = "ProcessTest") {
    val pid = Process.myPid()
    val processName = if (Build.VERSION.SDK_INT >= 28) {
        Application.getProcessName()
    } else {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        manager.runningAppProcesses.firstOrNull { it.pid == pid }?.processName
    }
    Log.d(tag, "pid=$pid, processName=$processName")
}