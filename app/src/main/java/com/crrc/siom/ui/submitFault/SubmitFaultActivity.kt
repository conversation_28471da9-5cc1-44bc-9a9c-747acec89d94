package com.crrc.siom.ui.submitFault

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.crrc.common.bean.response.AlarmResponse
import com.crrc.common.bean.response.DeviceInfo
import com.crrc.siom.ui.theme.SiomTheme
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.material3.SnackbarDuration
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SubmitFaultActivity : ComponentActivity() {
    private val viewModel: SubmitFaultViewModel by viewModels()
    
    companion object {
        fun start(context: Context) {
            val intent = Intent(context, SubmitFaultActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SiomTheme {
                SubmitFaultContent(
                    viewModel = viewModel,
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
@Composable
fun SubmitFaultContent(
    viewModel: SubmitFaultViewModel,
    onBackClick: () -> Unit
) {
    val deviceData by viewModel.deviceData.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val selectedControlArea by viewModel.selectedControlArea.collectAsState()
    val controlAreas by viewModel.controlAreas.collectAsState()
    val selectedDeviceType by viewModel.selectedDeviceType.collectAsState()
    val deviceTypes by viewModel.deviceTypes.collectAsState()
    val selectedDevice by viewModel.selectedDevice.collectAsState()
    val devices by viewModel.devices.collectAsState()
    val faultDescription by viewModel.faultDescription.collectAsState()
    val repairAdvice by viewModel.repairAdvice.collectAsState()
    val alarmResponses by viewModel.alarmResponses.collectAsState()
    val selectedAlarm by viewModel.selectedAlarm.collectAsState()
    val isAlarmLoading by viewModel.isAlarmLoading.collectAsState()
    val isFaultDetailLoading by viewModel.isFaultDetailLoading.collectAsState()
    
    // 对话框状态
    var showDeviceSelectorDialog by remember { mutableStateOf(false) }
    var showAlarmSelectorDialog by remember { mutableStateOf(false) }
    
    val scrollState = rememberScrollState()
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    
    // 加载设备数据
    LaunchedEffect(Unit) {
        viewModel.loadDeviceData()
    }
    
    // 错误提示
    LaunchedEffect(error) {
        error?.let {
            coroutineScope.launch {
                snackbarHostState.showSnackbar(it)
            }
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            TopAppBar(
                title = { Text("故障上报") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
            
            if (isLoading && deviceData == null) {
                // 显示加载中
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(scrollState)
                        .padding(16.dp)
                ) {
                    // 故障设备选择项
                    SelectionItem(
                        title = "故障设备",
                        value = selectedDevice?.name ?: "",
                        onSelect = { 
                            if (controlAreas.isNotEmpty()) {
                                showDeviceSelectorDialog = true
                            }
                        }
                    )
    
                    HorizontalDivider()
                    Spacer(modifier = Modifier.height(12.dp))
    
                    // 故障现象选择项
                    SelectionItem(
                        title = "故障现象",
                        value = selectedAlarm?.alarmType ?: "",
                        onSelect = { 
                            if (selectedDevice != null) {
                                showAlarmSelectorDialog = true
                            } else {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar("请先选择故障设备")
                                }
                            }
                        },
                        isLoading = isAlarmLoading
                    )
    
                    HorizontalDivider()
                    Spacer(modifier = Modifier.height(12.dp))
    
                    // 故障描述输入框
                    TextInputField(
                        title = "故障描述",
                        value = faultDescription,
                        onValueChange = { viewModel.setFaultDescription(it) },
                        placeholder = "请输入故障描述"
                    )
    
                    HorizontalDivider()
                    Spacer(modifier = Modifier.height(12.dp))
    
                    // 维修建议输入框
                    TextInputField(
                        title = "维修建议",
                        value = repairAdvice,
                        onValueChange = { viewModel.setRepairAdvice(it) },
                        placeholder = "维修建议将根据故障现象自动填充",
                        isLoading = isFaultDetailLoading
                    )
    
                    Spacer(modifier = Modifier.height(32.dp))
    
                    // 提交按钮
                    Button(
                        onClick = {
                            viewModel.submitFault(
                                onSuccess = {
                                    coroutineScope.launch {
                                        snackbarHostState.showSnackbar("故障上报成功", duration = SnackbarDuration.Short)
                                        onBackClick()
                                    }
                                },
                                onError = { errorMsg ->
                                    coroutineScope.launch {
                                        snackbarHostState.showSnackbar(errorMsg)
                                    }
                                }
                            )
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        enabled = !isLoading && !isFaultDetailLoading && selectedDevice != null && 
                                   selectedAlarm != null && faultDescription.isNotBlank()
                    ) {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier
                                    .size(24.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                        } else {
                            Text("提交")
                        }
                    }
                }
            }
        }
        
        // Snackbar显示错误信息
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
    
    // 设备选择对话框 - 弹窗中的三级级联选择
    if (showDeviceSelectorDialog) {
        StepByStepDeviceSelector(
            controlAreas = controlAreas,
            selectedControlArea = selectedControlArea,
            deviceTypes = deviceTypes,
            selectedDeviceType = selectedDeviceType,
            devices = devices,
            selectedDevice = selectedDevice,
            onControlAreaSelected = { viewModel.selectControlArea(it) },
            onDeviceTypeSelected = { viewModel.selectDeviceType(it) },
            onDeviceSelected = { viewModel.selectDevice(it) },
            onDismiss = { showDeviceSelectorDialog = false }
        )
    }
    
    // 报警现象选择对话框
    if (showAlarmSelectorDialog) {
        AlarmSelectionDialog(
            title = "选择故障现象",
            alarms = alarmResponses,
            selectedAlarm = selectedAlarm,
            onSelect = { alarm -> 
                viewModel.selectAlarm(alarm)
                showAlarmSelectorDialog = false
            },
            onDismiss = { showAlarmSelectorDialog = false }
        )
    }
}

@Composable
private fun SelectionItem(
    title: String,
    value: String,
    onSelect: () -> Unit,
    isLoading: Boolean = false
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelect() }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.weight(1f))

            if (isLoading) {
                // 显示加载指示器
                CircularProgressIndicator(
                    modifier = Modifier
                        .height(20.dp)
                        .width(20.dp),
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            if (value.isEmpty()) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "选择$title"
                )
            } else {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = value,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = "更改$title"
                    )
                }
            }
        }
    }
}

@Composable
private fun TextInputField(
    title: String,
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String,
    isLoading: Boolean = false
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
        
        if (isLoading) {
            Spacer(modifier = Modifier.width(8.dp))
            CircularProgressIndicator(
                modifier = Modifier
                    .height(20.dp)
                    .width(20.dp),
                strokeWidth = 2.dp
            )
        }
    }

    Spacer(modifier = Modifier.height(8.dp))

    OutlinedTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp),
        placeholder = { Text(placeholder) },
        enabled = !isLoading
    )
}

@Composable
private fun AlarmSelectionDialog(
    title: String,
    alarms: List<AlarmResponse>,
    selectedAlarm: AlarmResponse?,
    onSelect: (AlarmResponse) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = {
            LazyColumn(
                modifier = Modifier.heightIn(max = 300.dp)
            ) {
                items(alarms) { alarm ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { 
                                onSelect(alarm)
                            }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = alarm.alarmId == selectedAlarm?.alarmId,
                            onClick = { 
                                onSelect(alarm)
                            }
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(alarm.alarmType ?: "未知故障现象")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
private fun StepByStepDeviceSelector(
    controlAreas: List<String>,
    selectedControlArea: String?,
    deviceTypes: List<String>,
    selectedDeviceType: String?,
    devices: List<DeviceInfo>,
    selectedDevice: DeviceInfo?,
    onControlAreaSelected: (String) -> Unit,
    onDeviceTypeSelected: (String) -> Unit,
    onDeviceSelected: (DeviceInfo) -> Unit,
    onDismiss: () -> Unit
) {
    // 当前选择步骤
    var currentStep by remember { mutableStateOf(
        when {
            selectedControlArea == null -> 0 // 控区选择
            selectedDeviceType == null -> 1 // 设备类型选择
            selectedDevice == null -> 2 // 设备选择
            else -> 0 // 默认从控区选择开始
        }
    ) }
    
    // 获取当前步骤的标题
    val stepTitle = when(currentStep) {
        0 -> "选择控区"
        1 -> "选择设备类型"
        2 -> "选择设备"
        else -> "选择设备"
    }
    
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
            ) {
                // 标题区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stepTitle,
                        style = MaterialTheme.typography.titleLarge
                    )
                    
                    // 显示当前选择的路径
                    if (currentStep > 0) {
                        Spacer(modifier = Modifier.width(16.dp))
                        Text(
                            text = "控区: ${selectedControlArea ?: ""}" + 
                                    if (currentStep > 1 && selectedDeviceType != null) " > 类型: $selectedDeviceType" else "",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                // 选择列表
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp)
                        )
                ) {
                    when (currentStep) {
                        // 控区选择
                        0 -> {
                            LazyColumn {
                                items(controlAreas) { area ->
                                    StepSelectionItem(
                                        text = area,
                                        isSelected = area == selectedControlArea,
                                        onClick = { 
                                            onControlAreaSelected(area)
                                            currentStep = 1 // 进入下一步
                                        }
                                    )
                                }
                            }
                        }
                        // 设备类型选择
                        1 -> {
                            LazyColumn {
                                items(deviceTypes) { type ->
                                    StepSelectionItem(
                                        text = type,
                                        isSelected = type == selectedDeviceType,
                                        onClick = { 
                                            onDeviceTypeSelected(type)
                                            currentStep = 2 // 进入下一步
                                        }
                                    )
                                }
                            }
                        }
                        // 设备选择
                        2 -> {
                            LazyColumn {
                                items(devices) { device ->
                                    StepSelectionItem(
                                        text = device.name ?: "未命名设备",
                                        isSelected = device.id == selectedDevice?.id,
                                        onClick = { 
                                            onDeviceSelected(device)
                                            onDismiss() // 选择完毕后关闭对话框
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 按钮区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // 返回按钮 - 只在非第一步时显示
                    if (currentStep > 0) {
                        TextButton(
                            onClick = { 
                                currentStep--
                                // 返回上一步时清除当前步骤的选择
                                if (currentStep == 0) {
                                    // 清除设备类型和设备选择
                                    onDeviceTypeSelected("")
                                } else if (currentStep == 1) {
                                    // 清除设备选择
                                    onDeviceSelected(DeviceInfo())
                                }
                            }
                        ) {
                            Text("返回上一步")
                        }
                    } else {
                        // 占位，保持布局平衡
                        Spacer(modifier = Modifier.width(100.dp))
                    }
                    
                    // 取消按钮
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                }
            }
        }
    }
}

@Composable
private fun StepSelectionItem(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(
                if (isSelected) MaterialTheme.colorScheme.primaryContainer
                else Color.Transparent
            )
            .padding(vertical = 12.dp, horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = if (isSelected) MaterialTheme.colorScheme.primary
                else MaterialTheme.colorScheme.onSurface
        )
    }
}



