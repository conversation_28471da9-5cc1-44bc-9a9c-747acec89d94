package com.crrc.siom.ui.dashboard.groupInfo

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.crrc.common.bean.response.GroupMemberInfoResponse
import com.crrc.common.bean.response.UserInfoResponse
import com.crrc.siom.data.repository.UserRepository
import com.crrc.siom.data.repository.UserRepositoryImpl

class GroupInfoViewModel(groupId: String) : ViewModel() {


    private val userRepository: UserRepository = UserRepositoryImpl()

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _groupMemberInfo = MutableLiveData<List<GroupMemberInfoResponse>?>()
    val groupMemberInfo: LiveData<List<GroupMemberInfoResponse>?> = _groupMemberInfo
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    init {
        loadGroupMemberInfo(groupId)
    }
    /**
     * 加载用户信息
     */
    fun loadGroupMemberInfo(groupId: String) {

        if (groupId.isEmpty()) {
            _errorMessage.value = "用户ID为空,无用户信息"
            return
        }
        
        _isLoading.value = true
        _errorMessage.value = null
        
        userRepository.loadGroupMemberInfo(groupId) { members, errorMsg ->
            _isLoading.value = false
            Log.d("members",members.toString())
            if (members != null) {
                _groupMemberInfo.value = members
            } else {
                _errorMessage.value = errorMsg ?: "获取用户信息失败"
            }
        }
    }

}

class GroupInfoViewModelFactory(private val userId: String) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(GroupInfoViewModel::class.java)) {
            return GroupInfoViewModel(userId) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}