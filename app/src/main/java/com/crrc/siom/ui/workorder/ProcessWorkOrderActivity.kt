package com.crrc.siom.ui.workorder

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.crrc.siom.ui.theme.SiomTheme
import com.crrc.siom.ui.workorder.pages.process.CaseDetailPage
import com.crrc.siom.ui.workorder.pages.process.RepairProcessPage
import com.crrc.siom.ui.workorder.pages.process.ExpertGroupPage
import com.crrc.siom.ui.workorder.pages.process.CaseLibraryPage
import com.crrc.siom.ui.workorder.viewmodel.process.CaseLibraryViewModel
import kotlinx.coroutines.launch

class ProcessWorkOrderActivity : ComponentActivity() {
    companion object {
        private const val EXTRA_WORK_ORDER_ID = "work_order_id"
        
        fun start(context: Context, workOrderId: String) {
            context.startActivity(
                Intent(context, ProcessWorkOrderActivity::class.java).apply {
                    putExtra(EXTRA_WORK_ORDER_ID, workOrderId)
                }
            )
        }
    }

    @OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val workOrderId = intent.getStringExtra(EXTRA_WORK_ORDER_ID)
            ?: throw IllegalArgumentException("Work order id is required")

        val caseLibraryViewModel = viewModels<CaseLibraryViewModel>().value

        setContent {
            SiomTheme {
                val tabs = listOf("维修流程", "专家群", "案例库")
                val pagerState = rememberPagerState(pageCount = { tabs.size })
                val scope = rememberCoroutineScope()
                
                val caseUiState by caseLibraryViewModel.uiState.collectAsState()
                
                Scaffold(
                    topBar = {
                        Column {
                            TopAppBar(
                                title = { Text("故障处置") },
                                navigationIcon = {
                                    IconButton(onClick = { finish() }) {
                                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                                    }
                                }
                            )
                            
                            TabRow(
                                selectedTabIndex = pagerState.currentPage,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                tabs.forEachIndexed { index, title ->
                                    Tab(
                                        selected = pagerState.currentPage == index,
                                        onClick = {
                                            scope.launch {
                                                pagerState.animateScrollToPage(index)
                                            }
                                        },
                                        text = { Text(title) }
                                    )
                                }
                            }
                        }
                    }
                ) { paddingValues ->
                    HorizontalPager(
                        state = pagerState,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(paddingValues)
                    ) { page ->
                        when (page) {
                            0 -> RepairProcessPage(
                                workOrderId = workOrderId,
                                onBackClick = { finish() },
                                modifier = Modifier.fillMaxSize()
                            )
                            1 -> ExpertGroupPage(
                                workOrderId = workOrderId,
                                modifier = Modifier.fillMaxSize()
                            )
                            2 -> {
                                if (caseUiState.selectedCaseId != null) {
                                    CaseDetailPage(
                                        caseId = caseUiState.selectedCaseId!!,
                                        onBack = { caseLibraryViewModel.clearSelectedCase() }
                                    )
                                } else {
                                    CaseLibraryPage(
                                        workOrderId = workOrderId
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
} 