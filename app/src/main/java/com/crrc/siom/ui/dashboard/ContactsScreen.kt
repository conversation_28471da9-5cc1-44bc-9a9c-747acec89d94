package com.crrc.siom.ui.dashboard

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.Constant.EXPERT_GROUP_TYPE
import com.crrc.common.Constant.GROUP_TYPE
import com.crrc.common.Constant.USER_TYPE

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactsScreen(
    viewModel: DashboardViewModel = viewModel(),
    onChatClick: (String, String, Int) -> Unit
) {
    val userContacts by viewModel.userContacts.collectAsState()
    val groupContacts by viewModel.groupContacts.collectAsState()
    val expertGroupContacts by viewModel.expertGroupContacts.collectAsState()

    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    var showSearchDialog by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }

    Column(modifier = Modifier.fillMaxSize()) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (error != null) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = error ?: "",
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.padding(16.dp)
                    )
                    Button(onClick = { viewModel.refresh() }) {
                        Text("重试")
                    }
                }
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize()
            ) {
                // 联系人分组
                item {
                    ContactGroup(title = "联系人") {
                        userContacts.forEach { user ->
                            ContactItem(
                                name = user.realName,
                                onClick = {
                                    onChatClick(user.id,user.realName,USER_TYPE)
                                })
                        }
                    }
                }
                // 群聊分组
                item {
                    ContactGroup(title = "群聊") {
                        groupContacts.forEach { group ->
                            ContactItem(
                                name = group.name,
                                onClick = {
                                    onChatClick(group.id,group.name,GROUP_TYPE)
                                })
                        }
                    }
                }

                //专家群聊分组
                item {
                    ContactGroup(title = "专家群聊") {
                        expertGroupContacts.forEach { expertGroup ->
                            ContactItem(
                                name = expertGroup.name,
                                onClick = {
                                    onChatClick(expertGroup.id,expertGroup.name,EXPERT_GROUP_TYPE)
                                })
                        }
                    }
                }
                // 总数
                item {
                    val totalContacts = groupContacts.size + userContacts.size + expertGroupContacts.size
                    Text(
                        text = "${totalContacts}位联系人",
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }

    if (showSearchDialog) {
        AlertDialog(
            onDismissRequest = { showSearchDialog = false },
            title = { Text("搜索联系人") },
            text = {
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("输入联系人姓名") },
                    leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
                    singleLine = true
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        viewModel.searchContacts(searchQuery)
                        showSearchDialog = false
                    }
                ) {
                    Text("搜索")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showSearchDialog = false
                        searchQuery = ""
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun ContactGroup(
    title: String,
    expanded: Boolean = true,
    content: @Composable ColumnScope.() -> Unit = {}
) {
    var isExpanded by remember { mutableStateOf(expanded) }

    Column {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            onClick = { isExpanded = !isExpanded }
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium
                )
                Icon(
                    imageVector = if (isExpanded) Icons.Default.KeyboardArrowDown else Icons.Default.KeyboardArrowRight,
                    contentDescription = if (isExpanded) "收起" else "展开"
                )
            }
        }
        if (isExpanded) {
            Column {
                content()
            }
        }
    }
}

@Composable
private fun ContactItem(
    name: String,
    onClick: () -> Unit = {}
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = name)
        }
    }
} 