package com.crrc.siom.ui.dashboard.detail

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.Constant.EXPERT_GROUP_TYPE
import com.crrc.common.Constant.GROUP_TYPE
import com.crrc.common.utils.ToastUtil
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.model.ChatInfo
import com.crrc.siom.data.model.Message
import com.crrc.siom.data.model.ChatMessage
import com.crrc.siom.data.model.GroupChatMessage
import com.crrc.siom.data.model.HistoryMessage
import com.crrc.siom.data.model.HistoryMessageResponse
import com.crrc.siom.data.model.WsMessage
import com.crrc.siom.data.repository.ContactRepository
import com.crrc.siom.data.repository.ContactRepositoryImpl
import com.crrc.siom.service.WebSocketService
import com.crrc.siom.manager.ChatMessageCenter
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
class ChatDetailViewModel : ViewModel() {
    private val repository: ContactRepository = ContactRepositoryImpl()

    private val _conversationId = MutableStateFlow<String>("")
    val conversationId: StateFlow<String> = _conversationId

    private val _chatMessages = MutableStateFlow<List<WsMessage>>(emptyList())
    val chatMessages: StateFlow<List<WsMessage>> = _chatMessages

    private var chatBinder: WebSocketService.ChatBinder? = null

    // 分页相关字段
    private var lastHistoryMessageId: String? = null
    var isLoadingHistory = false
        private set
    var hasMoreHistory = true
        private set

    init {
        viewModelScope.launch {
            ChatMessageCenter.newMessageFlow.collect { message ->
                when(message){
                    is ChatMessage, is GroupChatMessage -> {
                        _chatMessages.value = listOf(message) + _chatMessages.value
                    }
                    is HistoryMessageResponse -> {
                        val historyList = message.messageList
                        if (historyList.isNotEmpty()) {
                            lastHistoryMessageId = historyList.first().id
                            _chatMessages.value = _chatMessages.value + message
                            hasMoreHistory = message.hasMore
                        } else {
                            hasMoreHistory = false
                        }
                        isLoadingHistory = false
                    }
                    else -> {
                        // 处理其他类型的消息
                    }
                }
            }
        }
    }

    private fun tryLoadHistoryIfReady() {
        if (chatBinder != null && _conversationId.value.isNotBlank() && _chatMessages.value.isEmpty()) {
            getHistoryMessage()
        }
    }

    fun setChatBinder(binder: WebSocketService.ChatBinder?) {
        chatBinder = binder
        tryLoadHistoryIfReady()
    }

    fun setConversationId(id: String) {
        _conversationId.value = id
        tryLoadHistoryIfReady()
    }

    // 发送消息
    fun sendMessage(content: String,chatType: Int) {
        if (content.isBlank()) return
        val conversationId = _conversationId.value
        val senderId = SessionManager().getUserId().toString()
        val replyTo: String? = null

        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val time = sdf.format(Date())
        val messageList = listOf(
            Message(
                type = "text",
                text = content,
                id = "",
                conversationId = conversationId,
                senderId = senderId,
                senderName = "",
                attachments = "",
                time = time,
                isRecalled = "",
                replyTo = replyTo.toString(),
            )
        )
        val chatMessageType = when (chatType) {
            0 -> "SINGLE_SENDING"
            else -> "GROUP_SENDING"    //1,2
        }

        val chatMessage = ChatMessage(
            type = chatMessageType,
            conversationId = conversationId,
            messageList = messageList,
        )
        chatBinder?.sendSingleMessage(chatMessage)
        _chatMessages.value = listOf(chatMessage) + _chatMessages.value
    }

    fun getHistoryMessage(){
        if (isLoadingHistory || !hasMoreHistory) return
        isLoadingHistory = true
        chatBinder?.sendHistoryMessage(
            HistoryMessage(
                type = "HISTORY_MESSAGE",
                userId = SessionManager().getUserId().toString(),
                conversationId = _conversationId.value,
                limit = 14,
                lastMessageId = lastHistoryMessageId
            )
        )
    }

    fun getConversation(type: Int, initiatorUserId: String, targetId: String, ) {
        if (type == GROUP_TYPE || type == EXPERT_GROUP_TYPE){
            Log.d("type",targetId.toString())
            setConversationId(targetId)
            return
        }
        repository.getConversation(type, initiatorUserId, targetId) { conversationId, error ->
            if (error != null) {
                ToastUtil.show(error)
            } else {
                setConversationId(conversationId.toString())
            }
        }
    }

}