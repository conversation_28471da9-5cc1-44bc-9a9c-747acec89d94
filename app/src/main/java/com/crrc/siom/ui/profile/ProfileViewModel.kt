package com.crrc.siom.ui.profile

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.UserInfoResponse
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.repository.UserRepository
import com.crrc.siom.data.repository.UserRepositoryImpl
import kotlinx.coroutines.launch

class ProfileViewModel : ViewModel() {
    private val userRepository: UserRepository = UserRepositoryImpl()
    private val sessionManager: SessionManager = SessionManager()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _userInfo = MutableLiveData<UserInfoResponse?>()
    val userInfo: LiveData<UserInfoResponse?> = _userInfo
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private  val _avatarBitmap = MutableLiveData<Bitmap?>()
    val avatarBitmap: LiveData<Bitmap?> = _avatarBitmap

    init {
        loadUserInfo()
    }
    
    /**
     * 加载用户信息
     */
    fun loadUserInfo() {
        val userId = sessionManager.getUserId()
        
        if (userId.isNullOrEmpty()) {
            _errorMessage.value = "用户ID为空，请重新登录"
            return
        }
        
        _isLoading.value = true
        _errorMessage.value = null
        
        userRepository.getUserById(userId) { userInfoResponse, errorMsg ->
            _isLoading.value = false
            if (userInfoResponse != null) {
                _userInfo.value = userInfoResponse
            } else {
                _errorMessage.value = errorMsg ?: "获取用户信息失败"
            }
        }
    }
    
    /**
     * 上传用户头像
     * @param avatarPath 头像文件路径
     */
    fun uploadAvatar(avatarPath: String) {
        val userId = sessionManager.getUserId()
        
        if (userId.isNullOrEmpty()) {
            _errorMessage.value = "用户ID为空，请重新登录"
            return
        }
        
        _isLoading.value = true
        
        userRepository.loadUserAvatar(avatarPath, userId) { success, errorMsg ->
            _isLoading.value = false
            if (success == true) {
                loadUserInfo()
            } else {
                _errorMessage.value = errorMsg ?: "上传头像失败"
            }
        }
    }

     fun getUserAvatar(){
        val userId = sessionManager.getUserId()
        if (userId.isNullOrEmpty()) {
            _errorMessage.value = "用户ID为空，请重新登录"
            return
        }
        _isLoading.value = true

        viewModelScope.launch {
            try {
                val bytes = userRepository.getUserAvatarSuspend(userId)
                if (bytes != null) {
                    _isLoading.value = false
                    val bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
                    _avatarBitmap.value = bitmap
                }
            } catch (e: Exception) {
                _isLoading.value = false
                Log.e("Avatar", "加载头像失败：${e.message}")
            }
        }
    }

}