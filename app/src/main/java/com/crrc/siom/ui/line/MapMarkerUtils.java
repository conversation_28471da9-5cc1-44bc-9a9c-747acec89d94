package com.crrc.siom.ui.line;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.GradientDrawable;
import android.util.Log;

import androidx.core.content.ContextCompat;

/**
 * 地图标记工具类
 * 提供创建各种地图标记和图像相关的工具方法
 */
public class MapMarkerUtils {
    private static final String TAG = "MapMarkerUtils";
    
    /**
     * 从资源ID创建Bitmap（支持XML drawable）
     */
    public static Bitmap createBitmapFromResource(Context context, int resourceId) {
        try {
            android.graphics.drawable.Drawable drawable = ContextCompat.getDrawable(context, resourceId);
            if (drawable == null) {
                Log.e(TAG, "无法加载资源ID: " + resourceId);
                return null;
            }
            
            drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            Bitmap bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(), 
                                              drawable.getIntrinsicHeight(),
                                              Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            drawable.draw(canvas);
            
            return bitmap;
        } catch (Exception e) {
            Log.e(TAG, "创建Bitmap失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 创建带有文字的气泡位图
     */
    public static Bitmap createTextBubbleBitmap(String text, Bitmap bubbleBackground) {
        if (bubbleBackground == null) {
            return null;
        }
        
        Bitmap result = bubbleBackground.copy(Bitmap.Config.ARGB_8888, true);
        Canvas canvas = new Canvas(result);
        
        Paint textPaint = new Paint();
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(30);
        textPaint.setAntiAlias(true);
        textPaint.setTextAlign(Paint.Align.CENTER);
        
        float x = result.getWidth() / 2f;
        float y = result.getHeight() / 2f - ((textPaint.descent() + textPaint.ascent()) / 2);
        
        canvas.drawText(text, x, y, textPaint);
        
        return result;
    }
    
    /**
     * 创建小圆点位图
     */
    public static Bitmap createDotBitmap(Context context) {
        GradientDrawable dotDrawable = new GradientDrawable();
        dotDrawable.setShape(GradientDrawable.OVAL);
        dotDrawable.setColor(Color.WHITE);
        dotDrawable.setStroke(2, Color.RED);
        
        int sizePx = (int) (8 * context.getResources().getDisplayMetrics().density);
        dotDrawable.setSize(sizePx, sizePx);
        
        Bitmap bitmap = Bitmap.createBitmap(sizePx, sizePx, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        dotDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        dotDrawable.draw(canvas);
        
        return bitmap;
    }
} 