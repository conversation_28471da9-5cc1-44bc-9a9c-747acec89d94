package com.crrc.siom.ui.workorder

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.SearchResponse
import com.crrc.siom.ui.components.SearchField
import com.crrc.siom.ui.theme.SiomTheme
import com.crrc.siom.ui.workorder.pages.list.PlannedWorkOrderPage
import com.crrc.siom.ui.workorder.pages.list.FaultWorkOrderPage
import com.crrc.siom.ui.workorder.pages.list.EmergencyWorkOrderPage
import com.crrc.siom.ui.workorder.pages.list.TemporaryWorkOrderPage
import com.crrc.siom.ui.workorder.viewmodel.WorkOrderViewModel
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import kotlinx.coroutines.launch

class WorkOrderActivity : ComponentActivity() {

    companion object {
        private const val INITIAL_PAGE = "initial_page"

        // 页面类型常量
        const val PAGE_PLANNED = 0
        const val PAGE_FAULT = 1
        const val PAGE_EMERGENCY = 2
        const val PAGE_TEMPORARY = 3

        fun start(context: Context, page: Int) {
            val intent = Intent(context, WorkOrderActivity::class.java).apply {
                putExtra(INITIAL_PAGE, page)
            }
            context.startActivity(intent)
        }
    }

    @OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val initialPage = intent.getIntExtra(INITIAL_PAGE, 0)

        setContent {
            SiomTheme {
                WorkOrderContent(initialPage) {
                    setResult(RESULT_OK)
                    finish()
                }
            }
        }
    }

    @OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
    @Composable
    fun WorkOrderContent(
        initialPage: Int,
        onBackClick: () -> Unit
    ) {
        val viewModel: WorkOrderViewModel = viewModel()
        val isTempOnly = initialPage == PAGE_TEMPORARY

        val pagerState = rememberPagerState(
            initialPage = if (isTempOnly) 0 else initialPage,
            pageCount = { if (isTempOnly) 1 else 3 }
        )
        val scope = rememberCoroutineScope()
        var searchQuery by remember { mutableStateOf("") }

        // 收集工单数量数据
        val workOrderCount by viewModel.workOrderCount.collectAsState()
        val isLoading by viewModel.isLoading.collectAsState()
        val error by viewModel.error.collectAsState()

        // 监听页面切换（包括滑动切换），每次切换时重新加载工单数量
        LaunchedEffect(pagerState.currentPage) {
            viewModel.loadWorkOrderCount()
        }

        Column(modifier = Modifier.fillMaxSize()) {
            TopAppBar(
                title = { Text("工单列表") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
//                SearchField(
//                    value = searchQuery,
//                    onValueChange = { searchQuery = it },
//                    onSearch = { }
//                )

                SearchBoxWithDropdown(viewModel = viewModel)


                if (isTempOnly) {
                    // 临时工单模式只显示一个标签
                    val tempCount = workOrderCount?.temporaryCount ?: 0

                    TabRow(
                        selectedTabIndex = 0,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Tab(
                            selected = true,
                            onClick = { },
                            modifier = Modifier.weight(1f),
                            text = { Text("临时工单($tempCount)") }
                        )
                    }
                } else {
                    // 普通模式显示三个标签
                    TabRow(
                        selectedTabIndex = pagerState.currentPage,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        val planCount = workOrderCount?.planCount ?: 0
                        val faultCount = workOrderCount?.faultCount ?: 0
                        val emergencyCount = workOrderCount?.emergencyCount ?: 0

                        listOf(
                            "计划工单($planCount)" to PAGE_PLANNED,
                            "故障工单($faultCount)" to PAGE_FAULT,
                            "应急工单($emergencyCount)" to PAGE_EMERGENCY
                        ).forEachIndexed { index, (title, _) ->
                            Tab(
                                selected = pagerState.currentPage == index,
                                onClick = {
                                    scope.launch {
                                        pagerState.animateScrollToPage(index)
                                    }
                                },
                                modifier = Modifier.weight(1f),
                                text = { Text(title) }
                            )
                        }
                    }
                }

                when {
                    isLoading -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(4.dp)
                        ) {
                            LinearProgressIndicator(modifier = Modifier.fillMaxWidth())
                        }
                    }

                    error != null -> {
                        Text(
                            text = "加载工单数量失败",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(4.dp)
                        )
                    }
                }

                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier.fillMaxSize(),
                    key = { it }
                ) { page ->
                    if (isTempOnly) {
                        TemporaryWorkOrderPage()
                    } else {
                        when (page) {
                            0 -> PlannedWorkOrderPage()
                            1 -> FaultWorkOrderPage()
                            2 -> EmergencyWorkOrderPage()
                        }
                    }
                }
            }
        }
    }
}


@Composable
fun SearchResultList(
    result: SearchResponse,
    onOrderClick: (SearchResponse.Order) -> Unit = {},
) {
    result.orderList?.takeIf { it.isNotEmpty() }?.let { orders ->
        DropdownMenuGroup(
            title = "工单",
            items = orders,
            onItemClick = onOrderClick,
            itemText = { order -> "${order.orderTitle} (ID: ${order.orderId})" }
        )
    }
}

@Composable
fun <T> DropdownMenuGroup(
    title: String,
    items: List<T>,
    onItemClick: (T) -> Unit,
    itemText: @Composable (T) -> String,
    modifier: Modifier = Modifier
) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleSmall,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 6.dp)
    )

    items.forEach { item ->
        Row(
            modifier = modifier
                .fillMaxWidth()
                .clickable { onItemClick(item) }
                .padding(horizontal = 16.dp, vertical = 4.dp), // 控制紧凑程度
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = itemText(item),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class, ExperimentalMaterial3Api::class)
@Composable
private fun SearchBoxWithDropdown(viewModel: WorkOrderViewModel) {
    val context = LocalContext.current
    val searchResults by viewModel.searchResults.collectAsState()
    var input by remember { mutableStateOf("") }
    var expanded by remember { mutableStateOf(false) }
    val keyboardController = LocalSoftwareKeyboardController.current
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { shouldExpand ->
            if (shouldExpand && input.isNotBlank() && hasResults(searchResults)) {
                expanded = true
            } else if (!shouldExpand) {
                expanded = false
            }
        }
    ) {
        SearchField(
            value = input,
            onValueChange = {
                input = it
            },
            modifier = Modifier
                .menuAnchor()
                .fillMaxWidth(),
            keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Search),
            keyboardActions = KeyboardActions(
                onSearch = {
                    viewModel.searchAll(input)
                    keyboardController?.hide()
                    expanded =
                        input.isNotBlank() && searchResults != null && hasResults(searchResults)
                }
            )
        )
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = {
                expanded = false
            },
            modifier = Modifier.background(MaterialTheme.colorScheme.primaryContainer)
        ) {
            if (searchResults != null) {
                SearchResultList(
                    result = searchResults!!,
                    onOrderClick = {
                        WorkOrderDetailActivity.start(context, it.orderId)
                        Toast.makeText(
                            context,
                            "跳转到工单详情: ${it.orderId}",
                            Toast.LENGTH_SHORT
                        ).show()
                        expanded = false
                    },
                )
            }
        }
    }
}

// 判断是否有检索结果
private fun hasResults(searchResults: SearchResponse?): Boolean {
    return searchResults != null &&
            searchResults.orderList?.isNotEmpty() == true
}