package com.crrc.siom.ui.submitFault

import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.AlarmResponse
import com.crrc.common.bean.response.DeviceInfo
import com.crrc.siom.data.repository.SubmitFaultRepository
import com.crrc.siom.data.repository.SubmitFaultRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class SubmitFaultViewModel : ViewModel() {
    private val repository: SubmitFaultRepository = SubmitFaultRepositoryImpl()
    
    // 设备信息
    private val _deviceData = MutableStateFlow<Map<String, Map<String, List<DeviceInfo>>>?>(null)
    val deviceData: StateFlow<Map<String, Map<String, List<DeviceInfo>>>?> = _deviceData.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 已选择的控区ID
    private val _selectedControlArea = MutableStateFlow<String?>(null)
    val selectedControlArea: StateFlow<String?> = _selectedControlArea.asStateFlow()
    
    // 控区列表（从设备数据中提取）
    private val _controlAreas = MutableStateFlow<List<String>>(emptyList())
    val controlAreas: StateFlow<List<String>> = _controlAreas.asStateFlow()
    
    // 已选择的设备类型
    private val _selectedDeviceType = MutableStateFlow<String?>(null)
    val selectedDeviceType: StateFlow<String?> = _selectedDeviceType.asStateFlow()
    
    // 设备类型列表（从设备数据中提取，基于选择的控区）
    private val _deviceTypes = MutableStateFlow<List<String>>(emptyList())
    val deviceTypes: StateFlow<List<String>> = _deviceTypes.asStateFlow()
    
    // 已选择的设备
    private val _selectedDevice = MutableStateFlow<DeviceInfo?>(null)
    val selectedDevice: StateFlow<DeviceInfo?> = _selectedDevice.asStateFlow()
    
    // 设备列表（从设备数据中提取，基于选择的控区和设备类型）
    private val _devices = MutableStateFlow<List<DeviceInfo>>(emptyList())
    val devices: StateFlow<List<DeviceInfo>> = _devices.asStateFlow()
    
    // 报警现象列表
    private val _alarmResponses = MutableStateFlow<List<AlarmResponse>>(emptyList())
    val alarmResponses: StateFlow<List<AlarmResponse>> = _alarmResponses.asStateFlow()
    
    // 已选择的报警现象
    private val _selectedAlarm = MutableStateFlow<AlarmResponse?>(null)
    val selectedAlarm: StateFlow<AlarmResponse?> = _selectedAlarm.asStateFlow()
    
    // 报警类型加载状态
    private val _isAlarmLoading = MutableStateFlow(false)
    val isAlarmLoading: StateFlow<Boolean> = _isAlarmLoading.asStateFlow()
    
    // 故障详情加载状态
    private val _isFaultDetailLoading = MutableStateFlow(false)
    val isFaultDetailLoading: StateFlow<Boolean> = _isFaultDetailLoading.asStateFlow()
    
    // 故障描述（用户手写）
    private val _faultDescription = MutableStateFlow("")
    val faultDescription: StateFlow<String> = _faultDescription.asStateFlow()
    
    // 维修建议（从API获取）
    private val _repairAdvice = MutableStateFlow("")
    val repairAdvice: StateFlow<String> = _repairAdvice.asStateFlow()
    
    /**
     * 加载设备信息
     */
    fun loadDeviceData() {
        _isLoading.value = true
        _error.value = null
        
        repository.device { deviceResponse, errorMsg ->
            _isLoading.value = false
            
            if (deviceResponse != null) {
                _deviceData.value = deviceResponse
                
                // 提取并设置控区列表
                val areas = deviceResponse.keys.toList()
                _controlAreas.value = areas
            } else {
                _error.value = errorMsg ?: "获取设备信息失败"
            }
        }
    }
    
    /**
     * 选择控区
     */
    fun selectControlArea(controlAreaId: String) {
        _selectedControlArea.value = controlAreaId
        _selectedDeviceType.value = null
        _selectedDevice.value = null
        _selectedAlarm.value = null
        _alarmResponses.value = emptyList()
        _repairAdvice.value = ""
        
        // 当选择了控区后，更新设备类型列表
        val deviceData = _deviceData.value
        if (deviceData != null && deviceData.containsKey(controlAreaId)) {
            val deviceTypes = deviceData[controlAreaId]?.keys?.toList() ?: emptyList()
            _deviceTypes.value = deviceTypes
        } else {
            _deviceTypes.value = emptyList()
        }
        
        // 清空设备列表
        _devices.value = emptyList()
    }
    
    /**
     * 选择设备类型
     */
    fun selectDeviceType(deviceType: String) {
        _selectedDeviceType.value = deviceType
        _selectedDevice.value = null
        _selectedAlarm.value = null
        _alarmResponses.value = emptyList()
        _repairAdvice.value = ""
        
        // 当选择了设备类型后，更新设备列表
        val deviceData = _deviceData.value
        val controlAreaId = _selectedControlArea.value
        
        if (deviceData != null && controlAreaId != null && 
            deviceData.containsKey(controlAreaId) && 
            deviceData[controlAreaId]?.containsKey(deviceType) == true) {
            
            val devices = deviceData[controlAreaId]?.get(deviceType) ?: emptyList()
            _devices.value = devices
        } else {
            _devices.value = emptyList()
        }
    }
    
    /**
     * 选择设备
     */
    fun selectDevice(device: DeviceInfo) {
        _selectedDevice.value = device
        _selectedAlarm.value = null
        _repairAdvice.value = ""
        // 当选择设备后，加载该设备对应的报警类型
        device.type?.let { loadAlarmTypes(it) }
    }
    
    /**
     * 加载报警类型
     */
    private fun loadAlarmTypes(type: Int) {
        _isAlarmLoading.value = true
        _error.value = null
        
        repository.alarmInfo(type) { alarmResponses, errorMsg ->
            _isAlarmLoading.value = false
            
            if (alarmResponses != null) {
                _alarmResponses.value = alarmResponses
            } else {
                _error.value = errorMsg ?: "获取报警现象失败"
                _alarmResponses.value = emptyList()
            }
        }
    }
    
    /**
     * 选择报警现象
     */
    fun selectAlarm(alarm: AlarmResponse) {
        _selectedAlarm.value = alarm
        _repairAdvice.value = ""
        
        // 当选择报警现象后，根据alarmId加载故障详情
        alarm.alarmId?.let { loadFaultDetail(it) }
    }
    
    /**
     * 加载故障详情
     */
    private fun loadFaultDetail(alarmId: String) {
        _isFaultDetailLoading.value = true
        _error.value = null
        
        repository.faultDetail(alarmId) { faultDetail, errorMsg ->
            _isFaultDetailLoading.value = false
            
            if (faultDetail != null) {
                faultDetail.repairSuggestion?.let {
                    _repairAdvice.value = it
                }
            } else {
                _error.value = errorMsg ?: "获取故障详情失败"
            }
        }
    }
    
    /**
     * 设置故障描述
     */
    fun setFaultDescription(description: String) {
        _faultDescription.value = description
    }
    
    /**
     * 设置维修建议
     */
    fun setRepairAdvice(advice: String) {
        _repairAdvice.value = advice
    }

    /**
     * 提交故障
     */
    fun submitFault(onSuccess: () -> Unit, onError: (String) -> Unit) {
        val device = _selectedDevice.value
        val alarmId = _selectedAlarm.value?.alarmId
        val faultDesc = _faultDescription.value
        val repairSugg = _repairAdvice.value

        // 参数验证
        if (device == null || device.id == null) {
            onError("请选择故障设备")
            return
        }

        if (alarmId.isNullOrEmpty()) {
            onError("请选择故障现象")
            return
        }

        if (faultDesc.isEmpty()) {
            onError("请输入故障描述")
            return
        }

        // 提交
        _isLoading.value = true
        repository.submitFault(
            deviceId = device.id,
            alarmId = alarmId,
            faultDescription = faultDesc,
            repairSuggestion = repairSugg,
            callback = { success, errorMsg ->
                _isLoading.value = false

                if (success == true) {
                    // 提交成功，重置表单
                    resetForm()
                    onSuccess()
                } else {
                    onError(errorMsg ?: "提交失败，请稍后重试")
                }
            }
        )
    }

    /**
     * 重置表单
     */
    private fun resetForm() {
        _selectedControlArea.value = null
        _selectedDeviceType.value = null
        _selectedDevice.value = null
        _selectedAlarm.value = null
        _alarmResponses.value = emptyList()
        _faultDescription.value = ""
        _repairAdvice.value = ""
        _devices.value = emptyList()
        _deviceTypes.value = emptyList()
    }
}
