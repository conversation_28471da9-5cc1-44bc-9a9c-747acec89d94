package com.crrc.siom.ui.line;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.widget.ViewPager2;

import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdate;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.Polyline;
import com.amap.api.maps.model.PolylineOptions;
import com.crrc.siom.R;
import com.crrc.siom.data.model.AlarmInfo;
import com.crrc.siom.data.model.Station;
import com.crrc.siom.data.model.StorageItem;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 线路地图页面（Java版）
 */
public class LineMapJavaActivity extends AppCompatActivity {

    private static final String TAG = "LineMapJavaActivity";

    // UI组件
    private MapView mapView;
    private AMap aMap;
    private TextView tvCurrentStation;
    private ProgressBar progressBar;
    private TextView tvError;
    private Button btnStore;
    private Button btnAlarm;
    
    // 地图相关
    private MapMarkerManager markerManager;
    
    // 详情面板
    private DetailPanelManager detailPanelManager;
    
    // ViewModel
    private LineMapJavaViewModel viewModel;
    
    // 权限
    private final String[] permissions = new String[]{
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    };

    private final ActivityResultLauncher<String[]> permissionLauncher = registerForActivityResult(
            new ActivityResultContracts.RequestMultiplePermissions(),
            permissions -> {
                boolean allGranted = true;
                for (Boolean granted : permissions.values()) {
                    if (!granted) {
                        allGranted = false;
                        break;
                    }
                }

                if (allGranted) {
                    // 所有权限都已授予，初始化地图
                    initMap();
                } else {
                    // 显示权限说明对话框
                    showPermissionExplanationDialog();
                }
            }
    );

    /**
     * 启动活动
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, LineMapJavaActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_line_map);
        
        // 初始化 ViewModel
        viewModel = new ViewModelProvider(this).get(LineMapJavaViewModel.class);
        
        // 初始化视图
        initViews();
        
        // 设置观察者
        setupObservers();
        
        // 检查和请求权限
        checkAndRequestPermissions(savedInstanceState);
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 设置工具栏
        MaterialToolbar toolbar = findViewById(R.id.toolbar);
        toolbar.setNavigationOnClickListener(v -> finish());
        
        // 获取视图引用
        mapView = findViewById(R.id.map_view);
        tvCurrentStation = findViewById(R.id.tv_current_station);
        progressBar = findViewById(R.id.progress_bar);
        tvError = findViewById(R.id.tv_error);
        btnStore = findViewById(R.id.btn_store);
        btnAlarm = findViewById(R.id.btn_alarm);
        
        // 设置按钮点击事件
        setupButtons();
    }
    
    /**
     * 设置按钮点击事件
     */
    private void setupButtons() {
        btnStore.setOnClickListener(v -> {
            if (markerManager != null) {
                markerManager.setDisplayMode(MarkerDisplayMode.STORE);
                viewModel.setDisplayMode(MarkerDisplayMode.STORE);
                Toast.makeText(this, "显示库房站点", Toast.LENGTH_SHORT).show();
            }
        });
        
        btnAlarm.setOnClickListener(v -> {
            if (markerManager != null) {
                markerManager.setDisplayMode(MarkerDisplayMode.ALARM);
                viewModel.setDisplayMode(MarkerDisplayMode.ALARM);
                Toast.makeText(this, "显示报警站点", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    /**
     * 设置观察者
     */
    private void setupObservers() {
        // 观察加载状态
        viewModel.getIsLoading().observe(this, isLoading -> {
            progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
        });
        
        // 观察错误状态
        viewModel.getError().observe(this, error -> {
            if (error != null) {
                tvError.setVisibility(View.VISIBLE);
                tvError.setText("加载失败: " + error);
            } else {
                tvError.setVisibility(View.GONE);
            }
        });
        
        // 观察当前站点变化
        viewModel.getCurrentStation().observe(this, currentStation -> {
            if (currentStation != null) {
                tvCurrentStation.setText("当前位置: " + currentStation);
                
                // 移动到当前站点
                moveToStation(currentStation);
                
                // 更新标记显示
                if (markerManager != null) {
                    markerManager.updateMarkerDisplay();
                }
            }
        });

        // 观察站点列表，初始加载时创建所有标记
        viewModel.getStations().observe(this, stations -> {
            if (stations != null && !stations.isEmpty() && aMap != null && markerManager != null) {
                markerManager.createAllMarkers(stations, viewModel.getCurrentStation().getValue());
                markerManager.updateMarkerDisplay();
            }
        });
        
        // 观察报警信息
        viewModel.getStationAlarmInfo().observe(this, response -> {
            try {
                if (response != null && detailPanelManager != null && 
                        detailPanelManager.getDetailAdapter() != null && 
                        detailPanelManager.getDetailAdapter().getAlarmFragment() != null) {
                    detailPanelManager.getDetailAdapter().getAlarmFragment().updateAlarmData(response);
                }
            } catch (Exception e) {
                Log.e(TAG, "更新报警数据失败: " + e.getMessage());
            }
        });
        
        // 观察备品备件信息
        viewModel.getLineDeviceInfo().observe(this, response -> {
            try {
                if (response != null && detailPanelManager != null && 
                        detailPanelManager.getDetailAdapter() != null && 
                        detailPanelManager.getDetailAdapter().getStorageFragment() != null) {
                    detailPanelManager.getDetailAdapter().getStorageFragment().updateStorageData(response);
                }
            } catch (Exception e) {
                Log.e(TAG, "更新备品备件数据失败: " + e.getMessage());
            }
        });
        
        // 观察工器具信息
        viewModel.getLineToolsInfo().observe(this, response -> {
            try {
                if (response != null && detailPanelManager != null && 
                        detailPanelManager.getDetailAdapter() != null && 
                        detailPanelManager.getDetailAdapter().getStorageFragment() != null) {
                    detailPanelManager.getDetailAdapter().getStorageFragment().updateStorageData(response);
                }
            } catch (Exception e) {
                Log.e(TAG, "更新工器具数据失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 移动到指定站点
     */
    private void moveToStation(String stationName) {
        if (aMap == null) return;
        
        Station station = findStationByName(stationName);
        if (station != null) {
            aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(
                    new LatLng(station.getX(), station.getY()), 15f));
        }
    }
    
    /**
     * 根据站点名称查找站点
     */
    private Station findStationByName(String stationName) {
        if (stationName == null) return null;
        
        for (Station station : viewModel.getStations().getValue()) {
            if (stationName.equals(station.getName())) {
                return station;
            }
        }
        return null;
    }

    /**
     * 检查和请求权限
     */
    private void checkAndRequestPermissions(Bundle savedInstanceState) {
        boolean allGranted = true;
        
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                allGranted = false;
                break;
            }
        }
        
        if (allGranted) {
            // 已有所有权限，初始化地图
            mapView.onCreate(savedInstanceState);
            initMap();
        } else {
            // 请求权限
            permissionLauncher.launch(permissions);
        }
    }

    /**
     * 显示权限说明对话框
     */
    private void showPermissionExplanationDialog() {
        new MaterialAlertDialogBuilder(this)
                .setTitle("需要权限")
                .setMessage("此应用需要位置权限来显示您在地图上的位置，以及存储权限来保存地图数据。")
                .setPositiveButton("去设置", (dialog, which) -> {
                    // 打开应用设置页面
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(Uri.fromParts("package", getPackageName(), null));
                    startActivity(intent);
                })
                .setNegativeButton("取消", (dialog, which) -> {
                    dialog.dismiss();
                    finish();
                })
                .show();
    }

    /**
     * 初始化地图
     */
    private void initMap() {
        if (aMap == null) {
            aMap = mapView.getMap();
            
            // 配置地图
            aMap.setMapType(AMap.MAP_TYPE_NORMAL);
            aMap.getUiSettings().setZoomControlsEnabled(false);
            aMap.showIndoorMap(false);
            aMap.showMapText(false);
            // 创建标记管理器
            markerManager = new MapMarkerManager(this, aMap);
            
            // 创建详情面板管理器
            detailPanelManager = new DetailPanelManager(this, viewModel);
            
            // 设置自定义信息窗口
            aMap.setInfoWindowAdapter(new MapMarkerManager.CustomInfoWindowAdapter(this));
            
            // 设置标记点击监听器
            setupMarkerClickListener();
            
            // 初始加载所有站点
            if (viewModel.getStations().getValue() != null && !viewModel.getStations().getValue().isEmpty()) {
                markerManager.createAllMarkers(
                        viewModel.getStations().getValue(), 
                        viewModel.getCurrentStation().getValue());
                markerManager.showNormalMarkers();
            }
            
            // 移动视角
            //参数依次是：视角调整区域的中心点坐标、希望调整到的缩放级别、俯仰角0°~45°（垂直与地图时为0）、偏航角 0~360° (正北方为0)
            CameraUpdate mCameraUpdate = CameraUpdateFactory.newCameraPosition(
                    new CameraPosition(new LatLng(36.254480176041895,120.36527681073292),11,15,346));
            aMap.moveCamera(mCameraUpdate);
        }
    }
    
    /**
     * 设置标记点击监听器
     */
    private void setupMarkerClickListener() {
        aMap.setOnMarkerClickListener(marker -> {
            // 检查是否为位置标记（通过tag判断）
            if (marker.getObject() != null) {
                String stationName = marker.getTitle();
                viewModel.onStationClick(stationName);
                
                if ("location".equals(marker.getObject())) {
                    detailPanelManager.showStationDetailPanel(stationName);
                    return true;
                } else if ("alarm".equals(marker.getObject())) {
                    detailPanelManager.showStationDetailPanel(stationName);
                    detailPanelManager.switchToAlarmTab();
                    return true;
                } else if ("store".equals(marker.getObject())) {
                    detailPanelManager.showStationDetailPanel(stationName);
                    detailPanelManager.switchToStorageTab();
                    return true;
                }
            }
            return false;
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mapView != null) {
            mapView.onResume();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mapView != null) {
            mapView.onPause();
        }
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (mapView != null) {
            mapView.onSaveInstanceState(outState);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mapView != null) {
            mapView.onDestroy();
        }
    }
} 