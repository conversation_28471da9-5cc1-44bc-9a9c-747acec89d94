package com.crrc.siom.ui.workorder.pages.process

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.platform.LocalContext
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.PlaybackException
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView.ControllerVisibilityListener
import com.crrc.siom.ui.theme.SiomTheme
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.upstream.DefaultLoadErrorHandlingPolicy
import java.io.File


class VideoPlayerActivity : ComponentActivity() {
    private var player: ExoPlayer? = null

    companion object {
        private const val TAG = "VideoPlayerActivity"
    }

    @OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val videoUrl = intent.getStringExtra("videoUrl")

        if (videoUrl.isNullOrEmpty()) {
            Log.e(TAG, "No video URL provided")
            finish()
            return
        }

        // 解码 URL（如果有编码）
        val decodedUrl = try {
            URLDecoder.decode(videoUrl, StandardCharsets.UTF_8.name())
        } catch (e: Exception) {
            Log.w(TAG, "Failed to decode URL, using original: $videoUrl", e)
            videoUrl
        }

        Log.d(TAG, "Playing video from URL: $decodedUrl")

        setContent {
            SiomTheme {
                VideoPlayerScreen(
                    videoUrl = decodedUrl,
                    onBackPressed = { finish() }
                )
            }
        }
    }

    @androidx.annotation.OptIn(UnstableApi::class)
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun VideoPlayerScreen(
        videoUrl: String,
        onBackPressed: () -> Unit
    ) {
        var isPlaying by remember { mutableStateOf(false) }
        var isPrepared by remember { mutableStateOf(false) }
        var isLoading by remember { mutableStateOf(true) }
        var errorMessage by remember { mutableStateOf<String?>(null) }

        // 创建缓存实例
        val context = LocalContext.current
        val cacheFactory = remember {
            val cacheDir = File(context.cacheDir, "media_cache")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }
            SimpleCache(
                cacheDir,
                LeastRecentlyUsedCacheEvictor(100 * 1024 * 1024), // 100MB 缓存
                StandaloneDatabaseProvider(context) // 使用 StandaloneDatabaseProvider
            )
        }

        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            AndroidView(
                factory = { context ->
                    PlayerView(context).apply {
                        // 创建缓存数据源工厂
                        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
                            .setAllowCrossProtocolRedirects(true)
                            .setConnectTimeoutMs(15000)
                            .setReadTimeoutMs(15000)

                        val upstreamFactory = DefaultDataSource.Factory(
                            context,
                            httpDataSourceFactory
                        )

                        val cacheDataSourceFactory = CacheDataSource.Factory()
                            .setCache(cacheFactory)
                            .setUpstreamDataSourceFactory(upstreamFactory)
                            .setFlags(
                                CacheDataSource.FLAG_BLOCK_ON_CACHE or
                                CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR
                            )
                            .setEventListener(object : CacheDataSource.EventListener {
                                override fun onCachedBytesRead(cacheSizeBytes: Long, cachedBytesRead: Long) {
                                    Log.d(TAG, "Reading from cache: $cachedBytesRead bytes")
                                }

                                override fun onCacheIgnored(reason: Int) {
                                    Log.d(TAG, "Cache ignored, reason: $reason")
                                }
                            })

                        // 修改 MediaSourceFactory 的创建
                        val mediaSourceFactory = DefaultMediaSourceFactory(cacheDataSourceFactory)
                            .setLoadErrorHandlingPolicy(
                                DefaultLoadErrorHandlingPolicy(/* minimumLoadableRetryCount= */ 3)
                            )

                        // 创建播放器
                        player = ExoPlayer.Builder(context)
                            .setMediaSourceFactory(mediaSourceFactory)
                            .build()
                            .apply {
                                playWhenReady = false
                                videoScalingMode = C.VIDEO_SCALING_MODE_SCALE_TO_FIT

                                addListener(object : Player.Listener {
                                    override fun onPlaybackStateChanged(playbackState: Int) {
                                        when (playbackState) {
                                            Player.STATE_READY -> {
                                                isLoading = false
                                                isPrepared = true
                                                if (isPlaying) {
                                                    play()
                                                    hideController()
                                                }
                                            }
                                            Player.STATE_BUFFERING -> {
                                                isLoading = true
                                            }
                                            Player.STATE_ENDED -> {
                                                isLoading = false
                                                isPlaying = false
                                                showController()
                                            }
                                            Player.STATE_IDLE -> {
                                                isLoading = false
                                            }
                                        }
                                    }

                                    override fun onPlayerError(error: PlaybackException) {
                                        Log.e("VideoPlayer", "Error playing video", error)
                                        errorMessage = "视频播放失败: ${error.message}"
                                        isLoading = false
                                    }
                                })

                                setMediaItem(MediaItem.fromUri(videoUrl))
                                prepare()
                            }

                        // PlayerView 设置
                        this.player = player
                        useController = true
                        resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
                        setShutterBackgroundColor(Color.Black.hashCode())

                        // 控制器设置
                        controllerAutoShow = true
                        controllerHideOnTouch = true
                        controllerShowTimeoutMs = 3000 // 3秒后自动隐藏

                        setControllerVisibilityListener(ControllerVisibilityListener { visibility ->
                            if (player?.isPlaying == true) {
                                postDelayed({ hideController() }, 500)
                            }
                        })

                        setOnClickListener {
                            if (player?.isPlaying == true) {
                                showController()
                            }
                        }
                    }
                },
                modifier = Modifier.fillMaxSize(),
                update = { view ->
                    if (view.player?.playbackState == Player.STATE_READY) {
                        if (isPlaying) {
                            view.player?.play()
                            view.hideController()
                        } else {
                            view.player?.pause()
                            view.showController()
                        }
                    }
                },
                onRelease = { view ->
                    view.player?.pause()
                    view.player?.stop()
                    view.player?.release()
                    view.player = null
                    // 释放缓存
                    cacheFactory.release()
                }
            )

            // 顶部栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .statusBarsPadding(),
            ) {
                TopAppBar(
                    title = { Text("视频播放") },
                    navigationIcon = {
                        IconButton(onClick = onBackPressed) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "返回",
                            )
                        }
                    },
                )
            }

            // 加载状态显示
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.7f)),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color.White)
                }
            }

            // 错误信息显示
            errorMessage?.let { error ->
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.8f)),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        modifier = Modifier.padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Text(
                            text = error,
                            color = Color.White,
                            textAlign = TextAlign.Center
                        )
                        Button(onClick = onBackPressed) {
                            Text("返回")
                        }
                    }
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause")
        player?.pause()
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")
        // player?.play()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        player?.stop()
        player?.release()
        player = null
    }
} 