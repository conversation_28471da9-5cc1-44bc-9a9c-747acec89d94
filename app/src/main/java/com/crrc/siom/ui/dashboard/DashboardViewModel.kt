package com.crrc.siom.ui.dashboard

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.AddressBookListResponse.ChatGroup
import com.crrc.common.bean.response.AddressBookListResponse.ChatUser
import com.crrc.common.bean.response.AddressBookListResponse.ExpertGroup
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.model.ChatInfo
import com.crrc.siom.data.model.ParticipantListMessage
import com.crrc.siom.data.model.ParticipantListMessageResponse
import com.crrc.siom.data.model.ReceiveMessage
import com.crrc.siom.data.repository.ContactRepository
import com.crrc.siom.data.repository.ContactRepositoryImpl
import com.crrc.siom.manager.ChatMessageCenter
import com.crrc.siom.service.WebSocketService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch




class DashboardViewModel : ViewModel() {
    // 使用临时实现，后续可以通过依赖注入提供
    private val repository: ContactRepository = ContactRepositoryImpl()

    private val _userContacts = MutableStateFlow<List<ChatUser>>(emptyList())
    val userContacts: StateFlow<List<ChatUser>> = _userContacts

    private val _groupContacts = MutableStateFlow<List<ChatGroup>>(emptyList())
    val groupContacts: StateFlow<List<ChatGroup>> = _groupContacts

    private val _expertGroupContacts = MutableStateFlow<List<ExpertGroup>>(emptyList())
    val expertGroupContacts: StateFlow<List<ExpertGroup>> = _expertGroupContacts




    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    private val _chatList = MutableStateFlow<List<ChatInfo>>(emptyList())
    val chatList: StateFlow<List<ChatInfo>> = _chatList

    // 当前选择的标签页 (0: 聊天, 1: 通讯录)
    private val _selectedTab = MutableStateFlow(0)
    val selectedTab: StateFlow<Int> = _selectedTab
    private var chatBinder: WebSocketService.ChatBinder? = null

    init {
        loadContacts()
        viewModelScope.launch {
            ChatMessageCenter.newMessageFlow.collect { message ->
                Log.d("TAG", "newMessageFlow: $message")
                when(message){
                    is ParticipantListMessageResponse -> {
                        _chatList.value =  message.chatInfoList
                    }
                    else -> {
                    }
                }
            }
        }
    }
    // 设置当前选中的标签页
    fun setSelectedTab(index: Int) {
        _selectedTab.value = index
    }

    fun  setChatBinder(binder: WebSocketService.ChatBinder?) {
        chatBinder = binder
        if (chatBinder != null) {
            getParticipantList()
        }
    }

    fun getParticipantList() {
        chatBinder?.sendParticipantListMessage(
            ParticipantListMessage(
                type = "GET_PARTICIPANT_LIST",
                userId = SessionManager().getUserId().toString()
            )
        )
    }

    fun loadContacts() {
        _isLoading.value = true
        _error.value = null
        val userId =  SessionManager().getUserId();
        print(userId)
        repository.getAddressBookList(userId.toString()) { addressBookListResponse, error ->
            _isLoading.value = false
            if (error != null) {
                _error.value = error
            } else {
                _userContacts.value = addressBookListResponse?.chatUsers!!
                _groupContacts.value = addressBookListResponse?.chatGroups!!
                _expertGroupContacts.value = addressBookListResponse?.expertGroups!!
            }
        }
    }

    fun sendMessageRead(conversationId: String,clientReportedLastReadMsgId: String){


        chatBinder?.sendReceiveMessage(
            ReceiveMessage(
                type = "RECEIVE",
                conversationId = conversationId,
                userId = SessionManager().getUserId().toString(),
                clientReportedLastReadMsgId = clientReportedLastReadMsgId
            )
        )
    }



    fun refresh() {
        loadContacts()
    }

    fun searchContacts(query: String) {

    }
}