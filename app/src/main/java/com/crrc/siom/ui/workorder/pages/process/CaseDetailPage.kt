package com.crrc.siom.ui.workorder.pages.process

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.CaseDetailResponse
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.runtime.getValue

import androidx.compose.ui.draw.clip
import coil.compose.SubcomposeAsyncImage
import coil.request.ImageRequest
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.layout.ContentScale
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import android.content.Context
import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.unit.Dp
import com.crrc.siom.ui.components.MessageItem
import com.crrc.siom.ui.workorder.viewmodel.process.CaseDetailViewModel

@Composable
fun CaseDetailPage(
    caseId: String,
    onBack: () -> Unit,
    viewModel: CaseDetailViewModel = viewModel()
) {
    LaunchedEffect(caseId) {
        viewModel.fetchCaseDetail(caseId)
    }

    val uiState by viewModel.uiState.collectAsState()

    when {
        uiState.isLoading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }

        uiState.error != null -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = "加载失败: ${uiState.error}",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.error
                    )
                    Button(onClick = { viewModel.fetchCaseDetail(caseId) }) {
                        Text("重试")
                    }
                }
            }
        }

        uiState.caseDetail != null -> {
            // 显示案例详情
            CaseDetailContent(
                caseDetail = uiState.caseDetail!!,
                onBack = onBack
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CaseDetailContent(
    caseDetail: CaseDetailResponse,
    onBack: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 标题栏 - 标题居中，返回按钮在左侧
        TopAppBar(
            title = {
                Text(
                    text = caseDetail.title ?: "",
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            },
            navigationIcon = {
                IconButton(onClick = onBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )

        // 详细信息
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            // 报警时间
            item {
                InfoRow(
                    icon = Icons.Default.Schedule,
                    label = "报警时间",
                    value = caseDetail.alarmTime ?: ""
                )
            }

            // 故障内容
            item {
                InfoRow(
                    icon = Icons.Default.Info,
                    label = "故障内容",
                    value = caseDetail.alarmContent ?: ""
                )
            }

            // 故障原因
            item {
                InfoRow(
                    icon = Icons.Default.Warning,
                    label = "故障原因",
                    value = caseDetail.alarmReason ?: ""
                )
            }

            // 设备名称
            item {
                InfoRow(
                    icon = Icons.Default.Build,
                    label = "设备名称",
                    value = caseDetail.device ?: ""
                )
            }

            // 操作人
            item {
                InfoRow(
                    icon = Icons.Default.Person,
                    label = "操作人",
                    value = caseDetail.casePerson ?: ""
                )
            }

            // 维修建议
            item {
                InfoRow(
                    icon = Icons.Default.Settings,
                    label = "维修建议",
                    value = caseDetail.suggestion ?: ""
                )
            }

            item {
                InfoRowChat(
                    icon = Icons.Default.Chat,
                    label = "专家群聊记录",
                    caseDetail = caseDetail
                )
            }
            //2025/6/11
//            // 相关图片
//            item {
//                InfoRowWithMedia(
//                    icon = Icons.Default.Image,
//                    label = "相关图片",
//                    mediaItems = caseDetail.picture ?: emptyList(),
//                    isVideo = false
//                )
//            }
//
//            // 相关视频
//            item {
//                InfoRowWithMedia(
//                    icon = Icons.Default.VideoLibrary,
//                    label = "相关视频",
//                    mediaItems = caseDetail.video ?: emptyList(),
//                    isVideo = true
//                )
//            }

            // 备注
            item {
                InfoRow(
                    icon = Icons.Default.Notes,
                    label = "备注",
                    value = caseDetail.remark ?: ""
                )
            }
        }
    }
}

@Composable
private fun InfoRow(
    icon: ImageVector,
    label: String,
    value: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = label,
                style = MaterialTheme.typography.titleMedium,
                color = Color.Gray,
                modifier = Modifier.width(80.dp)  // 固定宽度
            )

            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.weight(1f)
            )
        }

        Divider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            color = Color.LightGray
        )
    }
}

@Composable
private fun InfoRowChat(
    icon: ImageVector,
    label: String,
    caseDetail: CaseDetailResponse
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp) // 可选的内边距
    ) {
        // 第一行：图标 + 标签
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = label,
                style = MaterialTheme.typography.titleMedium,
                color = Color.Gray,
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 第二行：固定高度、可滑动的聊天记录
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
        ) {
            val messages = caseDetail.chatMessageList
            val participants = caseDetail.chatParticipantVOS

            LazyColumn(
                reverseLayout = true,
                modifier = Modifier.fillMaxSize()
            ) {
                items(messages.size) { index ->
                    val message = messages[index]
                    val participant = participants.find { it.id == message.senderId }
                    MessageItem(
                        message = message.text,
                        isFromMe = false,
                        timestamp = message.time,
                        senderName = participant?.realName.toString()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }

        Divider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            color = Color.LightGray
        )
    }
}


@Composable
private fun InfoRowWithMedia(
    icon: ImageVector,
    label: String,
    mediaItems: List<String>,
    isVideo: Boolean = false
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = label,
                style = MaterialTheme.typography.titleMedium,
                color = Color.Gray
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        val itemsPerRow = 3
        val rows = mediaItems.chunked(itemsPerRow)
        rows.forEach { rowItems ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = if (rowItems != rows.last()) 8.dp else 0.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 显示该行的项目
                rowItems.forEach { url ->
                    Box(modifier = Modifier.weight(1f)) {
                        if (isVideo) {
                            VideoItem(
                                videoUrl = url,
                                size = 100.dp
                            )
                        } else {
                            ImageItem(
                                imageUrl = url,
                                size = 100.dp
                            )
                        }
                    }
                }

                // 如果该行不满3个，添加空白占位以保持布局
                repeat(itemsPerRow - rowItems.size) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
        }


        Divider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            color = Color.LightGray
        )
    }
}


@Composable
private fun ImageItem(imageUrl: String, size: Dp) {
    Box(
        modifier = Modifier
            .size(size)
            .border(1.dp, Color.Gray, RoundedCornerShape(8.dp))
            .clickable { /* TODO: 查看大图 */ },
        contentAlignment = Alignment.Center
    ) {
        // 使用SubcomposeAsyncImage加载图片
        // 自定义加载中和错误状态
        SubcomposeAsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(imageUrl)
                .crossfade(true)
                .build(),
            contentDescription = "图片",
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(8.dp)),
            contentScale = ContentScale.Crop,
            loading = {
                MediaPlaceholder(false)
            },
            error = {
                // 加载失败显示错误图标
                MediaPlaceholder(false)
            }
        )
    }
}

@Composable
private fun VideoItem(
    videoUrl: String,
    size: Dp,
    context: Context = LocalContext.current
) {
    val scope = rememberCoroutineScope()
    val thumbnailBitmap = remember { mutableStateOf<Bitmap?>(null) }

    // 加载视频第一帧作为缩略图
    LaunchedEffect(videoUrl) {
        scope.launch(Dispatchers.IO) {
            try {
                val retriever = MediaMetadataRetriever()
                retriever.setDataSource(videoUrl)
                val bitmap = retriever.getFrameAtTime(0)
                thumbnailBitmap.value = bitmap
                retriever.release()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    Box(
        modifier = Modifier
            .size(size)
            .border(1.dp, Color.Gray, RoundedCornerShape(8.dp))
            .clickable {
                // 点击时启动视频播放
                val intent = Intent(context, VideoPlayerActivity::class.java).apply {
                    putExtra("videoUrl", videoUrl)
                }
                context.startActivity(intent)
            },
        contentAlignment = Alignment.Center
    ) {
        if (thumbnailBitmap.value != null) {
            Image(
                bitmap = thumbnailBitmap.value!!.asImageBitmap(),
                contentDescription = "视频缩略图",
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(7.dp)),
                contentScale = ContentScale.Crop
            )
        } else {
            MediaPlaceholder(true)
        }

        // 播放按钮覆盖层
        Icon(
            imageVector = Icons.Default.PlayArrow,
            contentDescription = "播放视频",
            modifier = Modifier
                .size(40.dp)
                .background(Color.Black.copy(alpha = 0.5f), CircleShape)
                .padding(8.dp),
            tint = Color.White
        )
    }
}

@Composable
private fun MediaPlaceholder(isVideo: Boolean) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .border(1.dp, Color.LightGray, RoundedCornerShape(8.dp)),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = if (isVideo) Icons.Default.PlayArrow else Icons.Default.Image,
            contentDescription = null,
            modifier = Modifier.size(40.dp),
            tint = Color.LightGray
        )
    }
}
