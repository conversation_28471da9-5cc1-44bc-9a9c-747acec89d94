package com.crrc.siom.ui.profile

import android.util.Log
import androidx.lifecycle.ViewModel
import com.crrc.siom.data.repository.UserRepository
import com.crrc.siom.data.repository.UserRepositoryImpl
import com.github.mikephil.charting.data.PieEntry
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.text.SimpleDateFormat
import java.util.*

class StatisticsViewModel : ViewModel() {
    private val userRepository: UserRepository = UserRepositoryImpl()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _statisticsData = MutableStateFlow<List<PieEntry>>(emptyList())
    val statisticsData: StateFlow<List<PieEntry>> = _statisticsData

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage

    // 获取统计数据
    fun loadStatistics(time: Calendar, timeType: Int, userId: String) {
        _isLoading.value = true
        _errorMessage.value = null
        val timeStr = when (timeType) {
            1 -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(time.time)
            2 -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(time.time)
            3 -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(time.time)
            else -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(time.time)
        }
        userRepository.getWorkOrderStatistics(timeStr, timeType, userId) { statics, error ->
            _isLoading.value = false
            if (statics != null) {
                val pieList = mutableListOf<PieEntry>()
                if (statics.uncompletedCount > 0) pieList.add(PieEntry(statics.uncompletedCount.toFloat(), "未完成"))
                if (statics.completedCount > 0) pieList.add(PieEntry(statics.completedCount.toFloat(), "已完成"))
                _statisticsData.value = pieList
            } else {
                _errorMessage.value = error ?: "未知错误"
                _statisticsData.value = emptyList()
            }
        }
    }
} 