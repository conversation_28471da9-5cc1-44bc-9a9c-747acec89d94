package com.crrc.siom.ui.line;

import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.widget.ViewPager2;

import com.amap.api.maps.MapView;
import com.crrc.siom.R;
import com.crrc.siom.data.model.Station;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

/**
 * 详情面板管理器
 */
public class DetailPanelManager {
    
    private final FragmentActivity activity;
    private final LineMapJavaViewModel viewModel;
    
    private final ConstraintLayout containerDetail;
    private final TextView tvLineInfo;
    private final ViewPager2 viewPager;
    private final TabLayout tabLayout;
    private final Spinner spinnerFilter;
    private final MapView mapView;
    
    private StationDetailAdapter detailAdapter;
    
    public DetailPanelManager(FragmentActivity activity, LineMapJavaViewModel viewModel) {
        this.activity = activity;
        this.viewModel = viewModel;
        
        containerDetail = activity.findViewById(R.id.container_detail);
        tvLineInfo = activity.findViewById(R.id.tv_line_info);
        viewPager = activity.findViewById(R.id.view_pager);
        tabLayout = activity.findViewById(R.id.tab_layout);
        spinnerFilter = activity.findViewById(R.id.spinner_filter);
        mapView = activity.findViewById(R.id.map_view);
        
        setupSpinner();
    }
    
    /**
     * 设置下拉选择器
     */
    private void setupSpinner() {
        String[] warehouseOptions = {"备品备件", "工器具"};
        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(activity, 
                android.R.layout.simple_spinner_item, warehouseOptions);
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerFilter.setAdapter(spinnerAdapter);
        spinnerFilter.setSelection(0); // 默认选择备品备件
    }
    
    /**
     * 显示站点详情面板
     */
    public void showStationDetailPanel(String stationName) {
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mapView.getLayoutParams();
        params.bottomToTop = R.id.container_detail;
        mapView.setLayoutParams(params);
        // 设置ViewPager
        setupViewPager(stationName);
        containerDetail.setVisibility(View.VISIBLE);
    }
    
    /**
     * 隐藏站点详情面板
     */
    public void hideStationDetailPanel() {
        // 恢复地图大小
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mapView.getLayoutParams();
        params.bottomToTop = R.id.button_container;
        mapView.setLayoutParams(params);
        
        // 隐藏容器
        containerDetail.setVisibility(View.GONE);
    }
    
    /**
     * 设置ViewPager
     */
    private void setupViewPager(String stationName) {
        // 创建适配器
        detailAdapter = new StationDetailAdapter(activity, stationName);
        viewPager.setAdapter(detailAdapter);
        
        // 强制预加载所有页面
        viewPager.setOffscreenPageLimit(2);
        
        // 设置标签页
        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case 0:
                    tab.setText("报警");
                    break;
                case 1:
                    tab.setText("库房");
                    break;
            }
        }).attach();
        
        // 设置站点信息
        tvLineInfo.setText("15号线-" + stationName + "站");
        
        // 设置Spinner选择监听
        spinnerFilter.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Station station = findStationByName(stationName);
                if (station != null) {
                    if (position == 0) {
                        // 备品备件 //todo storeID 四个地方要改
                        viewModel.getLineDeviceInfo(station.getStationId());
                    } else {
                        // 工器具
                        viewModel.getLineToolsInfo(station.getStationId());
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        
        // 加载数据
        Station station = findStationByName(stationName);
        if (station != null) {
            if (station.getHasAlarm()) {
                // 获取报警信息
                viewModel.getStationAlarmInfo(station.getStationId());
            }
            
            if (station.getStoreInfo() != null) {
                // 默认加载备品备件数据
                viewModel.getLineDeviceInfo(station.getStationId());
            }
        }
        
        // 先设置到第一个tab，确保初始化完成
        viewPager.setCurrentItem(0, false);
        
        // 添加页面切换监听
        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                // Show spinner only in warehouse tab (position 1)
                spinnerFilter.setVisibility(position == 1 ? View.VISIBLE : View.GONE);
            }
        });
    }
    
    /**
     * 切换到报警标签页
     */
    public void switchToAlarmTab() {
        viewPager.post(() -> {
            viewPager.setCurrentItem(0, true);
        });
    }
    
    /**
     * 切换到库房标签页
     */
    public void switchToStorageTab() {
        viewPager.post(() -> {
            viewPager.setCurrentItem(1, true);
        });
    }
    
    /**
     * 获取适配器
     */
    public StationDetailAdapter getDetailAdapter() {
        return detailAdapter;
    }
    
    /**
     * 根据站点名称查找站点
     */
    private Station findStationByName(String stationName) {
        if (stationName == null) return null;
        
        for (Station station : viewModel.getStations().getValue()) {
            if (stationName.equals(station.getName())) {
                return station;
            }
        }
        return null;
    }
} 