package com.crrc.siom.ui.dashboard.userinfo

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.crrc.common.bean.response.UserInfoResponse
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.repository.UserRepository
import com.crrc.siom.data.repository.UserRepositoryImpl

class UserInfoViewModel(userId: String) : ViewModel() {


    private val userRepository: UserRepository = UserRepositoryImpl()

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _userInfo = MutableLiveData<UserInfoResponse?>()
    val userInfo: LiveData<UserInfoResponse?> = _userInfo
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    init {
        loadUserInfo(userId)
    }
    /**
     * 加载用户信息
     */
    fun loadUserInfo(userId: String) {

        if (userId.isEmpty()) {
            _errorMessage.value = "用户ID为空,无用户信息"
            return
        }
        
        _isLoading.value = true
        _errorMessage.value = null
        
        userRepository.getUserById(userId) { userInfoResponse, errorMsg ->
            _isLoading.value = false
            if (userInfoResponse != null) {
                _userInfo.value = userInfoResponse
            } else {
                _errorMessage.value = errorMsg ?: "获取用户信息失败"
            }
        }
    }

}

class UserInfoViewModelFactory(private val userId: String) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(UserInfoViewModel::class.java)) {
            return UserInfoViewModel(userId) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}