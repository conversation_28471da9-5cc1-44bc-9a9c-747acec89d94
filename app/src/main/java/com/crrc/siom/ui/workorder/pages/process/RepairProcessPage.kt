package com.crrc.siom.ui.workorder.pages.process

import android.app.Activity
import android.os.Environment
import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp


import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentPaste // Placeholder for the clipboard icon
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.ProcedureResponse
import com.crrc.common.bean.response.WorkOrderDetailResponse
import com.crrc.common.utils.PrefsUtil
import com.crrc.common.utils.ToastUtil
import com.crrc.siom.AppApplication
import com.crrc.siom.ui.document.getMimeType
import com.crrc.siom.ui.document.openFileWithIntent
import com.crrc.siom.ui.workorder.viewmodel.process.RepairProcessViewModel
import com.crrc.siom.utils.DownloadUtil
import kotlinx.coroutines.delay
import java.io.File

@Composable
fun RepairProcessPage(
    workOrderId: String,
    modifier: Modifier = Modifier,
    onBackClick: () -> Unit,
    repairProcessViewModel: RepairProcessViewModel = viewModel()
) {
    val isLoading by repairProcessViewModel.isLoading.collectAsState()
    val procedureData by repairProcessViewModel.procedureData.collectAsState()
    val operationResult by repairProcessViewModel.operationResult.collectAsState()
    val context = LocalContext.current
    var notes by remember { mutableStateOf("") }
    LaunchedEffect(workOrderId) {
        repairProcessViewModel.loadProcedure(workOrderId)
    }
    operationResult?.let { (success, message) ->
        LaunchedEffect(operationResult) {
            if (success) {
                Toast.makeText(context, "操作成功", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "操作失败: $message", Toast.LENGTH_SHORT).show()
            }
            repairProcessViewModel.resetOperationResult()
        }
    }

    if (isLoading && procedureData == null) {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            CircularProgressIndicator()
        }
    } else {
        procedureData?.let { procedure ->
            RepairProcessContent(
                procedure = procedure,
                onPassProcedure = { procedureId ->
                    repairProcessViewModel.passProcedure(workOrderId, procedureId)
                },
                onFailProcedure = { procedureId ->
                    repairProcessViewModel.failProcedure(workOrderId, procedureId)
                },
                onConfirmProcedure = {
                    procedure.note = notes
                    repairProcessViewModel.confirmProcedure(workOrderId,procedure,
                        onSuccess = {
                            onBackClick()
                            ToastUtil.show(it)
                        },
                        onError = {
                            ToastUtil.show(it)
                        }
                    )
                },
                notes = notes,
                onNotesChange = { notes = it }
            )
        } ?: run {
            // 无数据显示
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                Text("暂无维修流程数据")
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RepairProcessContent(
    procedure: ProcedureResponse,
    onPassProcedure: (String) -> Unit,
    onFailProcedure: (String) -> Unit,
    onConfirmProcedure: () -> Unit,
    notes: String,
    onNotesChange: (String) -> Unit,
    showGuide: Boolean = false,
    onToggleGuide: (() -> Unit)? = null
) {
    Scaffold(
        bottomBar = {
            Button(
                onClick = onConfirmProcedure,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
                    .height(48.dp)
            ) {
                Text("确认完成")
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(innerPadding)
                .padding(16.dp)
        ) {
            Text(
                text = "维修内容",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text(
                text = procedure.content ?: "暂无内容",
                fontSize = 16.sp,
                modifier = Modifier.padding(bottom = 16.dp)
            )



            Divider(color = Color.LightGray, thickness = 1.dp)

            // "维修流程" Section Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp)
            ) {
                Text(
                    text = "维修流程",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                IconButton(onClick = { onToggleGuide?.invoke() }) {
                    Icon(
                        imageVector = Icons.Filled.ContentPaste,
                        contentDescription = "维修流程图标",
                        tint = if (showGuide) MaterialTheme.colorScheme.primary else Color.Gray
                    )
                }
            }
            // 维修手册区块，受 showGuide 控制
            if (showGuide && !procedure.getGuides().isNullOrEmpty()) {
                GuideSection(procedure.getGuides()!!)
            }
            if (procedure.procedures.isNullOrEmpty()) {
                Text("没有可用的维修项。", modifier = Modifier.padding(vertical = 16.dp))
            } else {
                procedure.procedures.forEachIndexed { index, item ->
                    ProcedureItemView(
                        procedure = item,
                        onPass = {
                            onPassProcedure(item.id)
                        },
                        onFail = {
                            onFailProcedure(item.id)
                        }
                    )
                    if (index < procedure.procedures.size - 1) {
                        Divider(color = Color.LightGray, thickness = 0.5.dp)
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "备注",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            OutlinedTextField(
                value = notes,
                onValueChange = onNotesChange,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
                placeholder = { Text("请输入......") },
                shape = RoundedCornerShape(8.dp)
            )

            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

@Composable
fun ProcedureItemView(
    procedure: ProcedureResponse.Procedure,
    onPass: () -> Unit,
    onFail: () -> Unit
) {
    var status by remember { mutableStateOf(procedure.status) }

    LaunchedEffect(procedure.status) {
        status = procedure.status
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.Top
    ) {
        Text(
            text = "${procedure.index}.",
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .padding(end = 8.dp)
                .align(Alignment.CenterVertically)
        )

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = procedure.item ?: "",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold
            )
            Text(
                text = "标准: ${procedure.standard ?: ""}",
                fontSize = 14.sp,
                color = Color.DarkGray,
                modifier = Modifier.padding(top = 4.dp)
            )
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.align(Alignment.CenterVertically)
        ) {
            RadioButton(
                onClick = {
                    status = 2
                    procedure.status = 2
                    onPass()
                },
                selected = status == 2
            )
            Text("通过")

            Spacer(modifier = Modifier.width(8.dp))

            RadioButton(
                onClick = {
                    status = 3
                    procedure.status = 3
                    onFail()
                },
                selected = status == 3
            )
            Text("未通过")
        }
    }
}

@Composable
fun GuideSection(guides: List<WorkOrderDetailResponse.Guide>) {
    if (guides.isEmpty()) return
    val context = LocalContext.current
    Column(modifier = Modifier.padding(bottom = 16.dp)) {
//        Text(
//            text = "维修手册",
//            fontSize = 18.sp,
//            fontWeight = FontWeight.Bold,
//            modifier = Modifier.padding(bottom = 8.dp)
//        )
        guides.forEach { guide ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
            ) {
                Column(modifier = Modifier.padding(12.dp)) {
                    val id = guide.id
                    Text(
                        text = guide.name,
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp,
                        color = if (id != null) MaterialTheme.colorScheme.primary else Color.Unspecified,
                        modifier = if (id != null) Modifier.clickable {
                            val ipAddress = PrefsUtil.getInstance(AppApplication.sApplication).baseUrl ?: ""
                            val fileUrl = "${ipAddress}config/file/mobile/downLoad?id=${guide.id}"
                            var fileName = "${guide.name}.${guide.suffix}"
                            DownloadUtil.downloadAndOpen(context,fileName,fileUrl)
                        } else Modifier
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, name = "Guide Hidden")
@Composable
fun PreviewRepairProcessContent_GuideHidden() {
    val mockProcedure = ProcedureResponse().apply {
        content = "更换空调滤芯，检查压缩机运行状态。"
        procedures = listOf(
            ProcedureResponse.Procedure().apply {
                id = "1"
                index = 1
                item = "更换滤芯"
                standard = "更换为原厂新滤芯"
                status = 1
            },
            ProcedureResponse.Procedure().apply {
                id = "2"
                index = 2
                item = "检查压缩机"
                standard = "压缩机运行正常，无异响"
                status = 2
            }
        )
        // mock guide
        guides = listOf(
            WorkOrderDetailResponse.Guide().apply {
                id = "2"
                name = "这是一份维修指导书"
                url = "检查压缩机"
            }
        )
    }
    var notes by remember { mutableStateOf("") }
    MaterialTheme {
        RepairProcessContent(
            procedure = mockProcedure,
            onPassProcedure = {},
            onFailProcedure = {},
            onConfirmProcedure = {},
            notes = notes,
            onNotesChange = { notes = it },
            showGuide = false
        )
    }
}

@Preview(showBackground = true, name = "Guide Shown")
@Composable
fun PreviewRepairProcessContent_GuideShown() {
    val mockProcedure = ProcedureResponse().apply {
        content = "更换空调滤芯，检查压缩机运行状态。"
        procedures = listOf(
            ProcedureResponse.Procedure().apply {
                id = "1"
                index = 1
                item = "更换滤芯"
                standard = "更换为原厂新滤芯"
                status = 1
            },
            ProcedureResponse.Procedure().apply {
                id = "2"
                index = 2
                item = "检查压缩机"
                standard = "压缩机运行正常，无异响"
                status = 2
            }
        )
        guides = listOf(
            WorkOrderDetailResponse.Guide().apply {
                id = "2"
                name = "这是一份维修指导书"
                url = "检查压缩机"
            }
        )
    }
    var notes by remember { mutableStateOf("") }
    MaterialTheme {
        RepairProcessContent(
            procedure = mockProcedure,
            onPassProcedure = {},
            onFailProcedure = {},
            onConfirmProcedure = {},
            notes = notes,
            onNotesChange = { notes = it },
            showGuide = true
        )
    }
}

