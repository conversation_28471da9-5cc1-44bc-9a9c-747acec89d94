package com.crrc.siom.ui.line;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.amap.api.maps.AMap;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.Polyline;
import com.amap.api.maps.model.PolylineOptions;
import com.crrc.siom.R;
import com.crrc.siom.data.model.Station;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 地图标记管理类
 */
public class MapMarkerManager {
    private static final String TAG = "MapMarkerManager";
    
    private final Context context;
    private final AMap aMap;
    
    // 存储不同类型的标记列表
    private List<Marker> allMarkers = new ArrayList<>(); // 位置标记
    private List<Marker> alarmMarkers = new ArrayList<>();
    private List<Marker> storeMarkers = new ArrayList<>();
    private Map<String, Marker> markerMap = new HashMap<>(); // 站点ID到标记的映射
    
    // 存储信息标记列表
    private List<Marker> allInfoMarkers = new ArrayList<>(); // 信息标记
    private List<Marker> alarmInfoMarkers = new ArrayList<>();
    private List<Marker> storeInfoMarkers = new ArrayList<>();
    private Map<String, Marker> infoMarkerMap = new HashMap<>(); // 站点ID到信息标记的映射
    
    private Polyline routeLine; // 存储路线
    
    private MarkerDisplayMode currentDisplayMode = MarkerDisplayMode.NORMAL;
    
    public MapMarkerManager(Context context, AMap aMap) {
        this.context = context;
        this.aMap = aMap;
    }
    
    /**
     * 创建所有站点的标记
     */
    public void createAllMarkers(List<Station> stations, String currentStation) {
        if (stations == null || stations.isEmpty() || aMap == null) {
            return;
        }
        
        // 清除所有现有标记和路线
        clearAllMarkersFromMap();
        allMarkers.clear();
        alarmMarkers.clear();
        storeMarkers.clear();
        markerMap.clear();
        allInfoMarkers.clear();
        alarmInfoMarkers.clear();
        storeInfoMarkers.clear();
        infoMarkerMap.clear();
        
        // 绘制路线
        drawRouteLine(stations);
        
        // 为每个站点创建标记
        for (Station station : stations) {
            // 处理站点信息
            boolean hasAlarm = station.getHasAlarm();
            boolean hasStore = station.getStoreInfo() != null;
            
            // 创建位置标记
            MarkerOptions markerOptions = new MarkerOptions()
                    .position(new LatLng(station.getX(), station.getY()))
                    .title(station.getName())
                    .visible(false)
                    .anchor(0.5f, 0.5f)
                    .zIndex(5);
                    
            // 使用小圆点图标
            markerOptions.icon(BitmapDescriptorFactory.fromBitmap(MapMarkerUtils.createDotBitmap(context)));
            
            // 添加位置标记到地图并保存引用
            Marker marker = aMap.addMarker(markerOptions);
            marker.setObject("location");
            allMarkers.add(marker);
            markerMap.put(station.getStationId(), marker);
            
            // 如果站点有报警属性，创建报警气泡
            if (hasAlarm) {
                alarmMarkers.add(marker);
                String alarmContent = "报警: " + station.getAlarmCount() + "条";
                Marker alarmBubble = createBubbleMarker(station, alarmContent, "alarm");
                
                if (alarmBubble != null) {
                    alarmInfoMarkers.add(alarmBubble);
                    infoMarkerMap.put(station.getStationId() + "_alarm", alarmBubble);
                }
            }
            
            // 如果站点有库房属性，创建库房气泡
            if (hasStore) {
                storeMarkers.add(marker);
                String storeContent = "库房: " + station.getStoreInfo().getName();
                Marker storeBubble = createBubbleMarker(station, storeContent, "store");
                
                if (storeBubble != null) {
                    storeInfoMarkers.add(storeBubble);
                    infoMarkerMap.put(station.getStationId() + "_store", storeBubble);
                }
            }
        }
    }
    
    /**
     * 创建气泡标记
     */
    private Marker createBubbleMarker(Station station, String content, String type) {
        MarkerOptions bubbleOptions = new MarkerOptions()
                .position(new LatLng(station.getX(), station.getY() - 0.0008))
                .title(station.getName())
                .snippet(content)
                .visible(false)
                .anchor(0.5f, 1.0f)
                .zIndex(10);
        
        Bitmap bubbleBitmap = MapMarkerUtils.createBitmapFromResource(context, R.drawable.bg_marker_info_bubble);
        if (bubbleBitmap != null) {
            Bitmap textBubble = MapMarkerUtils.createTextBubbleBitmap(content, bubbleBitmap);
            bubbleOptions.icon(BitmapDescriptorFactory.fromBitmap(textBubble != null ? textBubble : bubbleBitmap));
            
            Marker bubbleMarker = aMap.addMarker(bubbleOptions);
            bubbleMarker.setObject(type);
            allInfoMarkers.add(bubbleMarker);
            return bubbleMarker;
        }
        
        Log.e(TAG, "创建气泡标记失败");
        return null;
    }
    
    /**
     * 根据当前显示模式更新标记显示
     */
    public void updateMarkerDisplay() {
        switch (currentDisplayMode) {
            case NORMAL:
                showNormalMarkers();
                break;
            case ALARM:
                showAlarmMarkers();
                break;
            case STORE:
                showStoreMarkers();
                break;
        }
    }
    
    /**
     * 从地图上清除所有标记
     */
    public void clearAllMarkersFromMap() {
        for (Marker marker : allMarkers) {
            marker.setVisible(false);
        }
        
        for (Marker infoMarker : allInfoMarkers) {
            infoMarker.setVisible(false);
        }
    }
    
    /**
     * 清除地图上所有内容（不包括路线）
     */
    private void clearMap() {
        for (Marker marker : allMarkers) {
            marker.setVisible(false);
        }
        
        for (Marker infoMarker : allInfoMarkers) {
            infoMarker.setVisible(false);
        }
    }
    
    /**
     * 显示所有站点（正常模式）
     */
    public void showNormalMarkers() {
        // 先清除地图
        clearMap();
        
        // 显示所有位置标记
        for (Marker marker : allMarkers) {
            marker.setVisible(true);
        }
    }
    
    /**
     * 显示报警站点
     */
    public void showAlarmMarkers() {
        // 先清除地图
        clearMap();
        
        if (alarmMarkers.isEmpty()) {
            Toast.makeText(context, "没有报警站点", Toast.LENGTH_SHORT).show();
            showNormalMarkers();
            return;
        }
        
        // 显示所有非报警站点（半透明）
        for (Marker marker : allMarkers) {
            marker.setVisible(true);
            
            boolean isAlarmMarker = alarmMarkers.contains(marker);
            marker.setAlpha(isAlarmMarker ? 1.0f : 0.5f);
        }
        
        // 显示所有报警站点的报警气泡标记
        for (Marker infoMarker : alarmInfoMarkers) {
            infoMarker.setVisible(true);
        }
    }
    
    /**
     * 显示库房站点
     */
    public void showStoreMarkers() {
        // 先清除地图
        clearMap();
        
        if (storeMarkers.isEmpty()) {
            Toast.makeText(context, "没有库房站点", Toast.LENGTH_SHORT).show();
            showNormalMarkers();
            return;
        }
        
        // 显示所有非库房站点（半透明）
        for (Marker marker : allMarkers) {
            marker.setVisible(true);
            
            boolean isStoreMarker = storeMarkers.contains(marker);
            marker.setAlpha(isStoreMarker ? 1.0f : 0.5f);
        }
        
        // 显示所有库房站点的库房气泡标记
        for (Marker infoMarker : storeInfoMarkers) {
            infoMarker.setVisible(true);
        }
    }
    
    /**
     * 绘制路线
     */
    private void drawRouteLine(List<Station> stations) {
        // 如果已经有路线，先清除
        if (routeLine != null) {
            routeLine.remove();
        }
        
        PolylineOptions lineOptions = new PolylineOptions();
        for (Station station : stations) {
            lineOptions.add(new LatLng(station.getX(), station.getY()));
        }
        lineOptions.width(10f).color(Color.parseColor("#FF5252"));
        routeLine = aMap.addPolyline(lineOptions);
    }
    
    /**
     * 设置当前显示模式
     */
    public void setDisplayMode(MarkerDisplayMode mode) {
        this.currentDisplayMode = mode;
        updateMarkerDisplay();
    }
    
    /**
     * 获取当前显示模式
     */
    public MarkerDisplayMode getCurrentDisplayMode() {
        return currentDisplayMode;
    }
    
    /**
     * 自定义信息窗口适配器
     */
    public static class CustomInfoWindowAdapter implements AMap.InfoWindowAdapter {
        private final Context context;
        
        public CustomInfoWindowAdapter(Context context) {
            this.context = context;
        }
        
        @Override
        public View getInfoWindow(Marker marker) {
            // 只为位置标记显示信息窗口，信息标记不显示
            if (marker.getObject() != null && !"location".equals(marker.getObject())) {
                return null;
            }
            
            View infoWindow = LayoutInflater.from(context).inflate(
                    R.layout.custom_info_window, null);
            
            TextView title = infoWindow.findViewById(R.id.title);
            TextView snippet = infoWindow.findViewById(R.id.snippet);
            
            title.setText(marker.getTitle());
            
            // 如果没有附加信息，不显示
            String snippetText = marker.getSnippet();
            if (snippetText != null && !snippetText.isEmpty()) {
                snippet.setVisibility(View.VISIBLE);
                snippet.setText(snippetText);
            } else {
                snippet.setVisibility(View.GONE);
            }
            
            return infoWindow;
        }

        @Override
        public View getInfoContents(Marker marker) {
            return null;
        }
    }
} 