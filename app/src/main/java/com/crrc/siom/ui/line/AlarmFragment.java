package com.crrc.siom.ui.line;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.crrc.common.bean.response.StationAlarmResponse;
import com.crrc.siom.R;

import java.util.ArrayList;
import java.util.List;

public class AlarmFragment extends Fragment {
    private RecyclerView recyclerView;
    private AlarmAdapter adapter;
    private String stationName;
    
    public static AlarmFragment newInstance(String stationName) {
        AlarmFragment fragment = new AlarmFragment();
        Bundle args = new Bundle();
        args.putString("station_name", stationName);
        fragment.setArguments(args);
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            stationName = getArguments().getString("station_name");
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_alarm, container, false);
        recyclerView = view.findViewById(R.id.recycler_view);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new AlarmAdapter();
        recyclerView.setAdapter(adapter);
        
        return view;
    }

    public void updateAlarmData(List<StationAlarmResponse> alarms) {
        if (alarms == null || alarms.isEmpty()) {
            return;
        }
        
        adapter.setItems(alarms);
    }

    private class AlarmAdapter extends RecyclerView.Adapter<AlarmViewHolder> {
        private List<StationAlarmResponse> items = new ArrayList<>();
        
        @NonNull
        @Override
        public AlarmViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_alarm, parent, false);
            return new AlarmViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull AlarmViewHolder holder, int position) {
            StationAlarmResponse alarm = items.get(position);
            holder.bind(alarm);
        }
        
        @Override
        public int getItemCount() {
            return items.size();
        }
        
        @SuppressLint("NotifyDataSetChanged")
        public void setItems(List<StationAlarmResponse> items) {
            this.items = items;
            notifyDataSetChanged();
        }
    }
    
    private static class AlarmViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvAlarmId;
        private final TextView tvAlarmTime;
        private final TextView tvAlarmDesc;
        
        public AlarmViewHolder(@NonNull View itemView) {
            super(itemView);
            tvAlarmId = itemView.findViewById(R.id.tv_alarm_id);
            tvAlarmTime = itemView.findViewById(R.id.tv_alarm_time);
            tvAlarmDesc = itemView.findViewById(R.id.tv_alarm_desc);
        }
        
        public void bind(StationAlarmResponse alarm) {
            tvAlarmId.setText(alarm.getAlarmId());
            tvAlarmTime.setText(alarm.getLastAlarmDate());
            tvAlarmDesc.setText(alarm.getAlarmDescription());
        }
    }
} 