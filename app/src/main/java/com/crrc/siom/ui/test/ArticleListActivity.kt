package com.crrc.siom.ui.test

import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.crrc.common.base.BaseComposeActivity
import com.crrc.common.bean.response.Article

class ArticleListActivity : BaseComposeActivity<ArticleViewModel, List<Article>>() {

    override val viewModel: ArticleViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.loadArticles()
    }

    @Composable
    override fun Content(data: List<Article>?) {
        LazyColumn {
            items(data ?: emptyList()) { article ->
                ArticleItem(article)
            }
        }
    }

    @Composable
    private fun ArticleItem(article: Article) {
        Column(
            modifier = Modifier.Companion
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = article.title,
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.Companion.height(4.dp))
            Text(
                text = article.author,
                style = MaterialTheme.typography.bodySmall
            )
            Spacer(modifier = Modifier.Companion.height(8.dp))
            Text(
                text = article.content,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 2,
                overflow = TextOverflow.Companion.Ellipsis
            )
        }
    }

    override fun onNetworkResponded(data: List<Article>?) {
        // 可以在这里处理数据，比如保存到本地数据库
    }
}