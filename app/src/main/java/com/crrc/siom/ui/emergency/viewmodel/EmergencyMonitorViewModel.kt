package com.crrc.siom.ui.emergency.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.EmergencyEventListResponse.EmergencyEvent
import com.crrc.siom.data.repository.EmergencyMonitorRepository
import com.crrc.siom.data.repository.EmergencyMonitorRepositoryImpl


import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class EmergencyMonitorViewModel : ViewModel() {
    private val emergencyRepository: EmergencyMonitorRepository = EmergencyMonitorRepositoryImpl()

    private val _emergencyList = MutableStateFlow<List<EmergencyEvent>>(emptyList())
    val emergencyList: StateFlow<List<EmergencyEvent>> = _emergencyList.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    fun loadEmergencyList() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            try {
                emergencyRepository.getEmergencyEventList { emergencyEventList, errorMsg ->
                    _isLoading.value = false
                    emergencyEventList?.let {
                        _emergencyList.value = emergencyEventList.emergencyEventList
                    }
                }
            } catch (e: Exception) {
                _error.value = e.message ?: "未知错误"
                _isLoading.value = false
            }
        }
    }

} 