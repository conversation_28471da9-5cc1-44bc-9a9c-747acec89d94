package com.crrc.siom.ui.test

import com.crrc.common.base.BaseViewModel
import com.crrc.common.bean.response.Article
import com.crrc.siom.data.model.ArticleModel

class ArticleViewModel : BaseViewModel<ArticleModel, List<Article>>() {
    private val articleModel = sCreateModel()

    init {
        articleModel.register(this)
    }

    override fun sCreateModel(): ArticleModel = ArticleModel()

    fun loadArticles() {
        articleModel.loadModelData()
    }
}