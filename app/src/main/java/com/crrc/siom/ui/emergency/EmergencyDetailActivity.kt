package com.crrc.siom.ui.emergency

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.crrc.siom.ui.theme.SiomTheme

class EmergencyDetailActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_EMERGENCY_ID = "emergency_id"
        private const val EXTRA_EMERGENCY_CONTENT = "emergency_content"

        fun start(context: Context, emergencyId: String,name: String) {
            val intent = Intent(context, EmergencyDetailActivity::class.java).apply {
                putExtra(EXTRA_EMERGENCY_ID, emergencyId)
                putExtra(EXTRA_EMERGENCY_CONTENT, name)
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val emergencyId = intent.getStringExtra(EXTRA_EMERGENCY_ID) ?: ""
        val emergencyContent = intent.getStringExtra(EXTRA_EMERGENCY_CONTENT) ?: ""

        setContent {
            SiomTheme {
                EmergencyDetailScreen(
                    emergencyId = emergencyId,
                    emergencyContent = emergencyContent,
                    onBackClick = { finish() }
                )
            }
        }
    }
} 