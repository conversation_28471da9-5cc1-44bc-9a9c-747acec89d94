package com.crrc.siom.ui.caselibrary

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.CaseListResponse.CaseRecord
import com.crrc.siom.ui.workorder.components.BaseWorkOrderPage
import com.crrc.siom.ui.workorder.viewmodel.process.CaseLibraryViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CaseLibraryPage(
    viewModel: CaseLibraryViewModel = viewModel()
) {
    // 页面初始化时获取案例数据
    LaunchedEffect(Unit) {
        viewModel.getCaseList()
    }
    
    // 通过 ViewModel 收集状态
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    Column {
        TopAppBar(
            title = { Text("案例库") },
            navigationIcon = {
                IconButton(onClick = { (context as? Activity)?.finish() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )
        BaseWorkOrderPage(
            items = uiState.cases,
            isLoading = uiState.isLoading && uiState.currentPage == 1,
            isLoadingMore = uiState.isLoading && uiState.currentPage > 1,
            hasMoreData = uiState.hasMoreData,
            error = uiState.error,
            onRefresh = { viewModel.getCaseList(1) },
            onLoadMore = { viewModel.loadMoreCases() },
            filterBar = {
                Column {
                    OutlinedTextField(
                        value = uiState.searchQuery,
                        onValueChange = { viewModel.searchCases(it) },
                        placeholder = { Text("请输入搜索关键词") },
                        leadingIcon = { Icon(Icons.Default.Search, contentDescription = "搜索") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        singleLine = true,
                    )

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xFFF5F5F5))
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "名称",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                        Text(
                            text = "时间",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.width(120.dp)
                        )
                        // 占位，对应右侧箭头的位置
                        Spacer(modifier = Modifier.width(16.dp))
                    }
                }
            },
            itemContent = { case ->
                CaseItem(case = case, onClick = { viewModel.selectCase(case.id) })
                HorizontalDivider()
            }
        )
    }

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CaseItem(
    case: CaseRecord,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = onClick,
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = case.title,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )
            
            Text(
                text = case.createDate,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.width(120.dp)
            )
            
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "查看详情",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

