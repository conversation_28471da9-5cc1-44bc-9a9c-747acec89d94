package com.crrc.siom.ui.test

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.crrc.siom.MainActivity

class TestActivity : ComponentActivity() {
    private val activityList = listOf(
        ActivityItem("MainActivity", MainActivity::class.java),
        // 可在此添加更多 Activity
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MaterialTheme {
                Surface(modifier = Modifier.fillMaxSize()) {
                    ActivityList(activityList) { item ->
                        startActivity(Intent(this, item.clazz))
                    }
                }
            }
        }
    }
}

data class ActivityItem(val name: String, val clazz: Class<*>)

@Composable
fun ActivityList(items: List<ActivityItem>, onClick: (ActivityItem) -> Unit) {
    LazyColumn(modifier = Modifier.fillMaxSize()) {
        items(items) { item ->
            Text(
                text = item.name,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onClick(item) }
                    .padding(16.dp),
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
} 