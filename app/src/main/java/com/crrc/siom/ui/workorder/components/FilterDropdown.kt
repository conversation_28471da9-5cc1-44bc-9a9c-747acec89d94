package com.crrc.siom.ui.workorder.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.crrc.siom.ui.workorder.viewmodel.FilterViewModel
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp

@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun FilterDropdown(
    modifier: Modifier = Modifier,
    onFilterChanged: (Map<String, Any>) -> Unit,
    filterViewModel: FilterViewModel,
) {
    val statusOptions by filterViewModel.statusOptions.collectAsState()
    val stationOptions by filterViewModel.stationOptions.collectAsState()
    val priorityOptions by filterViewModel.priorityOptions.collectAsState()
    val isLoading by filterViewModel.isLoading.collectAsState()

    var expanded by remember { mutableStateOf(false) }
    var tempStatus by remember { mutableStateOf<List<String>>(emptyList()) }
    var tempStation by remember { mutableStateOf<List<String>>(emptyList()) }
    var tempPriority by remember { mutableStateOf<List<String>>(emptyList()) }

    val selectedStatusIds by filterViewModel.selectedStatusIds.collectAsState()
    val selectedStationIds by filterViewModel.selectedStationIds.collectAsState()
    val selectedPriorityIds by filterViewModel.selectedPriorityIds.collectAsState()

    LaunchedEffect(expanded) {
        if (expanded) {
            tempStatus = selectedStatusIds
            tempStation = selectedStationIds
            tempPriority = selectedPriorityIds
        }
    }



    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = it },
        modifier = modifier
    ) {
        Button(
            onClick = { expanded = true },
            modifier = Modifier.menuAnchor()
        ) {
            Icon(
                imageVector = Icons.Default.FilterList,
                contentDescription = "筛选",
                modifier = Modifier.size(20.dp)
            )
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier
                .width(320.dp)
                .height(380.dp)
        ) {
            LazyColumn(
                modifier = Modifier
                    .padding(16.dp)
                    .size(width = 320.dp, height = 380.dp)
            ) {
                item {
                    Text("工单状态", style = MaterialTheme.typography.titleSmall)
                    Spacer(Modifier.height(4.dp))
                    FlowRow(
                        maxItemsInEachRow = 4,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        statusOptions.forEach { option ->
                            val selected = tempStatus.contains(option.getId())
                            Box(
                                modifier = Modifier
                                    .background(
                                        color = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
                                        shape = RoundedCornerShape(50)
                                    )
                                    .clickable {
                                        tempStatus = if (selected) tempStatus - option.getId() else tempStatus + option.getId()
                                    }
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = option.getName(),
                                    color = if (selected) Color.White else MaterialTheme.colorScheme.onSurfaceVariant,
                                    fontSize = 14.sp,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }

                item {
                    Spacer(Modifier.height(12.dp))
                    Text("车站", style = MaterialTheme.typography.titleSmall)
                    Spacer(Modifier.height(4.dp))
                    FlowRow(
                        maxItemsInEachRow = 4,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        stationOptions.forEach { option ->
                            val selected = tempStation.contains(option.getId())
                            Box(
                                modifier = Modifier
                                    .background(
                                        color = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
                                        shape = RoundedCornerShape(50)
                                    )
                                    .clickable {
                                        tempStation = if (selected) tempStation - option.getId() else tempStation + option.getId()
                                    }
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = option.getName(),
                                    color = if (selected) Color.White else MaterialTheme.colorScheme.onSurfaceVariant,
                                    fontSize = 14.sp,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }

                item {
                    Spacer(Modifier.height(12.dp))
                    Text("优先级", style = MaterialTheme.typography.titleSmall)
                    Spacer(Modifier.height(4.dp))
                    FlowRow(
                        maxItemsInEachRow = 4,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        priorityOptions.forEach { option ->
                            val selected = tempPriority.contains(option.getId())
                            Box(
                                modifier = Modifier
                                    .background(
                                        color = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
                                        shape = RoundedCornerShape(50)
                                    )
                                    .clickable {
                                        tempPriority = if (selected) tempPriority - option.getId() else tempPriority + option.getId()
                                    }
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = option.getName(),
                                    color = if (selected) Color.White else MaterialTheme.colorScheme.onSurfaceVariant,
                                    fontSize = 14.sp,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }

                item {
                    Spacer(Modifier.height(16.dp))
                    Row(
                        Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        OutlinedButton(onClick = {
                            tempStatus = emptyList()
                            tempStation = emptyList()
                            tempPriority = emptyList()
                        }) {
                            Text("重置")
                        }
                        Button(onClick = {
                            filterViewModel.resetAllFilters()
                            tempStatus.forEach { filterViewModel.toggleStatusSelection(it) }
                            tempStation.forEach { filterViewModel.toggleStationSelection(it) }
                            tempPriority.forEach { filterViewModel.togglePrioritySelection(it) }
                            onFilterChanged(filterViewModel.buildFilterParams())
                            expanded = false
                        }) {
                            Text("确定")
                        }
                    }
                }
            }
        }
    }
}



