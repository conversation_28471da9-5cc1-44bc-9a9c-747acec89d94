package com.crrc.siom.ui.workorder.viewmodel.process

import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.CaseDetailResponse
import com.crrc.siom.data.repository.CaseRepository
import com.crrc.siom.data.repository.CaseRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

data class CaseDetailUiState(
    val caseDetail: CaseDetailResponse? = null,
    val isLoading: Boolean = false,
    val error: String? = null
)

class CaseDetailViewModel : ViewModel() {
    
    private val _uiState = MutableStateFlow(CaseDetailUiState(isLoading = true))
    val uiState: StateFlow<CaseDetailUiState> = _uiState.asStateFlow()
    
    private val repository: CaseRepository = CaseRepositoryImpl()
    
    fun fetchCaseDetail(caseId: String) {
        _uiState.update { it.copy(isLoading = true, error = null) }
        
        repository.getCaseDetail(caseId) { caseDetail, errorMsg ->
            if (caseDetail != null) {
                _uiState.update { 
                    it.copy(
                        caseDetail = caseDetail,
                        isLoading = false,
                        error = null
                    )
                }
            } else {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = errorMsg ?: "获取案例详情失败"
                    )
                }
            }
        }
    }
} 