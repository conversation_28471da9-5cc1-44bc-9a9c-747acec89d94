package com.crrc.siom.ui.workorder.viewmodel.list

import com.crrc.common.bean.response.EmergencyOrderListResponse
import com.crrc.common.bean.response.OrderFilterParamResponse.FilterParam
import com.crrc.common.utils.GsonUtil
import com.crrc.siom.data.repository.WorkOrderRepository
import com.crrc.siom.data.repository.WorkOrderRepositoryImpl

class EmergencyWorkOrderViewModel :
    BaseWorkOrderViewModelImpl<EmergencyOrderListResponse.EmergencyOrderRecord>() {
    private val workOrderRepository: WorkOrderRepository = WorkOrderRepositoryImpl()

    init {
        refresh()
    }

    override fun loadData(isRefresh: Boolean) {
        workOrderRepository.getEmergencyWorkOrders(
            _currentPage.value,
            pageSize,
            _filterParams.value
        ) { workOrders, error ->
            if (isRefresh) {
                _isLoading.value = false
            } else {
                _isLoadingMore.value = false
                isProcessingLoadMore = false
            }

            if (error != null) {
                _error.value = error
            } else if (workOrders != null) {
                if (isRefresh) {
                    _workOrders.value = workOrders
                } else {
                    _workOrders.value = (_workOrders.value + workOrders).distinctBy { it.id }
                }

                _hasMoreData.value = workOrders.size >= pageSize

                if (workOrders.isNotEmpty()) {
                    _currentPage.value = _currentPage.value + 1
                }
            }
        }
    }

    override fun getWorkOrderRepository(): WorkOrderRepository {
        return workOrderRepository
    }
}