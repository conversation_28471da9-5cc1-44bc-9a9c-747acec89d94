package com.crrc.siom.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

/**
 * 通用单选对话框组件
 * @param T 选项类型
 * @param title 对话框标题
 * @param items 选项列表
 * @param selectedItem 已选中的选项
 * @param itemText 将选项转换为显示文本的函数
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调，参数为选中的选项
 */
@Composable
fun <T> SingleSelectDialog(
    title: String,
    items: List<T>,
    selectedItem: T?,
    itemText: (T) -> String = { it.toString() },
    onDismiss: () -> Unit,
    onConfirm: (T?) -> Unit
) {
    // 本地状态，用于跟踪当前选中项
    var selectedState by remember { mutableStateOf(selectedItem) }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = MaterialTheme.shapes.medium,
            color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 标题
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // 选项列表
                LazyColumn(
                    modifier = Modifier
                        .weight(1f, fill = false)
                        .heightIn(max = 300.dp)
                ) {
                    items(items) { item ->
                        val isSelected = selectedState == item
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { selectedState = item }
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = isSelected,
                                onClick = { selectedState = item }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = itemText(item),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }

                // 按钮区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = { onConfirm(selectedState) }) {
                        Text("确定")
                    }
                }
            }
        }
    }
} 