package com.crrc.siom.ui.emergency.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.EmergencyDetailResponse
import com.crrc.siom.data.repository.EmergencyMonitorRepository
import com.crrc.siom.data.repository.EmergencyMonitorRepositoryImpl
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class EmergencyDetailViewModel : ViewModel() {
    private val emergencyRepository: EmergencyMonitorRepository = EmergencyMonitorRepositoryImpl()

    private val _emergencyDetail = MutableStateFlow<EmergencyDetailResponse?>(null)
    val emergencyDetail: StateFlow<EmergencyDetailResponse?> = _emergencyDetail.asStateFlow()
    
    private val _remainingTime = MutableStateFlow<Long>(0)
    val remainingTime: StateFlow<Long> = _remainingTime.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 加载应急详情
    fun loadEmergencyDetail(uuid: String) {
        _isLoading.value = true
        _error.value = null
        
        try {
            emergencyRepository.getEmergencyEventStatus(uuid) { response, errorMsg ->
                val detail = response
                _emergencyDetail.value = detail
                detail?.time?.let { timeValue ->
                    _remainingTime.value = parseTimeString(timeValue)
                }
                _isLoading.value = false
                startCountdown()
            }

        } catch (e: Exception) {
            _error.value = e.message ?: "未知错误"
            _isLoading.value = false
        }
    }
    
    // 解析时间字符串为秒数 (格式 "HH:MM:SS")
    private fun parseTimeString(timeString: String): Long {
        try {
            val parts = timeString.split(":")
            if (parts.size == 3) {
                val hours = parts[0].toLong()
                val minutes = parts[1].toLong()
                val seconds = parts[2].toLong()
                return hours * 3600 + minutes * 60 + seconds
            }
        } catch (_: Exception) {
        }
        return 0
    }
    
    // 倒计时功能
    private fun startCountdown() {
        viewModelScope.launch {
            while (_remainingTime.value > 0) {
                delay(1000) // 每秒更新一次
                _remainingTime.value = _remainingTime.value - 1
            }
        }
    }
    
    // 格式化时间为 HH:MM:SS
    fun formatTime(seconds: Long): String {
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val secs = seconds % 60
        return String.format("%02d:%02d:%02d", hours, minutes, secs)
    }
} 