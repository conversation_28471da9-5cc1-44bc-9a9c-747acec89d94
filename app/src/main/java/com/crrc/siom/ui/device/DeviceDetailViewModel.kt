package com.crrc.siom.ui.device

import androidx.lifecycle.ViewModel
import com.crrc.common.bean.request.QRcodeRequest
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.crrc.common.bean.response.DeviceDetailResponse
import com.crrc.common.utils.GsonUtil
import com.crrc.siom.data.repository.HomeRepository
import com.crrc.siom.data.repository.HomeRepositoryImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.jvm.java

class DeviceDetailViewModel : ViewModel() {
    private val _deviceDetail = MutableStateFlow<DeviceDetailResponse?>(null)
    val deviceDetail: StateFlow<DeviceDetailResponse?> = _deviceDetail.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()


    private val repository: HomeRepository = HomeRepositoryImpl()

    fun loadDeviceDetail(qrcode: String) {
        _isLoading.value = true
        _error.value = null
        repository.scan(qrcode) { deviceDetailResponses, errorMsg ->
            if (errorMsg != null) {
                _error.value = errorMsg
            } else {
                _deviceDetail.value = deviceDetailResponses
            }
            _isLoading.value = false
        }
    }
    fun loadDeviceDetailById(id: String) {
        _isLoading.value = true
        _error.value = null
        repository.getDeviceDetailById(id) { deviceDetailResponse, errorMsg ->
            if (errorMsg != null) {
                _error.value = errorMsg
            } else {
                _deviceDetail.value = deviceDetailResponse
            }
            _isLoading.value = false
        }
    }
} 