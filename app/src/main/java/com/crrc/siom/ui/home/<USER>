package com.crrc.siom.ui.home

import android.R.attr.padding
import android.app.Activity
import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.siom.ui.workorder.WorkOrderActivity
import com.crrc.siom.ui.components.SearchField
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import com.crrc.siom.ui.caselibrary.CaseLibraryActivity
import com.crrc.siom.ui.components.SiomCard
import com.crrc.siom.ui.emergency.EmergencyMonitorActivity
import com.crrc.siom.ui.theme.extendedColors
import com.crrc.siom.ui.document.DocumentManageActivity
import com.crrc.siom.ui.line.LineMapJavaActivity
import com.crrc.siom.ui.submitFault.SubmitFaultActivity
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.focusable
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.tooling.preview.Preview
import com.crrc.common.bean.response.SearchResponse
import androidx.lifecycle.ViewModel
import com.crrc.siom.ui.workorder.WorkOrderDetailActivity
import androidx.compose.material.icons.filled.Assignment
import androidx.compose.material.icons.filled.Map
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import com.crrc.common.Constant.ID_FLAG
import com.crrc.siom.ui.dashboard.userinfo.UserInfoActivity
import com.crrc.siom.ui.device.DeviceDetailActivity
import com.crrc.siom.ui.qrcode.QRScannerActivity
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun HomeScreen(
    viewModel: HomeViewModel = viewModel()
) {
    val context = LocalContext.current
    val taskCounts by viewModel.taskCounts.observeAsState(TaskCounts())
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            viewModel.refresh()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            if (error != null) {
                Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
            }

            SearchBoxWithDropdown(viewModel = viewModel)
            TempOrderLineMapCard(
                onTempOrderClick = {
                    val intent = Intent(context, WorkOrderActivity::class.java).apply {
                        putExtra("initial_page", 3)
                    }
                    launcher.launch(intent)
                },
                onLineMapClick = {
                    LineMapJavaActivity.start(context)
                }
            )
            TaskOverviewCard(
                counts = taskCounts,
                onPlannedClick = {
//                    WorkOrderActivity.start(context, 0)
                    val intent = Intent(context, WorkOrderActivity::class.java).apply {
                        putExtra("initial_page", 0)
                    }
                    launcher.launch(intent)
                },
                onFaultClick = {
                    val intent = Intent(context, WorkOrderActivity::class.java).apply {
                        putExtra("initial_page", 1)
                    }
                    launcher.launch(intent)
                },
                onEmergencyClick = {
                    val intent = Intent(context, WorkOrderActivity::class.java).apply {
                        putExtra("initial_page", 2)
                    }
                    launcher.launch(intent)
                }
            )
            FunctionButtons()
        }

        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }
    }
}

@Composable
private fun TaskOverviewCard(
    counts: TaskCounts,
    onPlannedClick: () -> Unit,
    onFaultClick: () -> Unit,
    onEmergencyClick: () -> Unit
) {
    SiomCard(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "任务概览",
                style = MaterialTheme.typography.titleLarge
            )
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                TaskCountItem(
                    title = "计划工单",
                    count = counts.planned.toString(),
                    onClick = onPlannedClick
                )
                TaskCountItem(
                    title = "故障工单",
                    count = counts.fault.toString(),
                    onClick = onFaultClick
                )
                TaskCountItem(
                    title = "应急工单",
                    count = counts.emergency.toString(),
                    onClick = onEmergencyClick
                )
            }
        }
    }
}

@Composable
private fun TaskCountItem(
    title: String,
    count: String,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .clickable(onClick = onClick)
            .padding(8.dp),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = count,
                style = MaterialTheme.typography.headlineMedium,
            )
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
private fun FunctionButtons(

) {
    val context = LocalContext.current

    // 功能按钮区域
    SiomCard(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Text(
                text = "常用功能",
                style = MaterialTheme.typography.titleLarge
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 功能按钮网格
            LazyVerticalGrid(
                columns = GridCells.Fixed(4),
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    FunctionButton(
                        text = "排班计划",
                        icon = Icons.Default.Schedule,
                        onClick = {

                        }
                    )
                }
                item {
                    FunctionButton(
                        text = "故障上报",
                        icon = Icons.Default.Report,
                        onClick = {
                            SubmitFaultActivity.start(context)
                        }
                    )
                }
                item {
                    FunctionButton(
                        text = "案例库",
                        icon = Icons.Default.Book,
                        onClick = {
                            CaseLibraryActivity.start(context)
                        }
                    )
                }
                item {
                    FunctionButton(
                        text = "文档管理",
                        icon = Icons.Default.Description,
                        onClick = {
                            DocumentManageActivity.start(context, "")
                        }
                    )
                }
                item {
                    FunctionButton(
                        text = "应急监测",
                        icon = Icons.Default.Warning,
                        onClick = {
                            EmergencyMonitorActivity.start(context)
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun FunctionButton(
    text: String,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        IconButton(
            onClick = onClick,
            modifier = Modifier
                .size(48.dp)
                .background(
                    color = MaterialTheme.extendedColors.iconGround,
                    shape = RoundedCornerShape(12.dp)
                )
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
fun SearchResultList(
    result: SearchResponse,
    onUserClick: (SearchResponse.User) -> Unit = {},
    onDeviceClick: (SearchResponse.Device) -> Unit = {},
    onOrderClick: (SearchResponse.Order) -> Unit = {},
    onFileClick: (SearchResponse.File) -> Unit = {},
) {
    var hasPrevGroup = false
    // 用户
    result.userList?.takeIf { it.isNotEmpty() }?.let { users ->
//        if (hasPrevGroup) Divider(thickness = 0.5.dp)
        hasPrevGroup = true
        DropdownMenuGroup(
            title = "用户",
            items = users,
            onItemClick = onUserClick,
            itemText = { user -> "${user.name} (ID: ${user.userId})" }
        )
    }
    // 设备
    result.deviceList?.takeIf { it.isNotEmpty() }?.let { devices ->
        if (hasPrevGroup) Divider(thickness = 0.5.dp)
        hasPrevGroup = true
        DropdownMenuGroup(
            title = "设备",
            items = devices,
            onItemClick = onDeviceClick,
            itemText = { device -> "${device.deviceName} (ID: ${device.deviceId})" }
        )
    }
    // 工单
    result.orderList?.takeIf { it.isNotEmpty() }?.let { orders ->
        if (hasPrevGroup) Divider(thickness = 0.5.dp)
        hasPrevGroup = true
        DropdownMenuGroup(
            title = "工单",
            items = orders,
            onItemClick = onOrderClick,
            itemText = { order -> "${order.orderTitle} (ID: ${order.orderId})" }
        )
    }
    // 文件
    result.fileList?.takeIf { it.isNotEmpty() }?.let { files ->
        if (hasPrevGroup) Divider(thickness = 0.5.dp)
        hasPrevGroup = true
        DropdownMenuGroup(
            title = "文件",
            items = files,
            onItemClick = onFileClick,
            itemText = { file -> "${file.fileName} (ID: ${file.fileId})" }
        )
    }
}

@Composable
fun <T> DropdownMenuGroup(
    title: String,
    items: List<T>,
    onItemClick: (T) -> Unit,
    itemText: @Composable (T) -> String,
    modifier: Modifier = Modifier
) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleSmall,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 6.dp)
    )

    items.forEach { item ->
        Row(
            modifier = modifier
                .fillMaxWidth()
                .clickable { onItemClick(item) }
                .padding(horizontal = 16.dp, vertical = 4.dp), // 控制紧凑程度
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = itemText(item),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class, ExperimentalMaterial3Api::class)
@Composable
private fun SearchBoxWithDropdown(viewModel: HomeViewModel) {
    val context = LocalContext.current
    val searchResults by viewModel.searchResults.collectAsState()
    var input by remember { mutableStateOf("") }
    var expanded by remember { mutableStateOf(false) }
    val cameraPermissionState = rememberPermissionState(android.Manifest.permission.CAMERA)
    val keyboardController = LocalSoftwareKeyboardController.current
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { shouldExpand ->
            if (shouldExpand && input.isNotBlank() && hasResults(searchResults)) {
                expanded = true
            } else if (!shouldExpand) {
                expanded = false
            }
        }
    ) {
        SearchField(
            value = input,
            onValueChange = {
                input = it
            },
            onAddClick = {
                if (cameraPermissionState.status.isGranted) {
                    (context as? com.crrc.siom.MainActivity)?.let {
                        com.crrc.siom.MainActivity.startQRScanner(it)
                    } ?: run {
                        QRScannerActivity.start(context)
                    }
                } else {
                    cameraPermissionState.launchPermissionRequest()
                }
            },
            modifier = Modifier
                .menuAnchor()
                .fillMaxWidth(),
            keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Search),
            keyboardActions = KeyboardActions(
                onSearch = {
                    viewModel.searchAll(input)
                    keyboardController?.hide()
                    expanded =
                        input.isNotBlank() && searchResults != null && hasResults(searchResults)
                }
            )
        )
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = {
                expanded = false
            },
            modifier = Modifier.background(MaterialTheme.colorScheme.primaryContainer)
        ) {
            if (searchResults != null) {
                SearchResultList(
                    result = searchResults!!,
                    onUserClick = {
                        Toast.makeText(
                            context,
                            "跳转到用户详情: ${it.userId}",
                            Toast.LENGTH_SHORT
                        ).show()
                        UserInfoActivity.start(context, it.userId)
                        expanded = false
                    },
                    onDeviceClick = {
                        Toast.makeText(
                            context,
                            "跳转到设备详情: ${it.deviceId}",
                            Toast.LENGTH_SHORT
                        ).show()
                        DeviceDetailActivity.start(context, it.deviceId, ID_FLAG)
                        expanded = false
                    },
                    onOrderClick = {
                        WorkOrderDetailActivity.start(context, it.orderId)
                        Toast.makeText(
                            context,
                            "跳转到工单详情: ${it.orderId}",
                            Toast.LENGTH_SHORT
                        ).show()
                        expanded = false
                    },
                    onFileClick = {
                        DocumentManageActivity.start(context, it.fileId)
                        Toast.makeText(
                            context,
                            "跳转到文件详情: ${it.fileId}",
                            Toast.LENGTH_SHORT
                        ).show()
                        expanded = false
                    }
                )
            }
        }
    }
}

// 判断是否有检索结果
private fun hasResults(searchResults: SearchResponse?): Boolean {
    return searchResults != null &&
            (searchResults.userList?.isNotEmpty() == true ||
                    searchResults.deviceList?.isNotEmpty() == true ||
                    searchResults.orderList?.isNotEmpty() == true ||
                    searchResults.fileList?.isNotEmpty() == true)
}

@Composable
private fun TempOrderLineMapCard(
    onTempOrderClick: () -> Unit,
    onLineMapClick: () -> Unit
) {
    SiomCard(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp, horizontal = 8.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            FunctionButton(
                text = "临时工单",
                icon = Icons.Default.Assignment,
                onClick = onTempOrderClick,
                modifier = Modifier.weight(1f)
            )
            Spacer(modifier = Modifier.width(16.dp))
            FunctionButton(
                text = "线路图",
                icon = Icons.Default.Map,
                onClick = onLineMapClick,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

class MockHomeViewModel : ViewModel() {
    val taskCounts = mutableStateOf(TaskCounts())
    val isLoading = mutableStateOf(false)
    val error = mutableStateOf<String?>(null)
    val searchResults = mutableStateOf(
        SearchResponse().apply {
            userList = listOf(
                SearchResponse.User().apply {
                    userId = "u1"
                    name = "张三"
                },
                SearchResponse.User().apply {
                    userId = "u2"
                    name = "李四"
                }
            )
            deviceList = listOf(
                SearchResponse.Device().apply {
                    deviceId = "d1"
                    deviceName = "设备A"
                }
            )
            orderList = listOf(
                SearchResponse.Order().apply {
                    orderId = "o1"
                    orderTitle = "工单A"
                }
            )
            fileList = listOf(
                SearchResponse.File().apply {
                    fileId = "f1"
                    fileName = "文件A"
                }
            )
        }
    )

    fun searchAll(query: String) {}
    fun refresh() {}
}

@Preview(showBackground = true)
@Composable
fun PreviewHomeScreen() {
    val mockViewModel = remember { MockHomeViewModel() }
    CompositionLocalProvider(LocalContext provides LocalContext.current) {
        var searchQuery by remember { mutableStateOf("张") }
        var expanded by remember { mutableStateOf(true) }
        MaterialTheme {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Box(modifier = Modifier.fillMaxWidth()) {
                    SearchField(
                        value = searchQuery,
                        onValueChange = {
                            searchQuery = it
                            expanded =
                                it.isNotBlank() && (mockViewModel.searchResults.value != null)
                        },
                        onAddClick = {},
                        onSearch = {}
                    )
                    if (expanded && searchQuery.isNotBlank()) {
                        DropdownMenu(
                            expanded = expanded && searchQuery.isNotBlank(),
                            onDismissRequest = { expanded = false },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp)
                                .background(MaterialTheme.colorScheme.primary)
                        ) {
                            SearchResultList(
                                result = mockViewModel.searchResults.value,
                                onUserClick = {},
                                onDeviceClick = {},
                                onOrderClick = {},
                                onFileClick = {}
                            )
                        }
                    }
                }
            }
        }
    }
}