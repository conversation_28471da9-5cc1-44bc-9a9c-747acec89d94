package com.crrc.siom.ui.home

import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.SearchResponse
import com.crrc.siom.data.repository.HomeRepository
import com.crrc.siom.data.repository.HomeRepositoryImpl
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

data class TaskCounts(
    val planned: Int = 0,
    val fault: Int = 0,
    val emergency: Int = 0
)

@OptIn(FlowPreview::class)
class HomeViewModel(private val repository: HomeRepository = HomeRepositoryImpl()) : ViewModel() {
    private val _taskCounts = MutableLiveData(TaskCounts())
    val taskCounts: LiveData<TaskCounts> = _taskCounts
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error
    
    private val _searchResults = MutableStateFlow<SearchResponse?>(null)
    val searchResults: StateFlow<SearchResponse?> = _searchResults
    private val _searchQuery = MutableStateFlow("")

    val searchQuery: StateFlow<String> = _searchQuery
    fun onSearchQueryChanged(query: String) {
        _searchQuery.value = query
    }

    init {
        loadTaskOverview()
        viewModelScope.launch {
            _searchQuery
                .debounce(400) // 400ms防抖
                .distinctUntilChanged()
                .collect { query ->
                    if (query.isNotBlank()) {
                        searchAll(query)
                    } else {
                        _searchResults.value = null
                    }
                }
        }
    }

    fun loadTaskOverview() {
        _isLoading.value = true
        repository.getTaskOverview { taskCounts, error ->
            if (taskCounts != null) {
                _taskCounts.value = taskCounts
                _error.value = null
            } else {
                _error.value = error
            }
            _isLoading.value = false
        }
    }

    fun searchAll(query: String) {
        repository.search(query) { results, error ->
            _searchResults.value = results
        }
//        viewModelScope.launch {
//            // 模拟网络延迟
//            delay(800)
//
//            // 构造 mock 数据
//            val mockResult = SearchResponse().apply {
//                userList = listOf(
//                    SearchResponse.User().apply {
//                        userId = "u1"
//                        name = "张三"
//                    },
//                    SearchResponse.User().apply {
//                        userId = "u2"
//                        name = "李四"
//                    }
//                )
//                deviceList = listOf(
//                    SearchResponse.Device().apply {
//                        deviceId = "d1"
//                        deviceName = "设备A"
//                    }
//                )
//                orderList = listOf(
//                    SearchResponse.Order().apply {
//                        orderId = "o1"
//                        orderTitle = "工单A"
//                    }
//                )
//                fileList = listOf(
//                    SearchResponse.File().apply {
//                        fileId = "f1"
//                        fileName = "文件A"
//                    }
//                )
//            }
//
//            // 返回 mock 数据
//            _searchResults.value = mockResult
//        }
    }
    
    fun refresh() {
        loadTaskOverview()
    }

}