package com.crrc.siom.ui.dashboard.groupInfo

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material3.ExperimentalMaterial3Api
import com.crrc.siom.ui.theme.SiomTheme

class GroupInfoActivity : ComponentActivity() {

    companion object {
        private const val GROUP_ID = "group_id"

        fun start(context: Context, groupId: String) {
            val intent = Intent(context, GroupInfoActivity::class.java).apply {
                putExtra(GROUP_ID, groupId)
            }
            context.startActivity(intent)
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val groupId = intent.getStringExtra(GROUP_ID)
        setContent {
            SiomTheme {
                GroupInfoScreen(
                    groupId = groupId.toString(),
                    onBackClick = { finish() })
            }
        }
    }
} 