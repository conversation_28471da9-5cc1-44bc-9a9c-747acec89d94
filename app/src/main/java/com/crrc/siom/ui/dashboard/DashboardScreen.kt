package com.crrc.siom.ui.dashboard

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.siom.ui.dashboard.detail.ChatDetailActivity


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardScreen(
    viewModel: DashboardViewModel = viewModel()
) {
    val selectedTab by viewModel.selectedTab.collectAsState()
    val context = LocalContext.current

    Column {
        TopAppBar(
            title = {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    Surface(
                        modifier = Modifier.width(200.dp),
                        shape = MaterialTheme.shapes.small,
                        border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline)
                    ) {
                        Row(
                            modifier = Modifier.height(32.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight()
                                    .clickable { viewModel.setSelectedTab(0) },
                                contentAlignment = androidx.compose.ui.Alignment.Center
                            ) {
                                Text(
                                    "聊天",
                                    color = if (selectedTab == 0)
                                        MaterialTheme.colorScheme.primary
                                        else MaterialTheme.colorScheme.onSurfaceVariant,
                                    style = MaterialTheme.typography.labelLarge
                                )
                            }
                            
                            VerticalDivider(
                                modifier = Modifier.height(32.dp),
                                color = MaterialTheme.colorScheme.outline
                            )
                            
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight()
                                    .clickable { viewModel.setSelectedTab(1) },
                                contentAlignment = androidx.compose.ui.Alignment.Center
                            ) {
                                Text(
                                    "通讯录",
                                    color = if (selectedTab == 1)
                                        MaterialTheme.colorScheme.primary
                                        else MaterialTheme.colorScheme.onSurfaceVariant,
                                    style = MaterialTheme.typography.labelLarge
                                )
                            }
                        }
                    }
                }
            },
            actions = {
                IconButton(onClick = { }) {
                    Icon(Icons.Default.Search, contentDescription = "搜索")
                }
            }
        )
        
        HorizontalDivider()
        
        Box(modifier = Modifier.weight(1f)) {
            when (selectedTab) {
                0 -> {
                    ChatListScreen(
                        onChatClick = { userOrGroupId,userOrGroupName,chatType ->
                            ChatDetailActivity.start(context, userOrGroupId,userOrGroupName,chatType)
                        }
                    )
                }
                else -> {
                    ContactsScreen(
                        onChatClick = { userOrGroupId,userOrGroupName,chatType ->
                            ChatDetailActivity.start(context, userOrGroupId,userOrGroupName,chatType)
                        }
                    )
                }
            }
        }
    }
}