package com.crrc.siom.ui.emergency

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.siom.ui.emergency.viewmodel.EmergencyDetailViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmergencyDetailScreen(
    emergencyId: String,
    emergencyContent: String,
    onBackClick: () -> Unit,
    viewModel: EmergencyDetailViewModel = viewModel()
) {
    val emergencyDetail by viewModel.emergencyDetail.collectAsState()
    val remainingTime by viewModel.remainingTime.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    LaunchedEffect(emergencyId) {
        viewModel.loadEmergencyDetail(emergencyId)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("应急详情") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (error != null) {
                Text(
                    text = "加载失败: ${error}",
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(16.dp)
                )
            } else if (emergencyDetail == null) {
                Text(
                    text = "未找到应急详情",
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(16.dp)
                )
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                        .padding(16.dp)
                ) {
                    // 内容详情部分
                    SectionTitle(title = "应急内容详情")
                    
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface
                        )
                    ) {
                        Text(
                            text = emergencyContent,
                            modifier = Modifier.padding(16.dp),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 应急时间部分 - 优化为Android风格
                    SectionTitle(title = "应急时间详情")
                    
                    CountdownTimer(
                        remainingSeconds = remainingTime,
                        formattedTime = viewModel.formatTime(remainingTime)
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 应急处置建议 - 优化为Android风格
                    SectionTitle(title = "应急处置建议")
                    
                    emergencyDetail?.suggestion?.let { suggestions ->
                        SuggestionTimeline(suggestions = suggestions)
                    }
                }
            }
        }
    }
}

@Composable
fun SectionTitle(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        color = MaterialTheme.colorScheme.primary,
        modifier = Modifier.padding(vertical = 8.dp)
    )
}

@Composable
fun CountdownTimer(
    remainingSeconds: Long,
    formattedTime: String
) {
    // 创建脉动动画
    val infiniteTransition = rememberInfiniteTransition()
    val scale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        )
    )
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .height(180.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(Color(0xFF1A1A2E)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "已经启动应急",
                color = Color.White,
                style = MaterialTheme.typography.bodyLarge
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "剩余时间",
                color = Color.Red,
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 数字时钟样式
            Box(
                modifier = Modifier
                    .scale(scale)
                    .background(
                        color = Color.Black,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                Text(
                    text = formattedTime,
                    color = Color.Red,
                    fontSize = 36.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun SuggestionTimeline(suggestions: List<String>) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(Color(0xFF0A2342))
            .padding(16.dp)
    ) {
        Column {
            suggestions.forEachIndexed { index, suggestion ->
                SuggestionItem(
                    number = index + 1,
                    content = suggestion,
                    isLast = index == suggestions.size - 1
                )
            }
        }
    }
}

@Composable
fun SuggestionItem(
    number: Int,
    content: String,
    isLast: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        // 左侧数字和连接线
        Box(
            modifier = Modifier.width(40.dp),
            contentAlignment = Alignment.TopCenter
        ) {
            // 数字圆圈
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(Color(0xFF2A9D8F), CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = number.toString(),
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )
            }
            
            // 连接线
            if (!isLast) {
                Canvas(
                    modifier = Modifier
                        .padding(top = 32.dp)
                        .width(2.dp)
                        .height(40.dp)
                ) {
                    drawLine(
                        color = Color(0xFF2A9D8F),
                        start = Offset(size.width / 2, 0f),
                        end = Offset(size.width / 2, size.height),
                        strokeWidth = 4f,
                        cap = StrokeCap.Round
                    )
                }
            }
        }
        
        // 右侧内容
        Surface(
            modifier = Modifier
                .padding(start = 8.dp)
                .weight(1f),
            shape = RoundedCornerShape(8.dp),
            color = Color(0xFF264653)
        ) {
            Text(
                text = content,
                color = Color.White,
                modifier = Modifier.padding(12.dp),
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
} 