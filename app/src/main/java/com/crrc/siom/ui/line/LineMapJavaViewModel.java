package com.crrc.siom.ui.line;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.crrc.common.bean.response.LineDevice;
import com.crrc.common.bean.response.LineTool;
import com.crrc.common.bean.response.StationAlarmResponse;
import com.crrc.siom.data.model.SampleLineData;
import com.crrc.siom.data.model.Station;
import com.crrc.siom.data.model.StoreInfo;
import com.crrc.siom.data.repository.LineMapRepository;
import com.crrc.siom.data.repository.LineMapRepositoryImpl;

import java.util.ArrayList;
import java.util.List;
import kotlin.Unit;
import kotlin.jvm.functions.Function2;

/**
 * Java版本的LineMapViewModel
 */
public class LineMapJavaViewModel extends ViewModel {
    private static final String TAG = "LineMapJavaViewModel";
    
    private final LineMapRepository repository;
    
    // 站点列表
    private final MutableLiveData<List<Station>> stations = new MutableLiveData<>(new ArrayList<>());
    
    // 当前选中的站点
    private final MutableLiveData<String> currentStation = new MutableLiveData<>("南万站");
    
    // 加载状态
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    
    // 错误信息
    private final MutableLiveData<String> error = new MutableLiveData<>(null);
    
    // 显示模式
    private final MutableLiveData<MarkerDisplayMode> displayMode = new MutableLiveData<>(MarkerDisplayMode.NORMAL);
    
    // 报警信息
    private final MutableLiveData<List<StationAlarmResponse>> stationAlarmInfo = new MutableLiveData<>();
    // 备品备件
    private final MutableLiveData<List<LineDevice>> lineDeviceInfo = new MutableLiveData<>();
    // 工器具
    private final MutableLiveData<List<LineTool>> lineToolsInfo = new MutableLiveData<>();
    
    public LineMapJavaViewModel() {
        repository = new LineMapRepositoryImpl();
        loadLineInfo();
    }
    
    /**
     * 加载线路信息
     */
    public void loadLineInfo() {
        isLoading.setValue(true);
        error.setValue(null);
        
        // 使用Kotlin的Function2接口创建回调
        repository.getLineInfo((lineInfoList, errorMsg) -> {
            isLoading.postValue(false);
            
            if (errorMsg != null) {
                error.postValue(errorMsg);
                return Unit.INSTANCE;
            }
            
            try {
                List<Station> updatedStations = updateStationsFromApi(lineInfoList);
                stations.postValue(updatedStations);
            } catch (Exception e) {
                Log.e(TAG, "更新站点数据失败: " + e.getMessage());
                error.postValue("数据处理错误: " + e.getMessage());
            }
            
            return Unit.INSTANCE;
        });
    }
    
    /**
     * 根据API返回数据更新站点列表
     */
    private List<Station> updateStationsFromApi(List<?> lineInfoList) {
        List<Station> result = new ArrayList<>();
        
        // 获取本地示例数据作为基础
        List<Station> localStations = SampleLineData.INSTANCE.getStations();
        
        for (Station localStation : localStations) {
            if (lineInfoList != null && !lineInfoList.isEmpty()) {
                // 在API数据中寻找匹配项
                boolean found = false;
                for (Object lineInfo : lineInfoList) {
                    try {
                        // 通过反射获取属性，以适应不同版本的API
                        String stationId = (String) lineInfo.getClass().getMethod("getStationId").invoke(lineInfo);
                        
                        if (stationId != null && stationId.equals(localStation.getStationId())) {
                            found = true;
                            
                            // 获取报警数量
                            int alarmNum = 0;
                            try {
                                alarmNum = (int) lineInfo.getClass().getMethod("getOperationAlarmNum").invoke(lineInfo);
                            } catch (Exception e) {
                                Log.e(TAG, "获取报警数量失败: " + e.getMessage());
                            }
                            
                            // 获取库房信息
                            StoreInfo storeInfo = null;
                            try {
                                Object store = lineInfo.getClass().getMethod("getOperationStore").invoke(lineInfo);
                                if (store != null) {
                                    String name = (String) store.getClass().getMethod("getName").invoke(store);
                                    String location = (String) store.getClass().getMethod("getLocation").invoke(store);
                                    storeInfo = new StoreInfo(name, location);
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "获取库房信息失败: " + e.getMessage());
                            }
                            
                            // 创建更新后的站点
                            Station updatedStation = new Station(
                                    localStation.getStationId(),
                                    localStation.getName(),
                                    localStation.getX(),
                                    localStation.getY(),
                                    localStation.getAlarms(),
                                    alarmNum > 0,
                                    alarmNum,
                                    storeInfo
                            );
                            
                            result.add(updatedStation);
                            break;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "处理站点数据失败: " + e.getMessage());
                    }
                }
                
                // 如果API中没有找到，则使用本地数据
                if (!found) {
                    result.add(localStation);
                }
            } else {
                // 如果API没有返回数据，使用本地数据
                result.add(localStation);
            }
        }
        
        return result;
    }
    
    /**
     * 设置显示模式
     */
    public void setDisplayMode(MarkerDisplayMode mode) {
        displayMode.setValue(mode);
    }
    
    /**
     * 当站点被点击时更新当前站点
     */
    public void onStationClick(String stationName) {
        currentStation.setValue(stationName);
    }
    
    // 获取站点报警信息
    public void getStationAlarmInfo(String stationId) {
        isLoading.setValue(true);
        repository.getStationAlarmInfo(stationId, (response, errorMsg) -> {
            isLoading.postValue(false);
            if (errorMsg != null) {
                error.postValue(errorMsg);
                return Unit.INSTANCE;
            }
            stationAlarmInfo.postValue((List<StationAlarmResponse>) response);
            return Unit.INSTANCE;
        });
    }

    // 获取备品备件信息
    public void getLineDeviceInfo(String storeId) {
        isLoading.setValue(true);
        repository.getLineDeviceInfo(storeId, (response, errorMsg) -> {
            isLoading.postValue(false);
            if (errorMsg != null) {
                error.postValue(errorMsg);
                return Unit.INSTANCE;
            }
            lineDeviceInfo.postValue((List<LineDevice>) response);
            return Unit.INSTANCE;
        });
    }

    // 获取工器具信息
    public void getLineToolsInfo(String storeId) {
        isLoading.setValue(true);
        repository.getLineToolsInfo(storeId, (response, errorMsg) -> {
            isLoading.postValue(false);
            if (errorMsg != null) {
                error.postValue(errorMsg);
                return Unit.INSTANCE;
            }
            lineToolsInfo.postValue((List<LineTool>) response);
            return Unit.INSTANCE;
        });
    }

    // Getters for LiveData
    public LiveData<List<Station>> getStations() {
        return stations;
    }
    
    public LiveData<String> getCurrentStation() {
        return currentStation;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getError() {
        return error;
    }
    
    public LiveData<List<StationAlarmResponse>> getStationAlarmInfo() {
        return stationAlarmInfo;
    }

    public LiveData<List<LineDevice>> getLineDeviceInfo() {
        return lineDeviceInfo;
    }

    public LiveData<List<LineTool>> getLineToolsInfo() {
        return lineToolsInfo;
    }
} 