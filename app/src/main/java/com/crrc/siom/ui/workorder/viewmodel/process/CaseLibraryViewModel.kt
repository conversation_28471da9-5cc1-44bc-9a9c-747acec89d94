package com.crrc.siom.ui.workorder.pages.process

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.CaseListResponse.CaseRecord
import com.crrc.siom.data.repository.CaseRepository
import com.crrc.siom.data.repository.CaseRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

data class CaseLibraryUiState(
    val cases: List<CaseRecord> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val searchQuery: String = "",
    val currentPage: Int = 1,
    val hasMoreData: Boolean = false,
    val selectedCaseId: String? = null
)

class CaseLibraryViewModel : ViewModel() {
    
    private val _uiState = MutableStateFlow(CaseLibraryUiState())
    val uiState: StateFlow<CaseLibraryUiState> = _uiState.asStateFlow()
    
    private val repository: CaseRepository = CaseRepositoryImpl()
    
    fun fetchCases(workOrderId: String, page: Int = 1) {
        _uiState.update { it.copy(isLoading = true, error = null) }
        
        repository.getCaseFaultList(workOrderId,page, 10) { records, errorMsg ->
            if (records != null) {
                _uiState.update { state ->
                    state.copy(
                        cases = if (page == 1) records else state.cases + records,
                        currentPage = page,
                        // 假设每页10条，如果当前页的记录数小于10，说明没有更多数据了
                        hasMoreData = records.size >= 10,
                        isLoading = false
                    )
                }
            } else {
                _uiState.update { it.copy(error = errorMsg, isLoading = false) }
            }
        }
    }
    fun getCaseList( page: Int = 1) {
        _uiState.update { it.copy(isLoading = true, error = null) }

        repository.getCaseList(page, 10) { records, errorMsg ->
            if (records != null) {
                _uiState.update { state ->
                    state.copy(
                        cases = if (page == 1) records else state.cases + records,
                        currentPage = page,
                        hasMoreData = records.size >= 10,
                        isLoading = false
                    )
                }
            } else {
                _uiState.update { it.copy(error = errorMsg, isLoading = false) }
            }
        }
    }
    
    fun searchCases(query: String) {
        _uiState.update { it.copy(searchQuery = query) }
        
        if (query.isNotBlank()) {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            repository.searchCases(query, 1, 10) { records, errorMsg ->
                if (records != null) {
                    _uiState.update { state ->
                        state.copy(
                            cases = records,
                            currentPage = 1,
                            hasMoreData = records.size >= 10,
                            isLoading = false
                        )
                    }
                } else {
                    _uiState.update { it.copy(error = errorMsg, isLoading = false) }
                }
            }
        } else {
            // 如果搜索词为空，重新加载第一页数据
            fetchCases("", 1)
        }
    }
    
    fun loadMoreCases(workOrderId: String) {
        if (_uiState.value.hasMoreData && !_uiState.value.isLoading) {
            fetchCases(workOrderId, _uiState.value.currentPage + 1)
        }
    }
    fun loadMoreCases() {
        if (_uiState.value.hasMoreData && !_uiState.value.isLoading) {
            getCaseList( _uiState.value.currentPage + 1)
        }
    }
    
    fun selectCase(caseId: String) {
        _uiState.update { it.copy(selectedCaseId = caseId) }
        // 选择案例的逻辑可以在这里实现
    }
    
    fun clearSelectedCase() {
        _uiState.update { it.copy(selectedCaseId = null) }
    }
} 