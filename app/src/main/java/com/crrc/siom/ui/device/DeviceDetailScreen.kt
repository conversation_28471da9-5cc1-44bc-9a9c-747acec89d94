package com.crrc.siom.ui.device

import android.app.Activity
import android.text.TextUtils
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.DeviceDetailResponse
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import com.crrc.common.Constant.ID_FLAG
import com.crrc.common.Constant.QR_FLAG
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceDetailScreen(
    param: String,
    type: String,
    viewModel: DeviceDetailViewModel = viewModel()
) {
    val deviceDetail by viewModel.deviceDetail.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        if (TextUtils.equals(type, QR_FLAG)) {
            viewModel.loadDeviceDetail(param)
        } else if (TextUtils.equals(type, ID_FLAG)) {
            viewModel.loadDeviceDetailById(param)
        }

    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("设备详情") },
                navigationIcon = {
                    IconButton(onClick = { (context as? Activity)?.finish() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }

                error != null -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "加载失败: ${error}",
                            color = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = {
                                if (TextUtils.equals(type, QR_FLAG)) {
                                    viewModel.loadDeviceDetail(param)
                                } else if (TextUtils.equals(type, ID_FLAG)) {
                                    viewModel.loadDeviceDetailById(param)
                                }
                            }
                        ) {
                            Text("重试")
                        }
                    }
                }

                deviceDetail != null -> {
                    DeviceDetailContent(deviceDetail!!)
                }
            }
        }
    }
}

@Composable
fun DeviceDetailContent(deviceDetail: DeviceDetailResponse) {
    val detailItems = listOf(
        "设备编号" to deviceDetail.internalId,
        "设备名称" to deviceDetail.name,
        "设备类型" to deviceDetail.type,
        "所属站点" to deviceDetail.station,
        "位置描述" to deviceDetail.location,
        "位置编号" to deviceDetail.locationCode,
        "上线时间" to deviceDetail.onlineTime,
        "使用状态" to deviceDetail.status,
        "更换周期" to deviceDetail.replacementPeriod + "小时",
        "维护周期" to deviceDetail.maintenancePeriod + "小时",
        "质保到期时间" to deviceDetail.warrantyExpirationTime
    )
    Column(modifier = Modifier.padding(16.dp)) {
        detailItems.forEach { (label, value) ->
            DetailItem(label, value?.toString())
        }
    }
}

@Composable
fun DetailItem(label: String, value: String?) {
    Row(
        Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            modifier = Modifier.weight(1f),
            color = Color.Gray,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = value ?: "",
            modifier = Modifier.weight(2f)
        )
    }
    Divider()
}

