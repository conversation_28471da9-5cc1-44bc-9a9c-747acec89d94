package com.crrc.siom.ui.workorder.pages

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.PickMaterialResponse
import com.crrc.siom.ui.workorder.viewmodel.PickMaterialViewModel
import android.widget.Toast
import androidx.compose.ui.tooling.preview.Preview

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PickMaterialPage(
    workOrderId: String,
    onBackClick: () -> Unit,
    onPickSuccess: () -> Unit,
    viewModel: PickMaterialViewModel = viewModel()
) {
    val pickMaterialList by viewModel.pickMaterialList.collectAsState()
    val selectedStandbyItems by viewModel.selectedStandbyItems.collectAsState()
    val selectedToolItems by viewModel.selectedToolItems.collectAsState()
    val itemQuantities by viewModel.itemQuantities.collectAsState()
    val itemMaxQuantities by viewModel.itemStock.collectAsState()
    val quantityError by viewModel.quantityError.collectAsState()
    val note by viewModel.note.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val context = LocalContext.current
    
    // 显示数量错误提示
    LaunchedEffect(quantityError) {
        quantityError?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            viewModel.clearQuantityError()
        }
    }
    
    // 加载领料清单
    LaunchedEffect(workOrderId) {
        viewModel.loadPickMaterialList(workOrderId)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("领料清单") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                error != null -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "加载失败: $error",
                            color = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { viewModel.loadPickMaterialList(workOrderId) }
                        ) {
                            Text("重试")
                        }
                    }
                }
                pickMaterialList != null -> {
                    PickMaterialContent(
                        pickMaterialList = pickMaterialList!!,
                        selectedStandbyItems = selectedStandbyItems,
                        selectedToolItems = selectedToolItems,
                        itemQuantities = itemQuantities,
                        itemMaxQuantities = itemMaxQuantities,
                        note = note,
                        onStandbyItemToggle = { viewModel.toggleStandbyItem(it) },
                        onToolItemToggle = { viewModel.toggleToolItem(it) },
                        onQuantityChange = { id, quantity -> viewModel.updateItemQuantity(id, quantity) },
                        onNoteChange = { viewModel.updateNote(it) },
                        onConfirm = {
                            viewModel.confirmPickMaterial(workOrderId,
                                onSuccess = {
                                    Toast.makeText(context, "领料成功", Toast.LENGTH_SHORT).show()
                                    onPickSuccess()
                                },
                                onError = {
                                    Toast.makeText(context, "领料失败: $it", Toast.LENGTH_SHORT).show()
                                }
                            )
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun PickMaterialContent(
    pickMaterialList: PickMaterialResponse,
    selectedStandbyItems: List<Int>,
    selectedToolItems: List<Int>,
    itemQuantities: Map<Int, Int>,
    itemMaxQuantities: Map<Int, Int>,
    note: String,
    onStandbyItemToggle: (Int) -> Unit,
    onToolItemToggle: (Int) -> Unit,
    onQuantityChange: (Int, Int) -> Unit,
    onNoteChange: (String) -> Unit,
    onConfirm: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // 工器具
        MaterialSection(
            title = "工器具",
            items = pickMaterialList.toolItems ?: emptyList(),
            selectedItems = selectedToolItems,
            itemQuantities = itemQuantities,
            itemMaxQuantities = itemMaxQuantities,
            onItemToggle = onToolItemToggle,
            onQuantityChange = onQuantityChange
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 备品备件
        MaterialSection(
            title = "备品备件",
            items = pickMaterialList.standbyItems ?: emptyList(),
            selectedItems = selectedStandbyItems,
            itemQuantities = itemQuantities,
            itemMaxQuantities = itemMaxQuantities,
            onItemToggle = onStandbyItemToggle,
            onQuantityChange = onQuantityChange
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 备注
        Text(
            text = "备注",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = note,
            onValueChange = onNoteChange,
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("请输入...") },
            minLines = 3
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 提交按钮
        Button(
            onClick = onConfirm,
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
        ) {
            Text("提交")
        }
    }
}

@Composable
private fun MaterialSection(
    title: String,
    items: List<PickMaterialResponse.Item>,
    selectedItems: List<Int>,
    itemQuantities: Map<Int, Int>,
    itemMaxQuantities: Map<Int, Int>,
    onItemToggle: (Int) -> Unit,
    onQuantityChange: (Int, Int) -> Unit
) {
    var expanded by remember { mutableStateOf(true) }
    var searchText by remember { mutableStateOf("") }
    
    // 过滤物品列表
    val filteredItems = remember(items, searchText) {
        if (searchText.isEmpty()) {
            items
        } else {
            items.filter { it.name.contains(searchText, ignoreCase = true) }
        }
    }
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.primary,
        shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = !expanded }
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
            
            Icon(
                imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                contentDescription = if (expanded) "收起" else "展开",
                tint = Color.White
            )
        }
    }
    
    if (expanded) {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color.White,
            shape = RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp),
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                // 搜索框
                SearchBar(
                    searchText = searchText,
                    onSearchTextChange = { searchText = it }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 物品列表
                if (filteredItems.isEmpty()) {
                    Text(
                        text = "没有找到匹配的物品",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(vertical = 16.dp)
                    )
                } else {
                    filteredItems.forEach { item ->
                        val isSelected = selectedItems.contains(item.id)
                        val quantity = itemQuantities[item.id] ?: 0
                        val maxQuantity = itemMaxQuantities[item.id] ?: item.amount
                        
                        MaterialItemRow(
                            name = item.name,
                            maxQuantity = maxQuantity,
                            quantity = quantity,
                            isSelected = isSelected,
                            onToggle = { onItemToggle(item.id) },
                            onQuantityChange = { newQuantity -> onQuantityChange(item.id, newQuantity) }
                        )
                        
                        Divider(modifier = Modifier.padding(vertical = 8.dp))
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchBar(
    searchText: String,
    onSearchTextChange: (String) -> Unit
) {
    OutlinedTextField(
        value = searchText,
        onValueChange = onSearchTextChange,
        modifier = Modifier.fillMaxWidth(),
        placeholder = { Text("请输入文本") },
        leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
        trailingIcon = {
            if (searchText.isNotEmpty()) {
                IconButton(onClick = { onSearchTextChange("") }) {
                    Icon(Icons.Default.Close, contentDescription = "清除")
                }
            }
        },
        singleLine = true,
        shape = RoundedCornerShape(8.dp)
    )
}

@Composable
private fun MaterialItemRow(
    name: String,
    maxQuantity: Int,
    quantity: Int,
    isSelected: Boolean,
    onToggle: () -> Unit,
    onQuantityChange: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = isSelected,
            onCheckedChange = { onToggle() }
        )
        
        Text(
            text = name,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
        
        // 数量选择器
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 数量显示
            Text(
                text = quantity.toString(),
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(horizontal = 8.dp)
            )
            
            // 上下箭头
            Column {
                IconButton(
                    onClick = { 
                        if (quantity < maxQuantity) {
                            onQuantityChange(quantity + 1)
                        }
                    },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowUp,
                        contentDescription = "增加",
                        modifier = Modifier.size(16.dp)
                    )
                }
                
                IconButton(
                    onClick = { 
                        if (quantity > 0) {
                            onQuantityChange(quantity - 1)
                        }
                    },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "减少",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, name = "PickMaterialPage Preview")
@Composable
fun PreviewPickMaterialContent() {
    // 构造mock数据
    val toolItems = listOf(
        PickMaterialResponse.Item().apply {
            setId(1)
            setName("扳手")
            setAmount(2)
            setStock(10)
        },
        PickMaterialResponse.Item().apply {
            setId(2)
            setName("螺丝刀")
            setAmount(1)
            setStock(5)
        }
    )
    val standbyItems = listOf(
        PickMaterialResponse.Item().apply {
            setId(3)
            setName("保险丝")
            setAmount(5)
            setStock(20)
        }
    )
    val pickMaterialList = PickMaterialResponse().apply {
        setToolItems(toolItems)
        setStandbyItems(standbyItems)
        setNote("")
    }
    val selectedStandbyItems = remember { mutableStateListOf<Int>() }
    val selectedToolItems = remember { mutableStateListOf<Int>() }
    val itemQuantities = remember { mutableStateMapOf<Int, Int>() }
    val itemMaxQuantities = remember { mutableStateMapOf<Int, Int>().apply {
        put(1, 2)
        put(2, 1)
        put(3, 5)
    } }
    var note by remember { mutableStateOf("") }
    MaterialTheme {
        PickMaterialContent(
            pickMaterialList = pickMaterialList,
            selectedStandbyItems = selectedStandbyItems,
            selectedToolItems = selectedToolItems,
            itemQuantities = itemQuantities,
            itemMaxQuantities = itemMaxQuantities,
            note = note,
            onStandbyItemToggle = { id ->
                if (selectedStandbyItems.contains(id)) selectedStandbyItems.remove(id) else selectedStandbyItems.add(id)
            },
            onToolItemToggle = { id ->
                if (selectedToolItems.contains(id)) selectedToolItems.remove(id) else selectedToolItems.add(id)
            },
            onQuantityChange = { id, quantity -> itemQuantities[id] = quantity },
            onNoteChange = { note = it },
            onConfirm = {}
        )
    }
} 