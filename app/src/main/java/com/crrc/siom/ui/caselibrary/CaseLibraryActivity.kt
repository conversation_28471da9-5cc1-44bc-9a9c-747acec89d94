package com.crrc.siom.ui.caselibrary

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.collectAsState
import androidx.lifecycle.ViewModelProvider
import com.crrc.siom.ui.theme.SiomTheme
import com.crrc.siom.ui.workorder.pages.process.CaseDetailPage
import com.crrc.siom.ui.workorder.viewmodel.process.CaseLibraryViewModel
import androidx.compose.runtime.getValue

class CaseLibraryActivity : ComponentActivity() {
    private lateinit var caseLibraryViewModel: CaseLibraryViewModel

    companion object {
        fun start(context: Context) {
            context.startActivity(Intent(context, CaseLibraryActivity::class.java))
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        caseLibraryViewModel = ViewModelProvider(this)[CaseLibraryViewModel::class.java]
        setContent {
            SiomTheme {
                val caseUiState by caseLibraryViewModel.uiState.collectAsState()
                if (caseUiState.selectedCaseId != null) {
                    CaseDetailPage(
                        caseId = caseUiState.selectedCaseId!!,
                        onBack = { caseLibraryViewModel.clearSelectedCase() }
                    )
                } else {
                    CaseLibraryPage()
                }

            }
        }
    }
} 