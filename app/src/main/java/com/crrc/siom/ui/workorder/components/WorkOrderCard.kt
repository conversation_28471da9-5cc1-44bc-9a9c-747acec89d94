package com.crrc.siom.ui.workorder.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.crrc.common.bean.response.BaseOrderRecord

/**
 * 工单卡片组件
 * 
 * 统一的工单卡片UI组件，用于显示各类工单信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkOrderCard(
    workOrder: BaseOrderRecord,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    onAccept: () -> Unit = {},
    onAssign: () -> Unit = {},
    onReturn: () -> Unit = {},
    onVoid: () -> Unit = {},
    onMaterial: () -> Unit = {},
    onArrive: () -> Unit = {},
    onProcess: () -> Unit = {},
    onConfirm: () -> Unit = {},
    additionalInfo: @Composable ColumnScope.() -> Unit = {}
) {
    Card(
        modifier = modifier,
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Surface(
                    shape = MaterialTheme.shapes.small,
                    color = getPriorityBackgroundColor(workOrder.priority),
                    contentColor = Color.White,
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text(
                        text = workOrder.priority,
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
                
                Text(
                    text = workOrder.title,
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Surface(
                    shape = MaterialTheme.shapes.small,
                    color = getStatusBackgroundColor(workOrder.status),
                    contentColor = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Text(
                        text = workOrder.status,
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }
            
            Divider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
                color = Color(0xFF1B5E20)
            )
            
            InfoRow(
                label = "工单编号",
                value = workOrder.number,
                labelStyle = MaterialTheme.typography.bodyMedium,
                valueStyle = MaterialTheme.typography.bodyMedium
            )
            InfoRow(
                label = "所属线路",
                value = workOrder.line,
                labelStyle = MaterialTheme.typography.bodyMedium,
                valueStyle = MaterialTheme.typography.bodyMedium
            )
            InfoRow(
                label = "所属车站",
                value = workOrder.station,
                labelStyle = MaterialTheme.typography.bodyMedium,
                valueStyle = MaterialTheme.typography.bodyMedium
            )
            InfoRow(
                label = "发起时间",
                value = workOrder.startDate,
                labelStyle = MaterialTheme.typography.bodyMedium,
                valueStyle = MaterialTheme.typography.bodyMedium
            )
            
            additionalInfo()
            
            // 操作按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp),
                horizontalArrangement = Arrangement.End
            ) {
                WorkOrderActions(
                    status = workOrder.status,
                    onAccept = onAccept,
                    onAssign = onAssign,
                    onReturn = onReturn,
                    onVoid = onVoid,
                    onMaterial = onMaterial,
                    onArrive = onArrive,
                    onProcess = onProcess,
                    onConfirm = onConfirm
                )
            }
        }
    }
}

/**
 * 获取优先级背景颜色
 */
@Composable
fun getPriorityBackgroundColor(priority: String): Color {
    return when (priority) {
        "高" -> Color(0xFFB71C1C) // 深红色
        "中" -> Color(0xFFE65100) // 深橙色
        "低" -> Color(0xFF2E7D32) // 深绿色
        else -> Color(0xFF1B5E20) // 深绿色
    }
}

/**
 * 获取状态背景颜色
 */
@Composable
fun getStatusBackgroundColor(status: String): Color {
    return when (status) {
        "待接单" -> Color(0xFF0277BD) // 深蓝色
        "待领料" -> Color(0xFFE65100) // 深橙色
        "待到场" -> Color(0xFF4527A0) // 深紫色
        "待处理" -> Color(0xFF2E7D32) // 深绿色
        "待完成" -> Color(0xFF424242) // 深灰色
        else -> Color(0xFF1B5E20) // 深绿色
    }
}

