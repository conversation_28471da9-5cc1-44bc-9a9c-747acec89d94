package com.crrc.siom.ui.notifications

import android.util.Log
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assignment
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.model.NotificationDelMessage
import com.crrc.siom.data.model.NotificationListMessage
import com.crrc.siom.data.model.NotificationMessage
import com.crrc.siom.data.model.NotificationReadMessage
import com.crrc.siom.data.model.WorkOrderData
import com.crrc.siom.manager.ChatMessageCenter
import com.crrc.siom.service.WebSocketService
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

data class NotificationItem(
    val notificationId: String,
    val icon: ImageVector,
    val title: String,
    val content: String,
    val createTime: String,
    var read: Boolean = false,
    val workOrderData: WorkOrderData? = null
)

class NotificationsViewModel : ViewModel() {

    private val _notifications = MutableStateFlow<List<NotificationItem>>(emptyList())
    val notifications: StateFlow<List<NotificationItem>> = _notifications
    var TAG: String = "NotificationsViewModel"
    private var chatBinder: WebSocketService.ChatBinder? = null

    init {
        viewModelScope.launch {
            ChatMessageCenter.newMessageFlow.collect { message ->
                Log.d(TAG, message.toString())
                when (message) {
                    is NotificationMessage -> {
                        val record = message.record
                        if (record.type == 1) {
                            val item = NotificationItem(
                                notificationId = record.id,
                                icon = Icons.Default.Assignment,
                                title = record.title,
                                content = record.content,
                                createTime = record.createTime,
                                workOrderData = record.data,
                                read = record.read
                            )
                            _notifications.value = listOf(item) + _notifications.value
                        } else {
                            //普通消息
                            val item = NotificationItem(
                                notificationId = record.id,
                                icon = Icons.Default.Notifications,
                                title = record.title,
                                content = record.content,
                                createTime = record.createTime,
                                read = record.read,
                                workOrderData = null
                            )
                            _notifications.value = listOf(item) + _notifications.value
                        }
                    }

                    else -> {
                        //忽略聊天消息
                    }
                }
            }
        }
    }

    fun setChatBinder(binder: WebSocketService.ChatBinder?) {
        chatBinder = binder
        if (chatBinder != null) {
            getNotificationHistoryList()
        }
    }

    fun getNotificationHistoryList() {
        _notifications.value = emptyList()
        chatBinder?.sendNotificationHistoryList(
            NotificationListMessage(
                type = "GET_UNREAD_NOTIFICATIONS",
                userId = SessionManager().getUserId().toString()
            )
        )
    }

    fun readNotification(notification: NotificationItem) {
        _notifications.value = _notifications.value.map {
            if (it.notificationId == notification.notificationId) it.copy(read = true) else it
        }
        chatBinder?.sendNotificationRead(
            NotificationReadMessage(
                type = "NOTICE_READ",
                notificationId = notification.notificationId,
                userId = SessionManager().getUserId().toString()
            )
        )
    }
    fun delNotification(notification: NotificationItem) {
        chatBinder?.delNotification(
            NotificationDelMessage(
                type = "DELETE_NOTIFICATION",
                notificationId = notification.notificationId,
                userId = SessionManager().getUserId().toString()
            )
        )
    }

}