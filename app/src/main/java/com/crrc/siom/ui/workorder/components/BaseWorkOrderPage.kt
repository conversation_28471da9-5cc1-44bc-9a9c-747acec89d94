package com.crrc.siom.ui.workorder.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.crrc.siom.ui.workorder.viewmodel.list.BaseWorkOrderViewModelImpl

/**
 * 工单页面基础组件，处理上拉加载和下拉刷新
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> BaseWorkOrderPage(
    items: List<T>,
    isLoading: Boolean,
    isLoadingMore: Boolean,
    hasMoreData: Boolean,
    error: String?,
    onRefresh: () -> Unit,
    onLoadMore: () -> Unit,
    filterBar: @Composable () -> Unit,
    itemContent: @Composable (T) -> Unit,
    // 工单操作相关参数
    isRollbackLoading: Boolean = false,
    isCancelLoading: Boolean = false,
    isAcceptLoading: Boolean = false,
    isDealLoading: Boolean = false,
    isConfirmLoading: Boolean = false,
    isArriveLoading: Boolean = false,
    rollbackResult: Pair<Boolean, String?>? = null,
    cancelResult: Pair<Boolean, String?>? = null,
    acceptResult: Pair<Boolean, String?>? = null,
    dealResult: Pair<Boolean, String?>? = null,
    confirmResult: Pair<Boolean, String?>? = null,
    arriveResult: Pair<Boolean, String?>? = null,
    onRollback: (String) -> Unit = {},
    onCancel: (String) -> Unit = {},
    onAccept: (String) -> Unit = {},
    onDeal: (String) -> Unit = {},
    onConfirm: (String) -> Unit = {},
    onArrive: (String) -> Unit = {},
    onResetRollbackResult: () -> Unit = {},
    onResetCancelResult: () -> Unit = {},
    onResetAcceptResult: () -> Unit = {},
    onResetDealResult: () -> Unit = {},
    onResetConfirmResult: () -> Unit = {},
    onResetArriveResult: () -> Unit = {}
) {
    val listState = rememberLazyListState()
    
    // 回退对话框状态
    val rollbackDialogState = rememberWorkOrderActionDialogState()
    
    // 作废对话框状态
    val cancelDialogState = rememberWorkOrderActionDialogState()
    
    // 接单对话框状态
    val acceptDialogState = rememberWorkOrderActionDialogState()
    
    // 处理对话框状态
    val dealDialogState = rememberWorkOrderActionDialogState()
    
    // 确认对话框状态
    val confirmDialogState = rememberWorkOrderActionDialogState()
    
    // 到场对话框状态
    val arriveDialogState = rememberWorkOrderActionDialogState()
    
    // 处理回退结果
    HandleWorkOrderActionResult(
        result = rollbackResult,
        action = WorkOrderAction.Rollback,
        onSuccess = onRefresh,
        onResetResult = onResetRollbackResult
    )

    // 处理作废结果
    HandleWorkOrderActionResult(
        result = cancelResult,
        action = WorkOrderAction.Cancel,
        onSuccess = onRefresh,
        onResetResult = onResetCancelResult
    )

    // 处理接单结果
    HandleWorkOrderActionResult(
        result = acceptResult,
        action = WorkOrderAction.Accept,
        onSuccess = onRefresh,
        onResetResult = onResetAcceptResult
    )

    // 处理处理结果
    HandleWorkOrderActionResult(
        result = dealResult,
        action = WorkOrderAction.Deal,
        onSuccess = onRefresh,
        onResetResult = onResetDealResult
    )

    // 处理确认结果
    HandleWorkOrderActionResult(
        result = confirmResult,
        action = WorkOrderAction.Confirm,
        onSuccess = onRefresh,
        onResetResult = onResetConfirmResult
    )

    // 处理到场结果
    HandleWorkOrderActionResult(
        result = arriveResult,
        action = WorkOrderAction.Arrive,
        onSuccess = onRefresh,
        onResetResult = onResetArriveResult
    )

    // 回退对话框
    WorkOrderActionDialog(
        state = rollbackDialogState,
        action = WorkOrderAction.Rollback,
        isLoading = isRollbackLoading,
        onAction = onRollback
    )
    
    // 作废对话框
    WorkOrderActionDialog(
        state = cancelDialogState,
        action = WorkOrderAction.Cancel,
        isLoading = isCancelLoading,
        onAction = onCancel
    )
    
    // 接单对话框
    WorkOrderActionDialog(
        state = acceptDialogState,
        action = WorkOrderAction.Accept,
        isLoading = isAcceptLoading,
        onAction = onAccept
    )
    
    // 处理对话框
    WorkOrderActionDialog(
        state = dealDialogState,
        action = WorkOrderAction.Deal,
        isLoading = isDealLoading,
        onAction = onDeal
    )
    
    // 确认对话框
    WorkOrderActionDialog(
        state = confirmDialogState,
        action = WorkOrderAction.Confirm,
        isLoading = isConfirmLoading,
        onAction = onConfirm
    )
    
    // 到场对话框
    WorkOrderActionDialog(
        state = arriveDialogState,
        action = WorkOrderAction.Arrive,
        isLoading = isArriveLoading,
        onAction = onArrive
    )

//    监听上一页返回后的ON_RESUME
//    val lifecycleOwner = LocalLifecycleOwner.current
//    DisposableEffect(Unit) {
//        val observer = LifecycleEventObserver { _, event ->
//            if (event == Lifecycle.Event.ON_RESUME) {
//                onRefresh()
//            }
//        }
//        lifecycleOwner.lifecycle.addObserver(observer)
//        onDispose {
//            lifecycleOwner.lifecycle.removeObserver(observer)
//        }
//    }

    // 监听滚动到底部
    val shouldLoadMore = remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            val totalItemsNumber = layoutInfo.totalItemsCount
            val lastVisibleItemIndex = (layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0) + 1

            totalItemsNumber >= 5 &&
            lastVisibleItemIndex > 0 && 
            lastVisibleItemIndex >= totalItemsNumber - 2 && 
            hasMoreData && 
            !isLoadingMore && !isLoading
        }
    }
    
    LaunchedEffect(shouldLoadMore.value) {
        if (shouldLoadMore.value) {
            onLoadMore()
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        // 筛选条件栏
        filterBar()
        
        PullToRefreshBox(
            isRefreshing = isLoading && items.isNotEmpty(),
            onRefresh = onRefresh,
            modifier = Modifier.fillMaxSize()
        ) {
            if (isLoading && items.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } 
            else if (error != null && items.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = error,
                            color = MaterialTheme.colorScheme.error,
                            modifier = Modifier.padding(16.dp)
                        )
                        Button(onClick = onRefresh) {
                            Text("重试")
                        }
                    }
                }
            } 
            else {
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(items.size) { index ->
                        val item = items[index]
                        Box(modifier = Modifier.fillMaxWidth()) {
                            CompositionLocalProvider(
                                LocalRollbackDialogState provides rollbackDialogState,
                                LocalCancelDialogState provides cancelDialogState,
                                LocalAcceptDialogState provides acceptDialogState,
                                LocalDealDialogState provides dealDialogState,
                                LocalConfirmDialogState provides confirmDialogState,
                                LocalArriveDialogState provides arriveDialogState
                            ) {
                                itemContent(item)
                            }
                        }
                    }
                    
                    // 底部加载更多
                    item {
                        if (items.isNotEmpty()) {
                            if (isLoadingMore) {
                                // 显示加载更多的进度条
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(24.dp),
                                        strokeWidth = 2.dp
                                    )
                                }
                            } else if (!hasMoreData) {
                                // 显示没有更多数据的提示
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        "没有更多数据了",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 提供对话框状态的CompositionLocal
val LocalRollbackDialogState = compositionLocalOf<WorkOrderActionDialogState> { 
    error("No RollbackDialogState provided") 
}

val LocalCancelDialogState = compositionLocalOf<WorkOrderActionDialogState> { 
    error("No CancelDialogState provided") 
}

val LocalAcceptDialogState = compositionLocalOf<WorkOrderActionDialogState> { 
    error("No AcceptDialogState provided") 
}

val LocalDealDialogState = compositionLocalOf<WorkOrderActionDialogState> { 
    error("No DealDialogState provided") 
}

val LocalConfirmDialogState = compositionLocalOf<WorkOrderActionDialogState> { 
    error("No ConfirmDialogState provided") 
}

val LocalArriveDialogState = compositionLocalOf<WorkOrderActionDialogState> { 
    error("No ArriveDialogState provided") 
}

/**
 * 工单页面基础组件，处理上拉加载和下拉刷新
 * 重载版本，直接接收BaseWorkOrderViewModelImpl减少传参
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> BaseWorkOrderPage(
    viewModel: BaseWorkOrderViewModelImpl<T>,
    filterBar: @Composable () -> Unit,
    itemContent: @Composable (T) -> Unit
) {
    // 从ViewModel收集状态
    val items by viewModel.workOrders.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
    val hasMoreData by viewModel.hasMoreData.collectAsState()
    val error by viewModel.error.collectAsState()
    
    // 工单操作相关状态
    val isRollbackLoading by viewModel.isRollbackLoading.collectAsState()
    val isCancelLoading by viewModel.isCancelLoading.collectAsState()
    val isAcceptLoading by viewModel.isAcceptLoading.collectAsState()
    val isDealLoading by viewModel.isDealLoading.collectAsState()
    val isConfirmLoading by viewModel.isConfirmLoading.collectAsState()
    val isArriveLoading by viewModel.isArriveLoading.collectAsState()
    
    val rollbackResult by viewModel.rollbackResult.collectAsState()
    val cancelResult by viewModel.cancelResult.collectAsState()
    val acceptResult by viewModel.acceptResult.collectAsState()
    val dealResult by viewModel.dealResult.collectAsState()
    val confirmResult by viewModel.confirmResult.collectAsState()
    val arriveResult by viewModel.arriveResult.collectAsState()
    
    // 使用原有的BaseWorkOrderPage实现
    BaseWorkOrderPage(
        items = items,
        isLoading = isLoading,
        isLoadingMore = isLoadingMore,
        hasMoreData = hasMoreData,
        error = error,
        onRefresh = { viewModel.refresh() },
        onLoadMore = { viewModel.loadMore() },
        filterBar = filterBar,
        itemContent = itemContent,
        isRollbackLoading = isRollbackLoading,
        isCancelLoading = isCancelLoading,
        isAcceptLoading = isAcceptLoading,
        isDealLoading = isDealLoading,
        isConfirmLoading = isConfirmLoading,
        isArriveLoading = isArriveLoading,
        rollbackResult = rollbackResult,
        cancelResult = cancelResult,
        acceptResult = acceptResult,
        dealResult = dealResult,
        confirmResult = confirmResult,
        arriveResult = arriveResult,
        onRollback = { viewModel.rollbackWorkOrder(it) },
        onCancel = { viewModel.cancelWorkOrder(it) },
        onAccept = { viewModel.acceptWorkOrder(it) },
        onDeal = { viewModel.dealWorkOrder(it) },
        onConfirm = { viewModel.confirmWorkOrder(it) },
        onArrive = { viewModel.arriveWorkOrder(it) },
        onResetRollbackResult = { viewModel.resetRollbackResult() },
        onResetCancelResult = { viewModel.resetCancelResult() },
        onResetAcceptResult = { viewModel.resetAcceptResult() },
        onResetDealResult = { viewModel.resetDealResult() },
        onResetConfirmResult = { viewModel.resetConfirmResult() },
        onResetArriveResult = { viewModel.resetArriveResult() }
    )
}
