package com.crrc.siom.ui.workorder.components

import androidx.compose.material3.TextButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable

@Composable
fun WorkOrderActions(
    status: String,
    onAccept: () -> Unit = {},      // 接单
    onAssign: () -> Unit = {},      // 指派
    onReturn: () -> Unit = {},      // 回退
    onVoid: () -> Unit = {},        // 作废
    onMaterial: () -> Unit = {},    // 领料
    onArrive: () -> Unit = {},      // 到场
    onProcess: () -> Unit = {},     // 处理
    onConfirm: () -> Unit = {},    // 确认
) {
    //待派单->待接单->待领料->待到场->待处理->待完成（故障处置内完成）->待确认
    when (status) {
        "待派发" -> {
            TextButton(onClick = onAssign) {
                Text("指派")
            }
            TextButton(onClick = onVoid) {
                Text("作废")
            }
        }
        "待接单" -> {
            TextButton(onClick = onReturn) {
                Text("回退")
            }
            TextButton(onClick = onAccept) {
                Text("接单")
            }
        }
        "待领料" -> {
            TextButton(onClick = onMaterial) {
                Text("领料")
            }
        }
        "待到场" -> {
            TextButton(onClick = onArrive) {
                Text("到场")
            }
        }
        "待处理" -> {
            TextButton(onClick = onProcess) {
                Text("处理")
            }
        }

        "待完成" -> {
            TextButton(onClick = onProcess) {
                Text("处理")
            }
        }
        "待确认" -> {
            TextButton(onClick = onConfirm) {
                Text("确认")
            }
        }
    }
} 