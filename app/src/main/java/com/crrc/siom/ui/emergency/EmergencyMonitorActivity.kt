package com.crrc.siom.ui.emergency

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.crrc.siom.ui.theme.SiomTheme
import com.crrc.siom.ui.workorder.WorkOrderActivity
import com.crrc.siom.ui.workorder.WorkOrderDetailActivity

class EmergencyMonitorActivity : ComponentActivity() {
    
    companion object {
        fun start(context: Context) {
            val intent = Intent(context, EmergencyMonitorActivity::class.java)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SiomTheme {
                EmergencyMonitorScreen(
                    onBackClick = { finish() },
                    onEmergencyDetailClick = { emergencyId,name ->
                        EmergencyDetailActivity.start(this, emergencyId,name)
                    },
                    onRelatedWorkOrderClick = { relateOrderId ->

                        Toast.makeText(this, "查看关联工单: $relateOrderId", Toast.LENGTH_SHORT).show()
                        WorkOrderDetailActivity.start(this, relateOrderId)
                    }
                )
            }
        }
    }
} 