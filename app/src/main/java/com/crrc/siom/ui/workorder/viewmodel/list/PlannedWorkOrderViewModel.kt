package com.crrc.siom.ui.workorder.viewmodel.list

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.MembersResponse
import com.crrc.common.bean.response.OrderFilterParamResponse.FilterParam
import com.crrc.common.bean.response.PlanOrderListResponse
import com.crrc.siom.data.repository.WorkOrderRepository
import com.crrc.siom.data.repository.WorkOrderRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
工单的几个viewmodel可以抽象一下
 */
class PlannedWorkOrderViewModel : BaseWorkOrderViewModelImpl<PlanOrderListResponse.PlanOrderRecord>() {
    private val workOrderRepository: WorkOrderRepository = WorkOrderRepositoryImpl()


    init {
        viewModelScope.launch {
            refresh()
        }
    }


    override fun loadData(isRefresh: <PERSON>olean) {
        workOrderRepository.getPlannedWorkOrders(
            _currentPage.value,
            pageSize,
            _filterParams.value
        ) { workOrders, error ->
            if (isRefresh) {
                _isLoading.value = false
            } else {
                _isLoadingMore.value = false
                isProcessingLoadMore = false
            }

            if (error != null) {
                _error.value = error
            } else if (workOrders != null) {
                if (isRefresh) {
                    _workOrders.value = workOrders
                } else {
                    _workOrders.value = (_workOrders.value + workOrders).distinctBy { it.id }
                }

                _hasMoreData.value = workOrders.size >= pageSize

                if (workOrders.isNotEmpty()) {
                    _currentPage.value = _currentPage.value + 1
                }
            }
        }
    }

    override fun getWorkOrderRepository(): WorkOrderRepository {
        return workOrderRepository
    }


}