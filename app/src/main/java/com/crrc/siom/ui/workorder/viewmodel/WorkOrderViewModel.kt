package com.crrc.siom.ui.workorder.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.bean.response.SearchResponse
import com.crrc.common.bean.response.WorkOrderCountResponse
import com.crrc.siom.data.repository.WorkOrderRepository
import com.crrc.siom.data.repository.WorkOrderRepositoryImpl
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

class WorkOrderViewModel : ViewModel() {
    private val workOrderRepository: WorkOrderRepository = WorkOrderRepositoryImpl()

    // 工单数量统计
    private val _workOrderCount = MutableStateFlow<WorkOrderCountResponse?>(null)
    val workOrderCount: StateFlow<WorkOrderCountResponse?> = _workOrderCount.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()


    private val _searchResults = MutableStateFlow<SearchResponse?>(null)
    val searchResults: StateFlow<SearchResponse?> = _searchResults
    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadWorkOrderCount()
    }

    fun loadWorkOrderCount() {
        _isLoading.value = true
        _error.value = null

        workOrderRepository.getWorkOrderCount { countData, error ->
            _isLoading.value = false

            if (error != null) {
                _error.value = error
            } else {
                _workOrderCount.value = countData
            }
        }
    }

    fun searchAll(query: String) {
//        repository.search(query) { results, error ->
//            _searchResults.value = results
//        }
        viewModelScope.launch {
            // 模拟网络延迟
            delay(800)

            // 构造 mock 数据
            val mockResult = SearchResponse().apply {
                userList = listOf(
                    SearchResponse.User().apply {
                        userId = "u1"
                        name = "张三"
                    },
                    SearchResponse.User().apply {
                        userId = "u2"
                        name = "李四"
                    }
                )
                deviceList = listOf(
                    SearchResponse.Device().apply {
                        deviceId = "d1"
                        deviceName = "设备A"
                    }
                )
                orderList = listOf(
                    SearchResponse.Order().apply {
                        orderId = "o1"
                        orderTitle = "工单A"
                    }
                )
                fileList = listOf(
                    SearchResponse.File().apply {
                        fileId = "f1"
                        fileName = "文件A"
                    }
                )
            }

            // 返回 mock 数据
            _searchResults.value = mockResult
        }
    }

}