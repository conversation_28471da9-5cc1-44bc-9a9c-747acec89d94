package com.crrc.siom.ui.theme

import androidx.compose.ui.graphics.Color

// 主色调 - 亮绿色
//val Primary = Color(0xFF2E2E2E)
val Primary = Color(0xFF1B5E20)
// 深绿色 - 用于选中状态
val PrimaryDark = Color(0xFF1B5E20)

// 背景色 - 浅灰色和白色
val Background = Color(0xFFF5F5F6)  // 浅灰色背景
val Surface = Color(0xFFFFFFFF)     // 白色表面（卡片和底部导航栏）

// 文本颜色
val OnPrimary = Color(0xFFFFFFFF)   // 主色上的文本（白色）
val OnBackground = Color(0xFF2E2E2E) // 背景上的主要文本（深灰/黑色）
val OnSurface = Color(0xFF2E2E2E)    // 表面上的主要文本（深灰/黑色）
val OnSurfaceVariant = Color(0xFF898F96) // 次要文本（中灰）

// 容器颜色
val PrimaryContainer = Color(0xFFFFFFFF)  // 白色容器
val OnPrimaryContainer = Color(0xFF2E2E2E)  // 黑色文本



