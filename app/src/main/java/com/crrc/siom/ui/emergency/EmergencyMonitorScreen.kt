package com.crrc.siom.ui.emergency

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.EmergencyEventListResponse.EmergencyEvent

import com.crrc.siom.ui.emergency.viewmodel.EmergencyMonitorViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmergencyMonitorScreen(
    onBackClick: () -> Unit,
    onEmergencyDetailClick: (String, String) -> Unit,
    onRelatedWorkOrderClick: (String) -> Unit,
    viewModel: EmergencyMonitorViewModel = viewModel()
) {
    val emergencyList by viewModel.emergencyList.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.loadEmergencyList()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("应急监测") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (error != null) {
                Text(
                    text = "加载失败: ${error}",
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(16.dp)
                )
            }  else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(emergencyList) { emergency ->
                        EmergencyMonitorItem(
                            emergency = emergency,
                            onDetailClick = { onEmergencyDetailClick(emergency.uuid, emergency.name) },
                            onWorkOrderClick = { onRelatedWorkOrderClick(emergency.relateOrderId) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun EmergencyMonitorItem(
    emergency: EmergencyEvent,
    onDetailClick: () -> Unit,
    onWorkOrderClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = emergency.alarmDate,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = emergency.name,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                OutlinedButton(
                    onClick = onDetailClick,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("应急详情")
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                OutlinedButton(
                    onClick = onWorkOrderClick,
                    modifier = Modifier.weight(1f),
                ) {
                    Text("关联工单")
                }
            }
        }
    }
} 