package com.crrc.siom;


import android.content.Context;

import com.amap.api.location.AMapLocationClient;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.services.core.ServiceSettings;
import com.tencent.mmkv.MMKV;

import com.crrc.common.BaseApplication;
import com.crrc.common.Constant;
import com.crrc.common.utils.MMkvUtil;
import com.crrc.common.utils.PrefsUtil;
import com.crrc.network.base.NetworkApi;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import xcrash.XCrash;



/**
 * <AUTHOR>
 */
public class AppApplication extends BaseApplication {
    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        //日志目录 0/Android/data/cn.crrc.imos/files/xcrash
        XCrash.init(this,new XCrash.InitParameters().setLogDir(getExternalFilesDir("xcrash").toString()));
    }

    @Override
    public void onCreate() {
        super.onCreate();
        NetworkApi.init(new NetworkRequestInfo(this));
        //mmKv初始化
        MMKV.initialize(this);
        MMkvUtil.getInstance();

        //崩溃日志收集
//        CrashHandler.init(this);
//
//        ANR收集
//        ANRWatchDog anrWatchDog = new ANRWatchDog();
//        ANRWatchDog.ANRListener defaultAnrListener = error -> {
//            try {
//                throw error;
//            } catch (ANRError anrError) {
//                anrError.printStackTrace();
//            }
//        };
//        anrWatchDog.setANRListener(defaultAnrListener).start();



        //ip持久化
        Constant.BaseUrl = PrefsUtil.getInstance(AppApplication.sApplication).getBaseUrl();
        // 定位隐私政策同意
        AMapLocationClient.updatePrivacyShow(this,true,true);
        AMapLocationClient.updatePrivacyAgree(this,true);
        // 地图隐私政策同意
        MapsInitializer.updatePrivacyShow(this,true,true);
        MapsInitializer.updatePrivacyAgree(this,true);
        // 搜索隐私政策同意
        ServiceSettings.updatePrivacyShow(this,true,true);
        ServiceSettings.updatePrivacyAgree(this,true);
        startLogCapture();

    }
    private void startLogCapture() {
        new Thread(() -> {
            Process process = null;
            BufferedReader reader = null;
            FileWriter writer = null;
            try {
                int pid = android.os.Process.myPid();
                // 启动 logcat 命令
                process = Runtime.getRuntime().exec("logcat --pid=" + pid + " -v time");

                reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                File logFile = new File(getExternalFilesDir(null), "log_" + getTodayString() + ".txt");

                writer = new FileWriter(logFile, true); // 追加模式

                String line;
                while ((line = reader.readLine()) != null) {
                    writer.write(line + "\n");
                    writer.flush(); // 实时刷新
                }

            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (writer != null) writer.close();
                    if (reader != null) reader.close();
                    if (process != null) process.destroy();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    private String getTodayString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd", Locale.getDefault());
        return sdf.format(new Date());
    }

}
