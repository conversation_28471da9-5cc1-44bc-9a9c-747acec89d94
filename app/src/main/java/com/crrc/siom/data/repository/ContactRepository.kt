package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import androidx.annotation.CheckResult
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.AddressBookListResponse
import com.crrc.common.bean.response.ChatInfo
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic
import com.crrc.siom.data.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

interface ContactRepository {
    suspend fun searchContacts(query: String): List<Contact>
    fun getAddressBookList(userId: String, callback: (AddressBookListResponse?, String?) -> Unit)
    fun getConversation(type: Int, initiatorUserId: String, targetId: String, callback: (String?, String?) -> Unit)
    fun getChatList(userId: String, callback: (List<ChatInfo>?, String?) -> Unit)
}

@SuppressLint("CheckResult")
class ContactRepositoryImpl : ContactRepository {
    override suspend fun searchContacts(query: String): List<Contact> =
        withContext(Dispatchers.IO) {
            // TODO: 实现搜索功能
            emptyList()
        }

    override fun getAddressBookList(
        userId: String,
        callback: (AddressBookListResponse?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getAddressBookList(userId)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<AddressBookListResponse>>() {
                    override fun onSuccess(response: BaseResponse<AddressBookListResponse>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun getConversation(
        type: Int,
        initiatorUserId: String,
        targetId: String,
        callback: (String?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getConversation(type, initiatorUserId, targetId)
            .compose(
                NetworkApi.getInstance()
                    .applySchedulers(object : BaseObserverForBasic<BaseResponse<String>>() {
                        override fun onSuccess(response: BaseResponse<String>) {
                            callback(response.data, null)
                        }

                        override fun onFail(e: Throwable) {
                            callback(null, e.message)
                        }
                    })
            )
    }

    @Deprecated("由websocket接口 PARTICIPANT_LIST_UPDATE 替代")
    override fun getChatList(
        userId: String,
        callback: (List<ChatInfo>?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getChatList(userId)
            .compose(
                NetworkApi.getInstance()
                    .applySchedulers(object : BaseObserverForBasic<BaseResponse<List<ChatInfo>>>() {
                        override fun onSuccess(response: BaseResponse<List<ChatInfo>>) {
                            callback(response.data, null)
                        }
                        override fun onFail(e: Throwable) {
                            callback(null, e.message)
                        }
                    })
            )
    }

} 