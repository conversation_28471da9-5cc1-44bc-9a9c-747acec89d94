package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.CaseListResponse
import com.crrc.common.bean.response.CaseDetailResponse
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic

interface CaseRepository {
    fun getCaseList(pageNum: Int, pageSize: Int, callback: (List<CaseListResponse.CaseRecord>?, String?) -> Unit)
    fun getCaseFaultList(workOrderId:String,pageNum: Int, pageSize: Int, callback: (List<CaseListResponse.CaseRecord>?, String?) -> Unit)

    fun searchCases(keyword: String, pageNum: Int, pageSize: Int, callback: (List<CaseListResponse.CaseRecord>?, String?) -> Unit)
    
    fun getCaseDetail(caseId: String, callback: (CaseDetailResponse?, String?) -> Unit)
}

@SuppressLint("CheckResult")
class CaseRepositoryImpl : CaseRepository {
    override fun getCaseList(pageNum: Int, pageSize: Int, callback: (List<CaseListResponse.CaseRecord>?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .getCaseList(pageNum, pageSize)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<CaseListResponse>>() {
                override fun onSuccess(response: BaseResponse<CaseListResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data.records, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun getCaseFaultList(
        workOrderId: String,
        pageNum: Int,
        pageSize: Int,
        callback: (List<CaseListResponse.CaseRecord>?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getCaseFaultList(workOrderId,pageNum, pageSize)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<CaseListResponse>>() {
                override fun onSuccess(response: BaseResponse<CaseListResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data.records, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun searchCases(keyword: String, pageNum: Int, pageSize: Int, callback: (List<CaseListResponse.CaseRecord>?, String?) -> Unit) {
        // 只实现列表接口，这里先保留搜索方法但不实现实际请求
        // 如果需要实现，可以按照下面的模板添加对应的接口调用
        /*
        NetworkApi.getService(ApiInterface::class.java)
            .searchCases(keyword, pageNum, pageSize)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<CaseListResponse>>() {
                override fun onSuccess(response: BaseResponse<CaseListResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data.records, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
        */
        
        // 临时方案：直接返回空数据
        callback(emptyList(), "搜索功能未实现")
    }

    override fun getCaseDetail(caseId: String, callback: (CaseDetailResponse?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .getCaseDetail(caseId)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<CaseDetailResponse>>() {
                override fun onSuccess(response: BaseResponse<CaseDetailResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }
} 