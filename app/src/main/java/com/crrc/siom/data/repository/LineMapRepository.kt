package com.crrc.siom.data.repository;

import android.annotation.SuppressLint;
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.LineDevice
import com.crrc.common.bean.response.LineInfoResponse
import com.crrc.common.bean.response.LineTool
import com.crrc.common.bean.response.StationAlarmResponse
import com.crrc.common.utils.GetRequestBody
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic

interface LineMapRepository {
    fun getLineInfo(callback: (List<LineInfoResponse>?, String?) -> Unit)
    fun getStationAlarmInfo(
        stationId: String,
        callback: (List<StationAlarmResponse>?, String?) -> Unit
    )

    fun getLineDeviceInfo(storeId: String, callback: (List<LineDevice>?, String?) -> Unit)
    fun getLineToolsInfo(storeId: String, callback: (List<LineTool>?, String?) -> Unit)

}

@SuppressLint("CheckResult")
class LineMapRepositoryImpl : LineMapRepository {
    override fun getLineInfo(callback: (List<LineInfoResponse>?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .getLineInfo()
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<List<LineInfoResponse>>>() {
                    override fun onSuccess(response: BaseResponse<List<LineInfoResponse>>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun getStationAlarmInfo(
        stationId: String,
        callback: (List<StationAlarmResponse>?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getStationAlarmInfo(stationId)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<List<StationAlarmResponse>>>() {
                    override fun onSuccess(response: BaseResponse<List<StationAlarmResponse>>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun getLineDeviceInfo(
        storeId: String,
        callback: (List<LineDevice>?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getLineDeviceInfo(storeId)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<List<LineDevice>>>() {
                    override fun onSuccess(response: BaseResponse<List<LineDevice>>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun getLineToolsInfo(
        storeId: String,
        callback: (List<LineTool>?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getLineToolsInfo(storeId)
            .compose(
                NetworkApi.getInstance()
                    .applySchedulers(object : BaseObserverForBasic<BaseResponse<List<LineTool>>>() {
                        override fun onSuccess(response: BaseResponse<List<LineTool>>) {
                            callback(response.data, null)
                        }

                        override fun onFail(e: Throwable) {
                            callback(null, e.message)
                        }
                    })
            )
    }

}




