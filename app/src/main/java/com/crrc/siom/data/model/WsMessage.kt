package com.crrc.siom.data.model

sealed interface WsMessage {
    val type: String
}

//聊天  单聊
data class ChatMessage(
    override val type: String = "SINGLE_SENDING",
    val conversationId : String,
    val messageList: List<Message>
): WsMessage
//聊天  群聊
data class GroupChatMessage(
    override val type: String = "GROUP_SENDING",
    val conversationId : String,
    val messageList: List<Message>
): WsMessage

data class Message(
    val id: String,
    val conversationId : String,
    val senderId: String,
    val senderName: String,
    val text: String,
    val type: String,
    val attachments:String,
    val time: String,
    val isRecalled: String,
    val replyTo: String,
)

//心跳
data class Heartbeat(
    override val type: String = "HEARTBEAT",
    val userId: String
): WsMessage
//消息已读
data class ReceiveMessage(
    override val type: String = "RECEIVE",
    val conversationId: String,
    val userId: String,
    val clientReportedLastReadMsgId: String,
): WsMessage
//消息撤回
data class RecallMessage(
    override val type: String,
    val conversationId: String,
    val messageId: String,
    val timestamp: String,
): WsMessage

//历史消息请求
data class HistoryMessage(
    override val type: String = "HISTORY_MESSAGE",
    val userId: String,
    val conversationId: String,
    val limit: Int,
    val lastMessageId: String?
): WsMessage
data class HistoryMessageResponse(
    override val type: String = "HISTORY_MESSAGE_RESPONSE",
    val userId: String,
    val conversationId: String,
    val requestTime: String,
    val responseTime: String,
    val hasMore: Boolean,
    val messageList: List<Message>
): WsMessage

//列表消息请求
data class ParticipantListMessage(
    override val type: String = "GET_PARTICIPANT_LIST",
    val userId: String,
): WsMessage
//聊天参与列表更新推送
data class ParticipantListMessageResponse(
    override val type: String="PARTICIPANT_LIST_UPDATE",
    val userId: String,
    val timestamp: String,
    val chatInfoList: List<ChatInfo>
): WsMessage

data class ChatInfo(
    val conversationId: String,
    val userId: String,
    val userName: String,
    val role: String,
    val joinTime: String,
    val lastReadMsgId: String,
    val unreadCount: Int = 0,
    val type: Int,
    val lastMsgSummary: String = ""
)
//强制下线
data class LogoutMessage(
    override val type: String,
    val msg: String
): WsMessage


/**
 * 通知
 */

//列表通知请求
data class NotificationListMessage(
    override val type: String = "GET_UNREAD_NOTIFICATIONS",
    val userId: String,
): WsMessage

data class NotificationReadMessage(
    override val type: String = "NOTICE_READ",
    val userId: String,
    val notificationId: String
): WsMessage

data class NotificationDelMessage(
    override val type: String = "DELETE_NOTIFICATION",
    val userId: String,
    val notificationId: String
): WsMessage

data class NotificationMessage(
    override val type: String,
    val record: NoticeRecord
): WsMessage

data class NoticeRecord(
    val id: String,
    val title: String,
    val userId: String,
    val content: String,
    val data: WorkOrderData,
    val type: Int,
    val createTime: String,
    val read: Boolean,
    val updateTime: String
)

data class WorkOrderData(
    val orderId: String,
)






