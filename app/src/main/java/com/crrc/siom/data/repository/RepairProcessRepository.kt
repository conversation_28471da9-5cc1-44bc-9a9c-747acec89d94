package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.ProcedureResponse
import com.crrc.common.utils.GetRequestBody
import com.crrc.common.utils.GsonUtil
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic


interface RepairProcessRepository {
    fun procedure(id: String,callback: (ProcedureResponse?, String?) -> Unit)
    fun procedureConfirm(id:String,procedureResponse: ProcedureResponse,callback: (Boolean?, String?) -> Unit);
    fun procedurePass(id: String, procedureId: String, callback: (Boolean?, String?) -> Unit);
    fun procedureFail(id: String, procedureId: String, callback: (Boolean?, String?) -> Unit);
}

@SuppressLint("CheckResult")
class RepairProcessRepositoryImpl : RepairProcessRepository {
    override fun procedure(id: String,callback: (ProcedureResponse?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .procedure(id)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<ProcedureResponse>>() {
                override fun onSuccess(response: BaseResponse<ProcedureResponse>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun procedureConfirm(id: String,procedureResponse: ProcedureResponse,callback: (Boolean?, String?) -> Unit) {

//        val procedures = procedureResponse.procedures
        val proceduresStr = GsonUtil.GsonString(procedureResponse)
        NetworkApi.getService(ApiInterface::class.java)
            .procedureConfirm(id,proceduresStr)
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, e.message)
                }
            }))
    }

    override fun procedurePass(
        id: String,
        procedureId: String,
        callback: (Boolean?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .procedurePass(id,procedureId)
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)

                }

                override fun onFail(e: Throwable) {
                    callback(false, e.message)
                }
            }))
    }

    override fun procedureFail(
        id: String,
        procedureId: String,
        callback: (Boolean?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .procedureFail(id,procedureId)
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }
                override fun onFail(e: Throwable) {
                    callback(false, e.message)
                }
            }))
    }


}