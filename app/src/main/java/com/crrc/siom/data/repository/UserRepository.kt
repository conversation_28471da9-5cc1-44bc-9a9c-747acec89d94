package com.crrc.siom.data.repository;

import android.annotation.SuppressLint;
import android.util.Log
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.GroupMemberInfoResponse
import com.crrc.common.bean.response.LoginBean
import com.crrc.common.bean.response.UserInfoResponse
import com.crrc.common.bean.response.WorkorderStatics
import com.crrc.common.utils.PrefsUtil
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic
import com.crrc.siom.AppApplication
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import okhttp3.Call
import okhttp3.Callback
import okhttp3.FormBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import java.io.File
import java.io.IOException
import kotlin.coroutines.resumeWithException

interface UserRepository {
    fun login(username: String, password: String, callback: (LoginBean?, String?) -> Unit)
    fun logout(callback: (Boolean?, String?) -> Unit)
    fun getUserById(userId: String, callback: (UserInfoResponse?, String?) -> Unit)
    fun loadUserAvatar(filePath: String, userId: String, callback: (Boolean?, String?) -> Unit)
    fun getUserAvatar(userId: String, callback: (ByteArray?, String?) -> Unit)
    suspend fun getUserAvatarSuspend(userId: String): ByteArray?
    fun getWorkOrderStatistics(
        time: String,
        timeType: Int,
        userId: String,
        callback: (WorkorderStatics?, String?) -> Unit
    )

    fun loadGroupMemberInfo(
        groupId: String,
        callback: (List<GroupMemberInfoResponse>?, String?) -> Unit
    )
}

@SuppressLint("CheckResult")
class UserRepositoryImpl : UserRepository {
    override fun login(
        username: String,
        password: String,
        callback: (LoginBean?, String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
//        map["username"] = username
//        map["password"] = password
        NetworkApi.getService(ApiInterface::class.java)
//            .login(GetRequestBody.requestBody(map))
            .login(username, password)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<LoginBean>>() {
                    override fun onSuccess(response: BaseResponse<LoginBean>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun logout(
        callback: (Boolean?, String?) -> Unit
    ) {

        NetworkApi.getService(ApiInterface::class.java)
            .logout()
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<Any>>() {
                    override fun onSuccess(response: BaseResponse<Any>) {
                        callback(true, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun getUserById(
        userId: String,
        callback: (UserInfoResponse?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getUserById(userId)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<UserInfoResponse>>() {
                    override fun onSuccess(response: BaseResponse<UserInfoResponse>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun loadUserAvatar(
        filePath: String,
        userId: String,
        callback: (Boolean?, String?) -> Unit
    ) {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                callback(false, "文件不存在")
                return
            }
            val requestFile = file.asRequestBody("image/*".toMediaTypeOrNull())
            val filePart = MultipartBody.Part.createFormData("file", file.name, requestFile)
            val userIdPart = userId.toRequestBody("text/plain".toMediaTypeOrNull())
            NetworkApi.getService(ApiInterface::class.java)
                .loadUserAvatar(filePart, userIdPart)
                .compose(
                    NetworkApi.getInstance().applySchedulers(object :
                        BaseObserverForBasic<BaseResponse<Any>>() {
                        override fun onSuccess(response: BaseResponse<Any>) {
                            callback(true, null)
                        }

                        override fun onFail(e: Throwable) {
                            callback(false, e.message)
                        }
                    })
                )
        } catch (e: Exception) {
            callback(false, e.message ?: "上传头像失败")
        }
    }


    override fun getUserAvatar(
        userId: String,
        callback: (ByteArray?, String?) -> Unit
    ) {
        val ipAddress = PrefsUtil.getInstance(AppApplication.sApplication).baseUrl ?: ""
        val url = "${ipAddress}config/user/getUserAvatar/"

        val formBody = FormBody.Builder()
            .add("userId", userId)
            .build()

        val request = Request.Builder()
            .url(url)
            .post(formBody)
            .build()

        val client = OkHttpClient()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                callback(null, e.message)
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    Log.d("Avatar", "请求成功，状态码=${response.code}")
                    val bytes = response.body?.bytes()
                    callback(bytes, null)
                } else {
                    Log.d("",response.message)
                    callback(null, "服务器返回错误码: ${response.code}")
                }
            }
        })
    }
    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun getUserAvatarSuspend(userId: String): ByteArray? =
        suspendCancellableCoroutine { cont ->
            getUserAvatar(userId) { bytes, error ->
                if (!cont.isActive) return@getUserAvatar

                if (error != null) {
                    cont.resumeWithException(IOException(error))
                } else {
                    cont.resume(bytes, onCancellation = null)
                }
            }
        }


    override fun getWorkOrderStatistics(
        time: String,
        timeType: Int,
        userId: String,
        callback: (WorkorderStatics?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getWorkOrderStatistics(time, timeType, userId)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<WorkorderStatics>>() {
                    override fun onSuccess(response: BaseResponse<WorkorderStatics>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun loadGroupMemberInfo(
        groupId: String,
        callback: (List<GroupMemberInfoResponse>?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getGroupMemberInfoById(groupId)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<List<GroupMemberInfoResponse>>>() {
                    override fun onSuccess(response: BaseResponse<List<GroupMemberInfoResponse>>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }
}




