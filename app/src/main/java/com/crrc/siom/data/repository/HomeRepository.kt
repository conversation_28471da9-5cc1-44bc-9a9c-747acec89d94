package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import com.crrc.common.BaseResponse
import com.crrc.common.bean.request.QRcodeRequest
import com.crrc.common.bean.response.DeviceDetailResponse
import com.crrc.common.bean.response.SearchResponse
import com.crrc.common.bean.response.TaskOverviewResponse
import com.crrc.common.utils.GsonUtil
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic
import com.crrc.siom.ui.home.TaskCounts

interface HomeRepository {
    fun getTaskOverview(callback: (TaskCounts?, String?) -> Unit)
    fun search(query: String, callback: (SearchResponse?, String?) -> Unit)
    fun scan(qrcode: String, callback: (DeviceDetailResponse?, String?) -> Unit)
    fun getDeviceDetailById(id: String, callback: (DeviceDetailResponse?, String?) -> Unit)

}
@SuppressLint("CheckResult")
class HomeRepositoryImpl : HomeRepository {
    override fun getTaskOverview(callback: (TaskCounts?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .getTaskOverview()
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<TaskOverviewResponse>>() {
                override fun onSuccess(response: BaseResponse<TaskOverviewResponse>) {
                    val data = response.data
                    if (data != null) {
                        val taskCounts = TaskCounts(
                            planned = data.planTaskCount,
                            fault = data.faultTaskCount,
                            emergency = data.emergencyTaskCount
                        )
                        callback(taskCounts, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun search(keyword: String, callback: (SearchResponse?, String?) -> Unit) {
        if (keyword.isBlank()) {
            callback(null, null)
            return
        }
        NetworkApi.getService(ApiInterface::class.java)
            .search(keyword)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<SearchResponse>>() {
                override fun onSuccess(response: BaseResponse<SearchResponse>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun scan(
        qrcode: String,
        callback: (DeviceDetailResponse?, String?) -> Unit
    ) {

        val requestObj = GsonUtil.GsonToBean<QRcodeRequest>(qrcode, QRcodeRequest::class.java)
        val data = requestObj.data
        val type = requestObj.type
        NetworkApi.getService(ApiInterface::class.java)
            .scan(data,type)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<DeviceDetailResponse>>() {
                override fun onSuccess(response: BaseResponse<DeviceDetailResponse>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun getDeviceDetailById(
        id: String,
        callback: (DeviceDetailResponse?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getDeviceDetailById(id)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<DeviceDetailResponse>>() {
                override fun onSuccess(response: BaseResponse<DeviceDetailResponse>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }
} 