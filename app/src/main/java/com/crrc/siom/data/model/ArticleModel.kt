package com.crrc.siom.data.model

import android.annotation.SuppressLint
import com.crrc.common.BaseResponse
import com.crrc.common.base.BaseModel
import com.crrc.common.base.IBaseModelListener
import com.crrc.common.base.PagingResult
import com.crrc.common.bean.response.Article
import com.crrc.common.observer.MvvmDataObserver
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.observer.BaseObserver

class ArticleModel : BaseModel<IBaseModelListener<List<Article>>, List<Article>>() {

    init {
        sIsPading = true // 启用分页
    }

    @SuppressLint("CheckResult")
    public override fun loadModelData() {
        NetworkApi.getService(ApiInterface::class.java)
            .getArticles(sPage, sPageSize)
            .compose(
                NetworkApi
                .getInstance()
                .applySchedulers(
                    BaseObserver(
                    this,
                    object : MvvmDataObserver<BaseResponse<List<Article>>>(this) {
                        override fun onSuccess(response: BaseResponse<List<Article>>) {
                            val articles = response.data ?: emptyList()
                            notifyResultSuccessToListener(
                                articles,
                                PagingResult(
                                    isFirstPage = sPage == INIT_PAGE_NUMBER,
                                    isEmpty = articles.isEmpty(),
                                    haveNext = articles.size >= sPageSize
                                )
                            )
                        }

                        override fun onFailure(e: Throwable) {
                            notifyResultFailureToListener(e.message ?: "Unknown error")
                        }
                    }
                )))
    }
}