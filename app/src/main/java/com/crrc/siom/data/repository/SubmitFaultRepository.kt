package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.AlarmResponse
import com.crrc.common.bean.response.DeviceInfo
import com.crrc.common.bean.response.FaultDetailResponse
import com.crrc.common.utils.GetRequestBody
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic


interface SubmitFaultRepository {
    fun device(callback: (Map<String, Map<String, List<DeviceInfo>>>?, String?) -> Unit)
    fun alarmInfo(type:Int,callback: (List<AlarmResponse>?, String?) -> Unit);
    fun faultDetail(alarmId:String,callback: (FaultDetailResponse?, String?) -> Unit);
    fun submitFault(deviceId: Int, alarmId: String, faultDescription: String, repairSuggestion: String, callback: (Boolean?, String?) -> Unit);
}

@SuppressLint("CheckResult")
class SubmitFaultRepositoryImpl : SubmitFaultRepository {
    override fun device(callback: (Map<String, Map<String, List<DeviceInfo>>>?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .device()
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Map<String, Map<String, List<DeviceInfo>>>>>() {
                override fun onSuccess(response: BaseResponse<Map<String, Map<String, List<DeviceInfo>>>>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun alarmInfo(type:Int,callback: (List<AlarmResponse>?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .alarmInfo(type)
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<List<AlarmResponse>>>() {
                override fun onSuccess(response: BaseResponse<List<AlarmResponse>>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun faultDetail(
        alarmId: String,
        callback: (FaultDetailResponse?, String?) -> Unit
    ) {

        NetworkApi.getService(ApiInterface::class.java)
            .faultDetail(alarmId)
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<FaultDetailResponse>>() {
                override fun onSuccess(response: BaseResponse<FaultDetailResponse>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun submitFault(
        deviceId: Int,
        alarmId: String,
        faultDescription: String,
        repairSuggestion: String,
        callback: (Boolean?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .submitFault(deviceId,alarmId,faultDescription,repairSuggestion)
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }
                override fun onFail(e: Throwable) {
                    callback(false, e.message)
                }
            }))
    }


}