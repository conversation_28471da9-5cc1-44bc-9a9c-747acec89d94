package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.EmergencyDetailResponse
import com.crrc.common.bean.response.EmergencyEventListResponse
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic

interface EmergencyMonitorRepository {
    fun getEmergencyEventList(callback: (EmergencyEventListResponse?, String?) -> Unit)
    fun getEmergencyEventStatus(uuid:String,callback: (EmergencyDetailResponse?, String?) -> Unit)
}
@SuppressLint("CheckResult")
class EmergencyMonitorRepositoryImpl:EmergencyMonitorRepository{
    override fun getEmergencyEventList(callback: (EmergencyEventListResponse?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .getEmergencyEventList()
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<EmergencyEventListResponse>>() {
                override fun onSuccess(response: BaseResponse<EmergencyEventListResponse>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun getEmergencyEventStatus(uuid:String,callback: (EmergencyDetailResponse?, String?) -> Unit) {

        NetworkApi.getService(ApiInterface::class.java)
            .getEmergencyEventStatus(uuid)
            .compose (NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<EmergencyDetailResponse>>() {
                override fun onSuccess(response: BaseResponse<EmergencyDetailResponse>) {
                    callback(response.data, null)
                }

                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

}