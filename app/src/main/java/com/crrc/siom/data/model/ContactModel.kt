package com.crrc.siom.data.model

data class Contact(
    val id: String,
    val name: String,
    val type: ContactType,
    val groupId: String
)

data class ContactGroup(
    val id: String,
    val name: String,
    val type: GroupType,
    val contactCount: Int = 0
)

enum class ContactType {
    PERSON,  // 个人
    GROUP    // 群组
}

enum class GroupType {
    TEAM,    // 班组
    CHAT     // 群聊
} 