package com.crrc.siom.data

import com.crrc.common.bean.response.LoginBean
import com.crrc.common.utils.MMkvUtil
import com.google.gson.Gson
import com.crrc.common.Constant.KEY_IS_LOGGED_IN
import com.crrc.common.Constant.KEY_TOKEN
import com.crrc.common.Constant.KEY_USER_ID
import com.crrc.common.Constant.KEY_USER_INFO

/**
 * 会话管理类，用于管理用户登录状态和信息
 */
class SessionManager() {
    private val gson = Gson()
    

    
    /**
     * 保存用户登录信息
     */
    fun saveUserLoginInfo(loginBean: LoginBean) {
        MMkvUtil.encode(KEY_IS_LOGGED_IN, true)
        MMkvUtil.encode(KEY_USER_INFO, gson.toJson(loginBean))
        loginBean.token?.let { MMkvUtil.encode(KEY_TOKEN, it) }
        loginBean.id?.let { MMkvUtil.encode(KEY_USER_ID, it) }
    }
    
    /**
     * 获取用户登录信息
     */
    fun getUserInfo(): LoginBean? {
        val userInfoJson = MMkvUtil.decodeString(KEY_USER_INFO)
        return if (userInfoJson.isNotEmpty()) {
            gson.fromJson(userInfoJson, LoginBean::class.java)
        } else {
            null
        }
    }
    
    /**
     * 获取用户ID
     */
    fun getUserId(): String? {
        val userId = MMkvUtil.decodeString(KEY_USER_ID)
        return if (userId.isNotEmpty()) userId else null
    }
    
    /**
     * 获取用户Token
     */
    fun getToken(): String? {
        val token = MMkvUtil.decodeString(KEY_TOKEN)
        return if (token.isNotEmpty()) token else null
    }
    
    /**
     * 检查用户是否已登录
     */
    fun isLoggedIn(): Boolean {
        return MMkvUtil.decodeBoolean(KEY_IS_LOGGED_IN)
//        return true
    }
    
    /**
     * 清除用户登录信息
     */
    fun logout() {
        MMkvUtil.removeKey(KEY_IS_LOGGED_IN)
        MMkvUtil.removeKey(KEY_USER_INFO)
        MMkvUtil.removeKey(KEY_TOKEN)
        MMkvUtil.removeKey(KEY_USER_ID)
    }
} 