package com.crrc.siom.data.repository;

import android.annotation.SuppressLint;
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.*
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic

interface DocumentRepository {
    fun getCurrentDirs(id: String, callback: (CurrentDirsResponse?, String?) -> Unit)
    fun getDirsList(dirIds: String, callback: (DirsListResponse?, String?) -> Unit)
    fun getLabelList(callback: (List<LabelListResponse>?, String?) -> Unit)
    fun getFilesByLabel(labelId: String, callback: (FilesByLabelResponse?, String?) -> Unit)
    fun searchFileName(fileName: String, callback: (SearchFileResponse?, String?) -> Unit)
    fun downloadFile(url: String, callback: (Any?, String?) -> Unit)
}

@SuppressLint("CheckResult")
class DocumentRepositoryImpl : DocumentRepository {
    private val api = NetworkApi.getService(ApiInterface::class.java)

    override fun getCurrentDirs(id: String, callback: (CurrentDirsResponse?, String?) -> Unit) {
        api.getCurrentDirs(id)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<CurrentDirsResponse>>() {
                override fun onSuccess(response: BaseResponse<CurrentDirsResponse>) {
                    callback(response.data, null)
                }
                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun getDirsList(dirIds: String, callback: (DirsListResponse?, String?) -> Unit) {
        api.getDirsList(dirIds)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<DirsListResponse>>() {
                override fun onSuccess(response: BaseResponse<DirsListResponse>) {
                    callback(response.data, null)
                }
                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun getLabelList(callback: (List<LabelListResponse>?, String?) -> Unit) {
        api.getLabelList()
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<List<LabelListResponse>>>() {
                override fun onSuccess(response: BaseResponse<List<LabelListResponse>>) {
                    callback(response.data, null)
                }
                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun getFilesByLabel(labelId: String, callback: (FilesByLabelResponse?, String?) -> Unit) {
        api.getFilesByLabel(labelId)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<FilesByLabelResponse>>() {
                override fun onSuccess(response: BaseResponse<FilesByLabelResponse>) {
                    callback(response.data, null)
                }
                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun searchFileName(fileName: String, callback: (SearchFileResponse?, String?) -> Unit) {
        api.searchFileName(fileName)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<SearchFileResponse>>() {
                override fun onSuccess(response: BaseResponse<SearchFileResponse>) {
                    callback(response.data, null)
                }
                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }

    override fun downloadFile(id: String, callback: (Any?, String?) -> Unit) {
        api.downLoadFile(id)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(response.data, null)
                }
                override fun onFail(e: Throwable) {
                    callback(null, e.message)
                }
            }))
    }
}




