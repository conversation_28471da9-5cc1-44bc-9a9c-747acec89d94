package com.crrc.siom.manager

import android.app.ActivityManager
import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.crrc.siom.service.WebSocketService

class ChatKeepAliveWorker(
    appContext: Context,
    workerParams: WorkerParameters
) : Worker(appContext, workerParams) {
    override fun doWork(): Result {
        // 检查 ChatService 是否运行，未运行则启动
        if (!isServiceRunning(applicationContext, WebSocketService::class.java)) {
            WebSocketService.start(applicationContext)
        }
        return Result.success()
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): <PERSON><PERSON>an {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        @Suppress("DEPRECATION")
        for (service in manager.getRunningServices(Int.MAX_VALUE)) {
            if (serviceClass.name == service.service.className) {
                return true
            }
        }
        return false
    }
} 