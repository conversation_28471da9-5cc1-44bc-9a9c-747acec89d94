package com.crrc.siom.manager

import com.crrc.common.utils.GsonUtil
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.model.Heartbeat
import kotlinx.coroutines.*
import okhttp3.*
import okio.ByteString
import java.util.concurrent.TimeUnit

class WebSocketManager private constructor(
    private val url: String,
    private val listener: WebSocketManagerListener,
    private val heartbeatInterval: Long = 30_000L,
    private val reconnectInterval: Long = 5_000L
) {

    private var webSocket: WebSocket? = null

    @Volatile private var isConnected = false
    @Volatile private var isConnecting = false

    private val lock = Any()

    private var reconnectJob: Job? = null
    private var heartbeatJob: Job? = null

    private val client = OkHttpClient.Builder()
        .pingInterval(heartbeatInterval, TimeUnit.MILLISECONDS)
        .build()

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    fun connect() {
        synchronized(lock) {
            if (isConnected || isConnecting) {
                println("WebSocket is already connected or connecting, skip.")
                return
            }
            isConnecting = true
        }

        val request = Request.Builder().url(url).build()
        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(ws: WebSocket, response: Response) {
                synchronized(lock) {
                    isConnected = true
                    isConnecting = false
                }
                listener.onConnected()
                startHeartbeat()
            }

            override fun onMessage(ws: WebSocket, text: String) {
                listener.onMessage(text)
            }

            override fun onMessage(ws: WebSocket, bytes: ByteString) {
                listener.onMessage(bytes.utf8())
            }

            override fun onClosing(ws: WebSocket, code: Int, reason: String) {
                synchronized(lock) {
                    isConnected = false
                    isConnecting = false
                }
                listener.onDisconnected(reason)
                stopHeartbeat()
            }

            override fun onClosed(ws: WebSocket, code: Int, reason: String) {
                synchronized(lock) {
                    isConnected = false
                    isConnecting = false
                }
                listener.onDisconnected(reason)
                stopHeartbeat()
            }

            override fun onFailure(ws: WebSocket, t: Throwable, response: Response?) {
                synchronized(lock) {
                    isConnected = false
                    isConnecting = false
                }
                listener.onError(t)
                stopHeartbeat()
                tryReconnect()
            }
        })
    }

    fun disconnect() {
        synchronized(lock) {
            isConnected = false
            isConnecting = false
        }
        stopHeartbeat()
        reconnectJob?.cancel()
        webSocket?.close(1000, "Manual disconnect")
        webSocket = null
    }

    fun sendMessage(message: String): Boolean {
        return webSocket?.send(message) ?: false
    }

    fun isActive(): Boolean = isConnected

    private fun startHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = scope.launch {
            while (isConnected) {
                delay(heartbeatInterval)
                if (isConnected) {
                    val heartbeat = Heartbeat(
                        userId = SessionManager().getUserId().toString()
                    )
                    webSocket?.send(GsonUtil.GsonString(heartbeat))
                }
            }
        }
    }

    private fun stopHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = null
    }

    private fun tryReconnect() {
        if (reconnectJob?.isActive == true) return

        reconnectJob = scope.launch {
            var delayTime = reconnectInterval
            while (!isConnected) {
                delay(delayTime)
                println("Reconnecting WebSocket...")
                connect()
                delayTime = (delayTime * 2).coerceAtMost(60_000L) // 最大 60 秒
            }
        }
    }

    fun release() {
        disconnect()
        releaseInstance()
        scope.cancel()
    }

    companion object {
        @Volatile
        private var instance: WebSocketManager? = null

        fun getInstance(
            url: String,
            listener: WebSocketManagerListener,
            heartbeatInterval: Long = 30_000L,
            reconnectInterval: Long = 5_000L
        ): WebSocketManager {
            return instance ?: synchronized(this) {
                instance ?: WebSocketManager(url, listener, heartbeatInterval, reconnectInterval).also { instance = it }
            }
        }

        fun releaseInstance() {
            instance?.disconnect()
            instance = null
        }
    }
}

interface WebSocketManagerListener {
    fun onConnected()
    fun onDisconnected(reason: String)
    fun onMessage(message: String)
    fun onError(t: Throwable)
}
