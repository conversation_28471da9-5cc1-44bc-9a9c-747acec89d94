package com.crrc.siom.manager

import com.crrc.siom.data.model.WsMessage
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

object ChatMessageCenter {
    // 新消息流，所有页面都可以 collect
    private val _newMessageFlow = MutableSharedFlow<WsMessage>(extraBufferCapacity = 100)
    val newMessageFlow = _newMessageFlow.asSharedFlow()

    fun emitNewMessage(message: WsMessage) {
        _newMessageFlow.tryEmit(message)
    }
}