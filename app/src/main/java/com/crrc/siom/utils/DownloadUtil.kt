package com.crrc.siom.utils

import android.annotation.SuppressLint
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Environment
import android.util.Log
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import com.crrc.siom.data.SessionManager
import java.io.File
import android.database.Cursor
import com.crrc.siom.ui.document.getMimeType
import com.crrc.siom.ui.document.openFileWithIntent
import kotlinx.coroutines.*


object DownloadUtil {
    private const val TAG = "DownloadUtil"
    /**
     * 下载文件到本地 Download 目录，返回 downloadId
     * @param context 上下文
     * @param fileUrl 文件下载链接（可拼接文件ID）
     * @param fileName 保存到本地的文件名
     * @param token 可选，若需要鉴权可传入 Bearer Token
     * @return downloadId
     */
    fun downloadFile(
        context: Context,
        fileUrl: String,
        fileName: String,
        token: String? = SessionManager().getToken()
    ): Long {
        Log.d(TAG, "downloadFile called. url=$fileUrl, fileName=$fileName, token=${token?.let { it.take(6) + "***" } ?: "null"}")
        return try {
            val request = DownloadManager.Request(fileUrl.toUri())
                .setTitle(fileName)
                .setDescription("正在下载")
                .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
                .setAllowedOverMetered(true)
                .setAllowedOverRoaming(true)
            if (!token.isNullOrEmpty()) {
                request.addRequestHeader("Authorization", "Bearer $token")
            }
            val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            val downloadId = downloadManager.enqueue(request)
            logDownloadProgress(context,downloadId)
            Log.d(TAG, "Download enqueued. downloadId=$downloadId")
            Toast.makeText(context, "已开始下载", Toast.LENGTH_SHORT).show()
            downloadId
        } catch (e: Exception) {
            Log.e(TAG, "下载失败: ${e.message}", e)
            Toast.makeText(context, "下载失败: ${e.message}", Toast.LENGTH_LONG).show()
            -1L
        }
    }

    /**
     * 注册下载完成监听，下载完成后回调本地文件
     * @param context 上下文
     * @param downloadId 下载任务id
     * @param fileName 下载的文件名
     * @param onComplete 下载完成回调，参数为本地文件（失败为null）
     */
    fun registerDownloadReceiver(
        context: Context,
        downloadId: Long,
        fileName: String,
        onComplete: (file: File?) -> Unit
    ) {
        val receiver = object : BroadcastReceiver() {
            @SuppressLint("Range")
            override fun onReceive(ctx: Context, intent: Intent) {
                val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                Log.d(TAG, "onReceive called. id=$id, downloadId=$downloadId")
                if (id == downloadId) {
                    val dm = ctx.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                    val query = DownloadManager.Query().setFilterById(downloadId)
                    val cursor = dm.query(query)
                    var file: File? = null
                    if (cursor != null && cursor.moveToFirst()) {
                        val uriString = cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI))
                        Log.d("DownloadUtil", "uriString=$uriString")
                        if (uriString != null) {
                            val uri = Uri.parse(uriString)
                            if ("file" == uri.scheme) {
                                file = File(uri.path!!)
                            } else if ("content" == uri.scheme) {
                                val downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                                val possibleFile = File(downloadDir, fileName)
                                if (possibleFile.exists()) {
                                    file = possibleFile
                                }
                            }
                        } else {
                            val downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                            val possibleFile = File(downloadDir, fileName)
                            if (possibleFile.exists()) {
                                file = possibleFile
                            }
                        }
                    }
                    cursor?.close()
                    onComplete(file)
                    ctx.unregisterReceiver(this)
                }
            }
        }
        //todo 这里如果考虑sdk 34的话会无法触发完成回调
        context.registerReceiver(
            receiver,
            IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
        )

    }
    fun logDownloadProgress(
        context: Context,
        downloadId: Long,
        intervalMs: Long = 500L
    ) {
        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        CoroutineScope(Dispatchers.IO).launch {
            var isDownloading = true
            while (isDownloading) {
                val query = DownloadManager.Query().setFilterById(downloadId)
                val cursor: Cursor? = downloadManager.query(query)
                if (cursor != null && cursor.moveToFirst()) {
                    val bytesDownloaded =
                        cursor.getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR))
                    val bytesTotal =
                        cursor.getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_TOTAL_SIZE_BYTES))
                    val status =
                        cursor.getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS))
                    val progress = if (bytesTotal > 0) (bytesDownloaded * 100 / bytesTotal) else -1
                    Log.d(TAG, "DownloadId=$downloadId, status=$status, $bytesDownloaded/$bytesTotal ($progress%)")
                    isDownloading = status == DownloadManager.STATUS_RUNNING || status == DownloadManager.STATUS_PAUSED
                    if (status == DownloadManager.STATUS_SUCCESSFUL || status == DownloadManager.STATUS_FAILED) {
                        isDownloading = false
                    }
                } else {
                    isDownloading = false
                }
                cursor?.close()
                delay(intervalMs)
            }
        }
    }

    fun downloadAndOpen(context: Context,fileName: String,url: String){
        val file = File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            fileName
        )
        if (file.exists()) {
            openFileWithIntent(context, file, getMimeType(fileName))
        } else {
            val downloadId = downloadFile(context, url, fileName)
            if (downloadId != -1L) {
                registerDownloadReceiver(
                    context,
                    downloadId,
                    fileName
                ) { downloadedFile ->
                    if (downloadedFile != null) {
                        Toast.makeText(context, "下载成功", Toast.LENGTH_SHORT)
                            .show()
                        openFileWithIntent(
                            context,
                            downloadedFile,
                            getMimeType(downloadedFile.name)
                        )
                    } else {
                        Toast.makeText(context, "下载失败", Toast.LENGTH_SHORT)
                            .show()
                    }
                }
            }
        }
    }


}




