package com.crrc.siom;


import android.app.Application;

import com.crrc.network.base.INetworkRequiredInfo;


/**
 * <AUTHOR>
 */
public class NetworkRequestInfo implements INetworkRequiredInfo {
    private final Application mApplication;

    public NetworkRequestInfo(Application application) {
        this.mApplication = application;
    }

    @Override
    public String getAppVersionName() {
        return BuildConfig.VERSION_NAME;
    }

    @Override
    public String getAppVersionCode() {
        return String.valueOf(BuildConfig.VERSION_CODE);
    }

    @Override
    public boolean isDebug() {
        return BuildConfig.DEBUG;
    }

    @Override
    public Application getApplicationContext() {
        return mApplication;
    }

}
