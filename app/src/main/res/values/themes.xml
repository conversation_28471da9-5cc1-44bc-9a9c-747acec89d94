<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Theme.SIOM" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/compose_primary</item>
        <item name="colorPrimaryDark">@color/compose_primary_dark</item>
        <item name="colorAccent">@color/compose_primary</item>
        <item name="android:statusBarColor">@color/compose_background</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:colorBackground">@color/compose_background</item>
        <item name="android:textColorPrimary">@color/compose_on_surface</item>
        <item name="android:textColorSecondary">@color/compose_on_surface_variant</item>
    </style>
    
</resources>