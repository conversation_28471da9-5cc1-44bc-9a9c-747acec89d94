<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <ImageView
            android:id="@+id/iv_storage"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_build"
            android:tint="@color/compose_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_item_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/compose_on_surface"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_storage"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="板卡1" />

        <TextView
            android:id="@+id/tv_produce_date_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="生产日期："
            android:textColor="@color/compose_on_surface_variant"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="@id/tv_item_name"
            app:layout_constraintTop_toBottomOf="@id/tv_item_name" />

        <TextView
            android:id="@+id/tv_produce_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/compose_on_surface"
            android:textSize="14sp"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_produce_date_label"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_produce_date_label"
            tools:text="2023-01-22 23:34:22" />

        <TextView
            android:id="@+id/tv_quantity_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="库存："
            android:textColor="@color/compose_on_surface_variant"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="@id/tv_produce_date_label"
            app:layout_constraintTop_toBottomOf="@id/tv_produce_date_label" />

        <TextView
            android:id="@+id/tv_quantity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/compose_on_surface"
            android:textSize="14sp"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_quantity_label"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_quantity_label"
            tools:text="2" />

        <TextView
            android:id="@+id/tv_other_info_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="其他信息："
            android:textColor="@color/compose_on_surface_variant"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="@id/tv_quantity_label"
            app:layout_constraintTop_toBottomOf="@id/tv_quantity_label" />

        <TextView
            android:id="@+id/tv_other_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/compose_on_surface"
            android:textSize="14sp"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_other_info_label"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_other_info_label"
            tools:text="2" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView> 