<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <ImageView
            android:id="@+id/iv_warning"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_warning"
            android:tint="#FF0000"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_alarm_id"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/compose_on_surface"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_warning"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题XXXX" />

        <TextView
            android:id="@+id/tv_alarm_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/compose_on_surface_variant"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@id/tv_alarm_id"
            app:layout_constraintTop_toBottomOf="@id/tv_alarm_id"
            tools:text="2023-01-22 23:34:22" />

        <TextView
            android:id="@+id/tv_alarm_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/compose_on_surface"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_alarm_id"
            app:layout_constraintTop_toBottomOf="@id/tv_alarm_time"
            tools:text="报警描述：XXXXXXXXXXXXXXXXXXXXXXXX" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView> 