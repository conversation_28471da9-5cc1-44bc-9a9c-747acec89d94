<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.qrcode.QRScannerActivity">

    <!-- 扫描视图 - 使用默认布局 -->
    <com.journeyapps.barcodescanner.DecoratedBarcodeView
        android:id="@+id/barcode_scanner"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/compose_surface"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:titleTextColor="@color/compose_on_surface"
        app:title="线路图" />



    <!-- 状态提示文本 -->
    <TextView
        android:id="@+id/status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请将二维码放入框内"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.65" />

    <!-- 闪光灯按钮 -->
    <ImageButton
        android:id="@+id/flashlight_button"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginBottom="48dp"
        android:background="@drawable/round_button_bg"
        android:contentDescription="闪光灯"
        android:src="@drawable/ic_flashlight_off"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 闪光灯文本提示 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="轻触点亮"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@id/flashlight_button"
        app:layout_constraintStart_toStartOf="@id/flashlight_button"
        app:layout_constraintTop_toBottomOf="@id/flashlight_button" />

</androidx.constraintlayout.widget.ConstraintLayout> 