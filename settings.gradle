pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
//        mavenCentral()
//        gradlePluginPortal()
        maven { url "https://jitpack.io" }
        maven {
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/central'
        }
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
//        mavenCentral()
        maven { url "https://jitpack.io" }
        maven {
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/central'
        }
        maven {
            url "https://plugins.gradle.org/m2/"
        }
        google()

    }
}

rootProject.name = "siom"
include ':app'
include ':network'
include ':common'
