package com.crrc.common.bean.response;


public class DeviceDetailResponse {
    private long id;
    private String internalId;
    private Object children;
    private String name;
    private String alias;
    private String type;
    private String station;

    private String location;
    private String locationCode;
    private String onlineTime;
    private String status;
    private String replacementPeriod;
    private String maintenancePeriod;
    private String warrantyExpirationTime;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getInternalId() {
        return internalId;
    }

    public void setInternalId(String internalId) {
        this.internalId = internalId;
    }

    public Object getChildren() {
        return children;
    }

    public void setChildren(Object children) {
        this.children = children;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStation() {
        return station;
    }

    public void setStation(String station) {
        this.station = station;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(String onlineTime) {
        this.onlineTime = onlineTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReplacementPeriod() {
        return replacementPeriod;
    }

    public void setReplacementPeriod(String replacementPeriod) {
        this.replacementPeriod = replacementPeriod;
    }

    public String getMaintenancePeriod() {
        return maintenancePeriod;
    }

    public void setMaintenancePeriod(String maintenancePeriod) {
        this.maintenancePeriod = maintenancePeriod;
    }

    public String getWarrantyExpirationTime() {
        return warrantyExpirationTime;
    }

    public void setWarrantyExpirationTime(String warrantyExpirationTime) {
        this.warrantyExpirationTime = warrantyExpirationTime;
    }
}
