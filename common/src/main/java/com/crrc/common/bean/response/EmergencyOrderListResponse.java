package com.crrc.common.bean.response;

import java.util.List;


public class EmergencyOrderListResponse {
    private List<EmergencyOrderRecord> records;
    private int total;
    private int size;
    private int current;
    
    public List<EmergencyOrderRecord> getRecords() {
        return records;
    }
    
    public void setRecords(List<EmergencyOrderRecord> records) {
        this.records = records;
    }
    
    public int getTotal() {
        return total;
    }
    
    public void setTotal(int total) {
        this.total = total;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
    }
    
    public int getCurrent() {
        return current;
    }
    
    public void setCurrent(int current) {
        this.current = current;
    }
    
    public static class EmergencyOrderRecord extends BaseOrderRecord{

    }
} 