package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;

public abstract  class BaseOrderRecord {

    @SerializedName("id")
    private String id;  // 工单ID

    @SerializedName("priority")
    private String priority;  // 优先级

    @SerializedName("title")
    private String title;  // 工单标题

    @SerializedName("status")
    private String status;  // 工单状态

    @SerializedName("type")
    private String type;  // 计划工单类型字段

    @SerializedName("number")
    private String number;  // 工单编号

    @SerializedName("line")
    private String line;  // 所属线路

    @SerializedName("station")
    private String station;  // 所属车站

    @SerializedName("startDate")
    private String startDate;  // 开始时间

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getStation() {
        return station;
    }

    public void setStation(String station) {
        this.station = station;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
}
