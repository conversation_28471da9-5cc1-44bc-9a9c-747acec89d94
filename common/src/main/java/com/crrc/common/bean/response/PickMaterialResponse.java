package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 领料清单响应模型
 */
public class PickMaterialResponse {
    @SerializedName("standby")
    private List<Item> standbyItems; // 备品备件列表
    
    @SerializedName("tool")
    private List<Item> toolItems; // 工器具列表
    
    @SerializedName("note")
    private String note; // 备注
    
    public static class Item {
        @SerializedName("id")
        private int id; // 物品ID
        
        @SerializedName("name")
        private String name; // 物品名称
        
        @SerializedName("amount")
        private int amount; // 数量

        @SerializedName("stock")
        private int stock; // 库存
        
        public int getId() {
            return id;
        }
        
        public void setId(int id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public int getAmount() {
            return amount;
        }
        
        public void setAmount(int amount) {
            this.amount = amount;
        }

        public int getStock() {
            return stock;
        }

        public void setStock(int stock) {
            this.stock = stock;
        }
    }
    
    public List<Item> getStandbyItems() {
        return standbyItems;
    }
    
    public void setStandbyItems(List<Item> standbyItems) {
        this.standbyItems = standbyItems;
    }
    
    public List<Item> getToolItems() {
        return toolItems;
    }
    
    public void setToolItems(List<Item> toolItems) {
        this.toolItems = toolItems;
    }
    
    public String getNote() {
        return note;
    }
    
    public void setNote(String note) {
        this.note = note;
    }
} 