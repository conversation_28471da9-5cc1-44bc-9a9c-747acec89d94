package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;

/**
 * 车站报警信息响应模型
 */
public class StationAlarmResponse {
    @SerializedName("alarmId")
    private String alarmId;  // 报警标识
    
    @SerializedName("level")
    private String level;  // 报警等级
    
    @SerializedName("lastAlarmDate")
    private String lastAlarmDate;  // 报警时间
    
    @SerializedName("alarmDescription")
    private String alarmDescription;  // 报警描述

    public String getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLastAlarmDate() {
        return lastAlarmDate;
    }

    public void setLastAlarmDate(String lastAlarmDate) {
        this.lastAlarmDate = lastAlarmDate;
    }

    public String getAlarmDescription() {
        return alarmDescription;
    }

    public void setAlarmDescription(String alarmDescription) {
        this.alarmDescription = alarmDescription;
    }
} 