package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 故障上报响应模型
 */
public class FaultReportResponse {
    @SerializedName("stations")
    private List<Station> stations;  // 接车站列表
    
    @SerializedName("description")
    private String description;  // 故障描述
    
    @SerializedName("suggestion")
    private String suggestion;  // 维修建议
    
    public static class Station {
        @SerializedName("id")
        private String id;  // 车站id
        
        @SerializedName("name")
        private String name;  // 车站名称
        
        @SerializedName("selected")
        private boolean selected;  // 是否选中
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public boolean isSelected() {
            return selected;
        }
        
        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }
    
    public List<Station> getStations() {
        return stations;
    }
    
    public void setStations(List<Station> stations) {
        this.stations = stations;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSuggestion() {
        return suggestion;
    }
    
    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }
} 