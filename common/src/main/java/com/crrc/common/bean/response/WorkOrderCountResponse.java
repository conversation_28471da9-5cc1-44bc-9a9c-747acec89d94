package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;

/**
 * 工单数量统计响应模型
 */
public class WorkOrderCountResponse {
    
    @SerializedName("plan")
    private int planCount; // 计划工单数量
    
    @SerializedName("fault")
    private int faultCount; // 故障工单数量
    
    @SerializedName("emergency")
    private int emergencyCount; // 应急工单数量
    
    @SerializedName("temp")
    private int temporaryCount; // 临时工单数量
    
    public int getPlanCount() {
        return planCount;
    }
    
    public void setPlanCount(int planCount) {
        this.planCount = planCount;
    }
    
    public int getFaultCount() {
        return faultCount;
    }
    
    public void setFaultCount(int faultCount) {
        this.faultCount = faultCount;
    }
    
    public int getEmergencyCount() {
        return emergencyCount;
    }
    
    public void setEmergencyCount(int emergencyCount) {
        this.emergencyCount = emergencyCount;
    }
    
    public int getTemporaryCount() {
        return temporaryCount;
    }
    
    public void setTemporaryCount(int temporaryCount) {
        this.temporaryCount = temporaryCount;
    }
} 