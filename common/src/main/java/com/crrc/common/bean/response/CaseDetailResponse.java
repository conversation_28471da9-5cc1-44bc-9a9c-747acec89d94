package com.crrc.common.bean.response;

import java.util.List;
import java.util.ArrayList;

/**
 * 案例详情响应
 */
public class CaseDetailResponse {
    /**
     * 案例标题
     */
    private String title;
    
    /**
     * 报警时间
     */
    private String alarmTime;
    
    /**
     * 报警内容
     */
    private String alarmContent;
    
    /**
     * 报警原因
     */
    private String alarmReason;
    
    /**
     * 设备名称
     */
    private String device;
    
    /**
     * 操作人
     */
    private String casePerson;
    
    /**
     * 维修建议
     */
    private String suggestion;

    private List<ParticipantVOS>  chatParticipantVOS;
    private List<ChatMessage>  chatMessageList;


    /**
     * 相关图片
     */
    private List<String> picture;
    
    /**
     * 相关视频
     */
    private List<String> video;
    
    /**
     * 备注
     */
    private String remark;

    public CaseDetailResponse() {
        this.picture = new ArrayList<>();
        this.video = new ArrayList<>();
    }

    public void setChatParticipantVOS(List<ParticipantVOS> chatParticipantVOS) {
        this.chatParticipantVOS = chatParticipantVOS;
    }

    public List<ParticipantVOS> getChatParticipantVOS() {
        return chatParticipantVOS;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(String alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getAlarmReason() {
        return alarmReason;
    }

    public void setAlarmReason(String alarmReason) {
        this.alarmReason = alarmReason;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getCasePerson() {
        return casePerson;
    }

    public void setCasePerson(String casePerson) {
        this.casePerson = casePerson;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public List<String> getPicture() {
        return picture;
    }

    public void setPicture(List<String> picture) {
        this.picture = picture;
    }

    public List<String> getVideo() {
        return video;
    }

    public void setChatMessageList(List<ChatMessage> chatMessageList) {
        this.chatMessageList = chatMessageList;
    }

    public List<ChatMessage> getChatMessageList() {
        return chatMessageList;
    }

    public void setVideo(List<String> video) {
        this.video = video;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static class ParticipantVOS{
        private String id;
        private String realName;
        private String avatar;
        private String source;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }
    }
}