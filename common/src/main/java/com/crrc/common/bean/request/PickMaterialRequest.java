package com.crrc.common.bean.request;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 领料请求模型
 */
public class PickMaterialRequest {
    @SerializedName("id")
    private String workOrderId; // 工单ID
    
    @SerializedName("standby")
    private List<Integer> standbyIds; // 备品备件ID列表
    
    @SerializedName("tool")
    private List<Integer> toolIds; // 工器具ID列表
    
    @SerializedName("note")
    private String note; // 备注
    
    public String getWorkOrderId() {
        return workOrderId;
    }
    
    public void setWorkOrderId(String workOrderId) {
        this.workOrderId = workOrderId;
    }
    
    public List<Integer> getStandbyIds() {
        return standbyIds;
    }
    
    public void setStandbyIds(List<Integer> standbyIds) {
        this.standbyIds = standbyIds;
    }
    
    public List<Integer> getToolIds() {
        return toolIds;
    }
    
    public void setToolIds(List<Integer> toolIds) {
        this.toolIds = toolIds;
    }
    
    public String getNote() {
        return note;
    }
    
    public void setNote(String note) {
        this.note = note;
    }
} 