package com.crrc.common.bean.response;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * Date on 2021/10/12
 * Des:登录sm4解密数据
 */
public class TokenIndoData implements Parcelable {

    private String access_token;
    private String token_type;
    private String refresh_token;
    private int expires_in;
    private String scope;
    private int refresh_interval;
    private String orgName;
    private String phone;
    private long tenantId;
    private String fullName;
    private String tenantSchema;
    private long userId;
    private long orgId;
    private String jti;

    protected TokenIndoData(Parcel in) {
        access_token = in.readString();
        token_type = in.readString();
        refresh_token = in.readString();
        expires_in = in.readInt();
        scope = in.readString();
        refresh_interval = in.readInt();
        orgName = in.readString();
        phone = in.readString();
        tenantId = in.readLong();
        fullName = in.readString();
        tenantSchema = in.readString();
        userId = in.readLong();
        orgId = in.readLong();
        jti = in.readString();
    }

    public static final Creator<TokenIndoData> CREATOR = new Creator<TokenIndoData>() {
        @Override
        public TokenIndoData createFromParcel(Parcel in) {
            return new TokenIndoData(in);
        }

        @Override
        public TokenIndoData[] newArray(int size) {
            return new TokenIndoData[size];
        }
    };

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getToken_type() {
        return token_type;
    }

    public void setToken_type(String token_type) {
        this.token_type = token_type;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public int getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(int expires_in) {
        this.expires_in = expires_in;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public int getRefresh_interval() {
        return refresh_interval;
    }

    public void setRefresh_interval(int refresh_interval) {
        this.refresh_interval = refresh_interval;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public long getTenantId() {
        return tenantId;
    }

    public void setTenantId(long tenantId) {
        this.tenantId = tenantId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getTenantSchema() {
        return tenantSchema;
    }

    public void setTenantSchema(String tenantSchema) {
        this.tenantSchema = tenantSchema;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getOrgId() {
        return orgId;
    }

    public void setOrgId(long orgId) {
        this.orgId = orgId;
    }

    public String getJti() {
        return jti;
    }

    public void setJti(String jti) {
        this.jti = jti;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(access_token);
        dest.writeString(token_type);
        dest.writeString(refresh_token);
        dest.writeInt(expires_in);
        dest.writeString(scope);
        dest.writeInt(refresh_interval);
        dest.writeString(orgName);
        dest.writeString(phone);
        dest.writeLong(tenantId);
        dest.writeString(fullName);
        dest.writeString(tenantSchema);
        dest.writeLong(userId);
        dest.writeLong(orgId);
        dest.writeString(jti);
    }
}
