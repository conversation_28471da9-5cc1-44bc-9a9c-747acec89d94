package com.crrc.common.bean.response;

import java.util.List;

public class TempWorkOrderParamResponse {
    private String title;
    private List<Item> type;
    private List<Item> line;
    private List<Item> station;
    private List<Group> group;
    private List<Item> priority;
    private List<AmountItem> standby;
    private List<AmountItem> tool;
    private String description;
    private String maintenanceGuide;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<Item> getType() {
        return type;
    }

    public void setType(List<Item> type) {
        this.type = type;
    }

    public List<Item> getLine() {
        return line;
    }

    public void setLine(List<Item> line) {
        this.line = line;
    }

    public List<Item> getStation() {
        return station;
    }

    public void setStation(List<Item> station) {
        this.station = station;
    }

    public List<Group> getGroup() {
        return group;
    }

    public void setGroup(List<Group> group) {
        this.group = group;
    }

    public List<Item> getPriority() {
        return priority;
    }

    public void setPriority(List<Item> priority) {
        this.priority = priority;
    }

    public List<AmountItem> getStandby() {
        return standby;
    }

    public void setStandby(List<AmountItem> standby) {
        this.standby = standby;
    }

    public List<AmountItem> getTool() {
        return tool;
    }

    public void setTool(List<AmountItem> tool) {
        this.tool = tool;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMaintenanceGuide() {
        return maintenanceGuide;
    }

    public void setMaintenanceGuide(String maintenanceGuide) {
        this.maintenanceGuide = maintenanceGuide;
    }

    public static class Item {
        private String id;
        private String name;
        private boolean selected;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean getSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }

    public static class Group {
        private String id;
        private String name;
        private String managerId;
        private String manager;
        private boolean selected;
        private List<Item> members;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getManagerId() {
            return managerId;
        }

        public void setManagerId(String managerId) {
            this.managerId = managerId;
        }

        public String getManager() {
            return manager;
        }

        public void setManager(String manager) {
            this.manager = manager;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }

        public List<Item> getMembers() {
            return members;
        }

        public void setMembers(List<Item> members) {
            this.members = members;
        }
    }

    public static class AmountItem {
        private int id;
        private String name;
        private int amount;

        private boolean selected;

        public void setSelected(boolean selected) {
            this.selected = selected;
        }

        public boolean getSelected() {
            return selected;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getAmount() {
            return amount;
        }

        public void setAmount(int amount) {
            this.amount = amount;
        }
    }
}
