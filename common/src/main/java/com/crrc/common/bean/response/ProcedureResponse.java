package com.crrc.common.bean.response;

import java.util.List;


public  class ProcedureResponse {
    private String id;
    private String content;
    private String note;
    private String url;
    private List<Procedure> procedures;

    private List<WorkOrderDetailResponse.Guide> guides;

    public List<WorkOrderDetailResponse.Guide> getGuides() {
        return guides;
    }

    public void setGuides(List<WorkOrderDetailResponse.Guide> guides) {
        this.guides = guides;
    }

    public static class Procedure {
        private String id;
        private int index;
        private String item;
        private String standard;
        private int status;

        // Getters and setters for Procedure fields
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public String getStandard() {
            return standard;
        }

        public void setStandard(String standard) {
            this.standard = standard;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }
    }

    // Getters and setters for Data fields
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<Procedure> getProcedures() {
        return procedures;
    }

    public void setProcedures(List<Procedure> procedures) {
        this.procedures = procedures;
    }
}


