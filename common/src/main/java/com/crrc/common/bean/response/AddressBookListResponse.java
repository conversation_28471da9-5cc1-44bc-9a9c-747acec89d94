package com.crrc.common.bean.response;

import java.util.List;


public class AddressBookListResponse {

    private List<ChatGroup> chatGroups;

    private List<ChatUser> chatUsers;
    private List<ExpertGroup> expertGroups;


    public void setExpertGroups(List<ExpertGroup> expertGroups) {
        this.expertGroups = expertGroups;
    }

    public List<ExpertGroup> getExpertGroups() {
        return expertGroups;
    }

    public List<ChatGroup> getChatGroups() {
        return chatGroups;
    }

    public void setChatGroups(List<ChatGroup> chatGroups) {
        this.chatGroups = chatGroups;
    }

    public List<ChatUser> getChatUsers() {
        return chatUsers;
    }

    public void setChatUsers(List<ChatUser> chatUsers) {
        this.chatUsers = chatUsers;
    }

    public static class ChatGroup{
        private String id;
        private String name;


        private String info;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getInfo() {
            return info;
        }

        public void setInfo(String info) {
            this.info = info;
        }

    }

    public static class ChatUser{
        private String id;
        private String realName;
        private String avatar;

        private String groupName;

        public void setGroupName(String groupName) {
            this.groupName = groupName;
        }

        public String getGroupName() {
            return groupName;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getRealName() {
            return realName;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }
    }

    public static class ExpertGroup{
        private String id;
        private String name;
        private String info;
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getInfo() {
            return info;
        }

        public void setInfo(String info) {
            this.info = info;
        }
    }
}
