package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class MembersResponse {

    private List<FilterParam> members;


    public List<FilterParam> getMembers() {
        return members;
    }

    public void setMembers(List<FilterParam> members) {
        this.members = members;
    }

    public static class FilterParam {
        @SerializedName("id")
        private String id;   //成员id

        @SerializedName("name")
        private String name;   //成员姓名

        @SerializedName("selected")
        private boolean selected;    //是否选中

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }
}
