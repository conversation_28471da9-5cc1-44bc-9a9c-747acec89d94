package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 工单详情响应模型
 */
public class WorkOrderDetailResponse {
    @SerializedName("id")
    private String id;
    
    @SerializedName("priority")
    private String priority;

    @SerializedName("currentStatus")
    private String currentStatus;
    
    @SerializedName("title")
    private String title;
    
    @SerializedName("status")
    private String status;
    
    @SerializedName("type")
    private String type;
    
    @SerializedName("group")
    private String group;
    
    @SerializedName("manager")
    private Person manager;
    
    @SerializedName("repairman")
    private Person repairman;
    
    @SerializedName("standby")
    private List<Item> standby;
    
    @SerializedName("tool")
    private List<Item> tool;
    
    @SerializedName("number")
    private String number;
    
    @SerializedName("line")
    private String line;
    
    @SerializedName("station")
    private String station;
    
    @SerializedName("startDate")
    private String startDate;
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("suggestion")
    private String suggestion;
    
    @SerializedName("guides")
    private List<Guide> guides;
    
    // 人员信息
    public static class Person {
        @SerializedName("name")
        private String name;
        
        @SerializedName("phone")
        private String phone;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
    }
    
    // 物品信息（备品备件或工器具）
    public static class Item {
        @SerializedName("id")
        private int id;
        
        @SerializedName("name")
        private String name;
        
        @SerializedName("amount")
        private int amount;
        
        public int getId() {
            return id;
        }
        
        public void setId(int id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public int getAmount() {
            return amount;
        }
        
        public void setAmount(int amount) {
            this.amount = amount;
        }
    }
    
    // 维修手册
    public static class Guide {
        @SerializedName("id")
        private String id;
        
        @SerializedName("name")
        private String name;

        @SerializedName("url")
        private String url;

        @SerializedName("suffix")
        private String suffix;


        public String getSuffix() {
            return suffix;
        }

        public void setSuffix(String suffix) {
            this.suffix = suffix;
        }

        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getUrl() {
            return url;
        }
        
        public void setUrl(String url) {
            this.url = url;
        }
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getPriority() {
        return priority;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getGroup() {
        return group;
    }
    
    public void setGroup(String group) {
        this.group = group;
    }
    
    public Person getManager() {
        return manager;
    }
    
    public void setManager(Person manager) {
        this.manager = manager;
    }
    
    public Person getRepairman() {
        return repairman;
    }
    
    public void setRepairman(Person repairman) {
        this.repairman = repairman;
    }
    
    public List<Item> getStandby() {
        return standby;
    }
    
    public void setStandby(List<Item> standby) {
        this.standby = standby;
    }
    
    public List<Item> getTool() {
        return tool;
    }
    
    public void setTool(List<Item> tool) {
        this.tool = tool;
    }
    
    public String getNumber() {
        return number;
    }
    
    public void setNumber(String number) {
        this.number = number;
    }
    
    public String getLine() {
        return line;
    }
    
    public void setLine(String line) {
        this.line = line;
    }
    
    public String getStation() {
        return station;
    }
    
    public void setStation(String station) {
        this.station = station;
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSuggestion() {
        return suggestion;
    }
    
    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }
    
    public List<Guide> getGuides() {
        return guides;
    }
    
    public void setGuides(List<Guide> guides) {
        this.guides = guides;
    }
} 