package com.crrc.common.bean.response;

import java.util.List;

/**
 * 用于故障提报
 */
public  class DeviceInfo {
        private Integer id;
        private List<Integer> children;
        private String name;
        private String alias;
        private Integer type;
        private Integer stationId;
        private String location;
        private String locationCode;
        private Long onlineTime;
        private Integer status;
        private Integer replacementPeriod;
        private Integer maintenancePeriod;
        private Long warrantyExpirationTime;
        private Integer storeId;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }



        public List<Integer> getChildren() {
            return children;
        }

        public void setChildren(List<Integer> children) {
            this.children = children;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAlias() {
            return alias;
        }

        public void setAlias(String alias) {
            this.alias = alias;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Integer getStationId() {
            return stationId;
        }

        public void setStationId(Integer stationId) {
            this.stationId = stationId;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getLocationCode() {
            return locationCode;
        }

        public void setLocationCode(String locationCode) {
            this.locationCode = locationCode;
        }

        public Long getOnlineTime() {
            return onlineTime;
        }

        public void setOnlineTime(Long onlineTime) {
            this.onlineTime = onlineTime;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getReplacementPeriod() {
            return replacementPeriod;
        }

        public void setReplacementPeriod(Integer replacementPeriod) {
            this.replacementPeriod = replacementPeriod;
        }

        public Integer getMaintenancePeriod() {
            return maintenancePeriod;
        }

        public void setMaintenancePeriod(Integer maintenancePeriod) {
            this.maintenancePeriod = maintenancePeriod;
        }

        public Long getWarrantyExpirationTime() {
            return warrantyExpirationTime;
        }

        public void setWarrantyExpirationTime(Long warrantyExpirationTime) {
            this.warrantyExpirationTime = warrantyExpirationTime;
        }

        public Integer getStoreId() {
            return storeId;
        }

        public void setStoreId(Integer storeId) {
            this.storeId = storeId;
        }
    }