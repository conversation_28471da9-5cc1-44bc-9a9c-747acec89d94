package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 计划工单列表响应模型
 */
public class PlanOrderListResponse {
    @SerializedName("records")
    private List<PlanOrderRecord> records;  // 工单记录列表
    
    @SerializedName("total")
    private int total;  // 总数
    
    @SerializedName("size")
    private int size;  // 每页显示条数
    
    @SerializedName("current")
    private int current;  // 当前页
    
    public static class PlanOrderRecord extends BaseOrderRecord{

    }
    
    public List<PlanOrderRecord> getRecords() {
        return records;
    }
    
    public void setRecords(List<PlanOrderRecord> records) {
        this.records = records;
    }
    
    public int getTotal() {
        return total;
    }
    
    public void setTotal(int total) {
        this.total = total;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
    }
    
    public int getCurrent() {
        return current;
    }
    
    public void setCurrent(int current) {
        this.current = current;
    }
} 