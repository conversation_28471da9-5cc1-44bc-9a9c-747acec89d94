package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 库房信息响应模型
 */
public class StoreInfoResponse {
    @SerializedName("items")
    private List<StoreItem> items;  // 物品条目列表
    
    public static class StoreItem {
        @SerializedName("type")
        private String type;  // 类型
        
        @SerializedName("brand")
        private String brand;  // 品牌
        
        @SerializedName("stock")
        private int stock;  // 库存
        
        @SerializedName("total")
        private int total;  // 总数
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public String getBrand() {
            return brand;
        }
        
        public void setBrand(String brand) {
            this.brand = brand;
        }
        
        public int getStock() {
            return stock;
        }
        
        public void setStock(int stock) {
            this.stock = stock;
        }
        
        public int getTotal() {
            return total;
        }
        
        public void setTotal(int total) {
            this.total = total;
        }
    }
    
    public List<StoreItem> getItems() {
        return items;
    }
    
    public void setItems(List<StoreItem> items) {
        this.items = items;
    }
} 