package com.crrc.common.bean.response;

import java.util.List;
import java.util.ArrayList;

/**
 * 案例库列表响应
 */
public class CaseListResponse {

    /**
     * 查询数据列表
     */
    private List<CaseRecord> records;

    /**
     * 总数
     */
    private int total;

    /**
     * 每页显示条数
     */
    private int size;

    /**
     * 当前页
     */
    private int current;

    /**
     * 排序字段信息
     */
    private List<OrderInfo> orders;

    /**
     * 自动优化 COUNT SQL
     */
    private boolean optimizeCountSql;

    /**
     * 是否进行 count 查询
     */
    private boolean isSearchCount;

    public CaseListResponse() {
        this.records = new ArrayList<>();
        this.orders = new ArrayList<>();
    }

    public List<CaseRecord> getRecords() {
        return records;
    }

    public void setRecords(List<CaseRecord> records) {
        this.records = records;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public List<OrderInfo> getOrders() {
        return orders;
    }

    public void setOrders(List<OrderInfo> orders) {
        this.orders = orders;
    }

    public boolean isOptimizeCountSql() {
        return optimizeCountSql;
    }

    public void setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
    }

    public boolean isSearchCount() {
        return isSearchCount;
    }

    public void setSearchCount(boolean searchCount) {
        isSearchCount = searchCount;
    }

    /**
     * 案例记录
     */
    public static class CaseRecord {
        /**
         * 案例id
         */
        private String id;

        /**
         * 案例名称
         */
        private String title;

        /**
         * 创建时间
         */
        private String createDate;

        public CaseRecord() {
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getCreateDate() {
            return createDate;
        }

        public void setCreateDate(String createDate) {
            this.createDate = createDate;
        }
    }

    /**
     * 排序信息
     */
    public static class OrderInfo {
        /**
         * 需要进行排序的字段
         */
        private String column;

        /**
         * 是否正序排列
         */
        private boolean asc;

        public OrderInfo() {
        }

        public String getColumn() {
            return column;
        }

        public void setColumn(String column) {
            this.column = column;
        }

        public boolean isAsc() {
            return asc;
        }

        public void setAsc(boolean asc) {
            this.asc = asc;
        }
    }
} 