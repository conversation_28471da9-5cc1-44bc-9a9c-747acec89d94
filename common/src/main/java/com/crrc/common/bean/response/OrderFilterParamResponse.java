package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class OrderFilterParamResponse {

    private List<FilterParam> station;  //所属车站
    private List<FilterParam> status; //工单状态

    private List<FilterParam> type;  //所属工单类型

    private List<FilterParam> priority; //优先级

    public static class FilterParam {
        @SerializedName("id")
        private String id;

        @SerializedName("name")
        private String name;

        @SerializedName("selected")
        private boolean selected;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }

    public List<FilterParam> getStation() {
        return station;
    }

    public void setStation(List<FilterParam> station) {
        this.station = station;
    }

    public List<FilterParam> getStatus() {
        return status;
    }

    public void setStatus(List<FilterParam> status) {
        this.status = status;
    }

    public List<FilterParam> getType() {
        return type;
    }

    public void setType(List<FilterParam> type) {
        this.type = type;
    }

    public List<FilterParam> getPriority() {
        return priority;
    }

    public void setPriority(List<FilterParam> priority) {
        this.priority = priority;
    }
}
