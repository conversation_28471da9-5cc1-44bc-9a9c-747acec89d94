package com.crrc.common.bean.response;

import com.google.gson.annotations.SerializedName;

/**
 * 任务总览响应模型
 */
public class TaskOverviewResponse {
    @SerializedName("plan")
    private int planTaskCount;  // 计划工单数量
    
    @SerializedName("fault")
    private int faultTaskCount;  // 故障工单数量
    
    @SerializedName("emergency")
    private int emergencyTaskCount;  // 应急工单数量
    
    @SerializedName("notice")
    private int noticeCount;  // 通知个数

    public int getPlanTaskCount() {
        return planTaskCount;
    }

    public void setPlanTaskCount(int planTaskCount) {
        this.planTaskCount = planTaskCount;
    }

    public int getFaultTaskCount() {
        return faultTaskCount;
    }

    public void setFaultTaskCount(int faultTaskCount) {
        this.faultTaskCount = faultTaskCount;
    }

    public int getEmergencyTaskCount() {
        return emergencyTaskCount;
    }

    public void setEmergencyTaskCount(int emergencyTaskCount) {
        this.emergencyTaskCount = emergencyTaskCount;
    }

    public int getNoticeCount() {
        return noticeCount;
    }

    public void setNoticeCount(int noticeCount) {
        this.noticeCount = noticeCount;
    }
} 