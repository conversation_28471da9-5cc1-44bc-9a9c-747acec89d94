package com.crrc.common;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 */
public class BaseResponse<T> {
@SerializedName(value = "code", alternate = {"errorCode"})
@Expose
    public Integer baseResponseCode;

    @SerializedName("msg")
    @Expose
    public String baseResponseReason;

    public T data;


    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

}
