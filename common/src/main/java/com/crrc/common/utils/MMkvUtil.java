package com.crrc.common.utils;

import android.os.Parcelable;

import androidx.annotation.Nullable;

import com.tencent.mmkv.MMKV;

import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 * Date on 2021-10-08
 * Des:mmkv
 */
public class MMkvUtil {
    private static MMkvUtil mInstance;
    private static MMKV mv;

    private MMkvUtil() {
        mv = MMKV.defaultMMKV();
    }

    /**
     * 初始化MMKV,只需要初始化一次，建议在Application中初始化
     */
    public static MMkvUtil getInstance() {
        if (mInstance == null) {
            synchronized (MMkvUtil.class) {
                if (mInstance == null) {
                    mInstance = new MMkvUtil();
                }
            }
        }
        return mInstance;
    }

    /**
     * 保存数据的方法，我们需要拿到保存数据的具体类型，然后根据类型调用不同的保存方法
     *
     * @param key
     * @param object
     */
    public static void encode(String key, Object object) {
        if (object instanceof String) {
            mv.encode(key, (String) object);
        } else if (object instanceof Integer) {
            mv.encode(key, (Integer) object);
        } else if (object instanceof Boolean) {
            mv.encode(key, (Boolean) object);
        } else if (object instanceof Float) {
            mv.encode(key, (Float) object);
        } else if (object instanceof Long) {
            mv.encode(key, (Long) object);
        } else if (object instanceof Double) {
            mv.encode(key, (Double) object);
        } else if (object instanceof byte[]) {
            mv.encode(key, (byte[]) object);
        } else {
            mv.encode(key, object.toString());
        }
    }

    public static void encodeSet(String key, Set<String> sets) {
        mv.encode(key, sets);
    }



    /**
     * 得到保存数据的方法，我们根据默认值得到保存的数据的具体类型，然后调用相对于的方法获取值
     */
    public static Integer decodeInt(String key) {
        return mv.decodeInt(key, 0);
    }

    public static Double decodeDouble(String key) {
        return mv.decodeDouble(key, 0.00);
    }

    public static Long decodeLong(String key) {
        return mv.decodeLong(key, 0L);
    }

    public static Boolean decodeBoolean(String key) {
        return mv.decodeBool(key, false);
    }

    public static Float decodeFloat(String key) {
        return mv.decodeFloat(key, 0F);
    }

    public static byte[] decodeBytes(String key) {
        return mv.decodeBytes(key);
    }

    public static String decodeString(String key) {
        return mv.decodeString(key, "");
    }

    public static Set<String> decodeStringSet(String key) {
        return mv.decodeStringSet(key, Collections.<String>emptySet());
    }

//    public static <T extends Parcelable> T Parcelable decodeParcelable(String key, Class<T> tClass) {
//        return mv.decodeParcelable(key, aClass.getClass());
//    }

    /**
     * 存
     * @param key key
     * @param obj Parcelable
     * @return Boolean
     */
    public static boolean encodeParcelable(String key, Parcelable obj) {
        return mv.encode(key, obj);
    }
    /**
     * 取
     *
     * @param key    key
     * @param tClass class
     * @param <T>    class
     * @return Boolean
     */
    public static <T extends Parcelable> T decodeParcelables(String key, Class<T> tClass) {
        return mv.decodeParcelable(key, tClass);
    }

    /**
     * 移除某个key对
     *
     * @param key
     */
    public static void removeKey(String key) {
        mv.removeValueForKey(key);
    }

    /**
     * 清除所有key
     */
    public static void clearAll() {
        mv.clearAll();
    }
}
