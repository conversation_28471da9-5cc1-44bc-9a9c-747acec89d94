package com.crrc.common.utils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * Date on 2021-09-28
 * Des:Gson数据转换
 */
public class GsonUtil {
    private static Gson gson = null;

    static {
        if (gson == null) {
            gson = new Gson();
        }
    }

    private GsonUtil() {
    }

    /**
     * 转成json
     *
     * @param object
     * @return
     */
    public static String GsonString(Object object) {
        String gsonString = null;
        if (gson != null) {
            gsonString = gson.toJson(object);
        }
        return gsonString;
    }

    /**
     * 转化为 字符串再转化为数组
     *
     * @param object object
     * @param cls    bean
     * @param <T>    对象名
     * @return List
     */
    public static <T> List<T> GsonToStringToList(Object object, Class<T> cls) {
        String gsonString = null;
        if (gson != null) {
            gsonString = gson.toJson(object);
        }

        return GsonToList(gsonString, cls);
    }

    /**
     * 转成bean
     *
     * @param gsonString
     * @param cls
     * @return
     */
    public static <T> T GsonToBean(String gsonString, Class<T> cls) {
        T t = null;
        if (gson != null) {
            t = gson.fromJson(gsonString, cls);
        }
        return t;
    }

    /**
     * 转成list
     *
     * @param gsonString
     * @param cls
     * @return
     */
    public static <T> List<T> GsonToList(String gsonString, Class<T> cls) {
        Type ListType = TypeToken.getParameterized(ArrayList.class, cls).getType();

        List<T> list = null;
        if (gson != null) {
            list = gson.fromJson(gsonString, ListType);
        }
        return list;
    }

    /**
     * 转成list中有map的
     *
     * @param gsonString
     * @return
     */
    public static <T> List<Map<String, T>> GsonToListMaps(String gsonString) {
        List<Map<String, T>> list = null;
        if (gson != null) {
            list = gson.fromJson(gsonString,
                    new TypeToken<List<Map<String, T>>>() {
                    }.getType());
        }
        return list;
    }


    /**
     * 转成map的
     *
     * @param gsonString
     * @return
     */
    public static <T> Map<String, T> GsonToMaps(String gsonString) {
        Map<String, T> map = null;
        if (gson != null) {
            map = gson.fromJson(gsonString, new TypeToken<Map<String, T>>() {
            }.getType());
        }
        return map;
    }

    /**
     * 对象转json字符串（替换转义字符）
     *
     * @param object 要转换的对象
     * @return json字符串
     */
    public static String GsonStringEscape(Object object) {
        String gsonString = null;
        if (gson != null) {
            gsonString = gson.toJson(object).replaceAll("\\\\u0026", "&");
        }
        return gsonString;
    }
}
