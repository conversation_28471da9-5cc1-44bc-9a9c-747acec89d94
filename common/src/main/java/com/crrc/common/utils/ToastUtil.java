package com.crrc.common.utils;

import android.text.TextUtils;
import android.widget.Toast;


import com.crrc.common.BaseApplication;

import static android.widget.Toast.LENGTH_SHORT;

/**
 * <AUTHOR>
 * Date on 2021-09-28
 * Des:吐司全局使用
 */
public class ToastUtil {

    /**
     * 默认显示短时间
     */
    private static int mToastShowLength = LENGTH_SHORT;

    public static void show(String msg) {
        try {
            if (!TextUtils.isEmpty(msg)) {
                showToast(msg);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void show(String msg, int toastShowLength) {
        try {
            if (!TextUtils.isEmpty(msg)) {
                mToastShowLength = toastShowLength;
                showToast(msg);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void showToast(String msg) {
        Toast mToast = Toast.makeText(BaseApplication.sApplication, msg, mToastShowLength);
        mToast.setText(msg);
        mToast.show();
    }
}
