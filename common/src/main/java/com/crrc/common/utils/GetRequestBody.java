package com.crrc.common.utils;



import static com.crrc.common.Constant.CONTENT_TYPE;

import com.google.gson.Gson;

import java.util.Map;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR>
 * Date on 2021/10/21
 * Des:retrofit post请求添加content_type 并且转化为baby
 */
public class GetRequestBody {
    public static RequestBody requestBody(Object object) {
        RequestBody requestBody = RequestBody.create(MediaType.parse(CONTENT_TYPE), GsonUtil.GsonString(object));
        return requestBody;
    }

    public static RequestBody getBaseRequestBody(Map<String, Object> map) {
        String  json= new Gson().toJson(map);
        return RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
    }
}
