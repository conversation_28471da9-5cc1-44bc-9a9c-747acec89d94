package com.crrc.common.base

import ViewStatus
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.crrc.common.utils.LiveDataUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

abstract class BaseViewModel<MODEL : BaseModel<IBaseModelListener<DATA>, DATA>, DATA> : ViewModel(), LifecycleObserver, IBaseModelListener<DATA> {

    /**
     * 界面数据
     */
    val sLiveDataLists = MutableLiveData<DATA>()

    /**
     * 是否第一页数据
     */
    val isFirstPage = MutableLiveData<Boolean>()

    /**
     * model
     */
    protected var sModel: MODEL? = null

    /**
     * 界面显示状态
     */
    val sLiveDataViewStatus = MutableLiveData<ViewStatus>()

    /**
     * 错误信息
     */
    val sLiveDataErrorMes = MutableLiveData<String>()

    /**
     * 区分model（哪一个请求）
     */
    val sMvvmModelMutableLiveData = MutableLiveData<BaseModel<*, *>>()

    // 添加 Compose 需要的状态
    protected val _isRefreshing = MutableStateFlow(false)
    val isRefreshing = _isRefreshing.asStateFlow()

    /**
     * 第一次加载数据或者下拉刷新数据
     */
    fun refresh() {
        _isRefreshing.value = true
        sLiveDataViewStatus.value = ViewStatus.LOADING
        createAndRegisterModel()
        sModel?.refresh()
    }

    /**
     * 上拉加载更多数据
     */
    fun refreshNextPage() {
        createAndRegisterModel()
        sModel?.refreshNextPageData()
    }

    /**
     * 创建model
     */
    private fun createAndRegisterModel() {
        if (sModel == null) {
            sModel = sCreateModel()
            sModel?.register(this)
        }
    }

    /**
     * 创建model
     */
    abstract fun sCreateModel(): MODEL

    override fun onCleared() {
        super.onCleared()
        sModel?.cancel()
    }

    override fun onLoadSuccess(data: DATA?, vararg pagingResult: PagingResult) {
        LiveDataUtils.postSetValue(sMvvmModelMutableLiveData, sModel)
        if (sModel?.isPaging() == true) {
            if (pagingResult[0].isEmpty) {
                if (pagingResult[0].isFirstPage) {
                    LiveDataUtils.postSetValue(sLiveDataLists, data)
                    LiveDataUtils.postSetValue(sLiveDataViewStatus, ViewStatus.EMPTY)
                } else {
                    LiveDataUtils.postSetValue(sLiveDataViewStatus, ViewStatus.NO_MORE_DATA)
                }
            } else {
                isFirstPage.postValue(pagingResult[0].isFirstPage)
                LiveDataUtils.postSetValue(sLiveDataLists, data)
                LiveDataUtils.postSetValue(sLiveDataViewStatus, ViewStatus.SHOW_CONTENT)
            }
        } else {
            LiveDataUtils.postSetValue(sLiveDataLists, data)
            LiveDataUtils.postSetValue(sLiveDataViewStatus, ViewStatus.SHOW_CONTENT)
        }
        _isRefreshing.value = false
    }

    override fun onLoadFail(errorMessage: String, vararg pagingResult: PagingResult) {
        LiveDataUtils.postSetValue(sMvvmModelMutableLiveData, sModel)
        LiveDataUtils.postSetValue(sLiveDataErrorMes, errorMessage)
        if (sModel?.isPaging() == true && pagingResult[0].isFirstPage.not()) {
            LiveDataUtils.postSetValue(sLiveDataViewStatus, ViewStatus.LOAD_MORE_FAILED)
        } else {
            LiveDataUtils.postSetValue(sLiveDataViewStatus, ViewStatus.REFRESH_ERROR)
        }
        _isRefreshing.value = false
    }
}

sealed class ViewState {
    object Initial : ViewState()
    object Loading : ViewState()
    object Error : ViewState()
    object Success : ViewState()
    object Empty : ViewState()
} 