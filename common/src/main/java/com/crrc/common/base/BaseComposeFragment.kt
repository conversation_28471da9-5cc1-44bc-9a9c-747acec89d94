package com.crrc.common.base

import ViewStatus
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import com.crrc.common.ui.CustomProgressDialog
import com.crrc.common.ui.LoadingDialog

abstract class BaseComposeFragment<VM : BaseViewModel<out BaseModel<IBaseModelListener<DATA>, DATA>, DATA>, DATA> : Fragment() {
    
    protected abstract val viewModel: VM
    private var loadingDialog: CustomProgressDialog? = null
    
    protected var isFirstPage: Boolean = false
    protected var mvvmModelTag: BaseModel<*, *>? = null
    protected var sLoadServiceMvvmModelTag: BaseModel<*, *>? = null
    protected var isLoadOver: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                val items by viewModel.sLiveDataLists.observeAsState()
                val viewStatus by viewModel.sLiveDataViewStatus.observeAsState()
                val errorMessage by viewModel.sLiveDataErrorMes.observeAsState()

                when (viewStatus) {
                    ViewStatus.LOADING -> LoadingContent()
                    ViewStatus.EMPTY -> EmptyContent()
                    ViewStatus.REFRESH_ERROR -> ErrorContent(errorMessage)
                    ViewStatus.CONNECTION_FAILED -> NetworkErrorContent()
                    ViewStatus.SHOW_CONTENT -> Content(items)
                    else -> Unit
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViewModel()
    }

    private fun initViewModel() {
        viewModel.sLiveDataLists.observe(viewLifecycleOwner) { data -> onNetworkResponded(data) }
        viewModel.isFirstPage.observe(viewLifecycleOwner) { isFirst -> isFirstPage = isFirst }
        viewModel.sMvvmModelMutableLiveData.observe(viewLifecycleOwner) { model -> mvvmModelTag = model }
        viewModel.sLiveDataViewStatus.observe(viewLifecycleOwner) { status -> handleViewStatus(status) }
        viewModel.sLiveDataErrorMes.observe(viewLifecycleOwner) { /* 错误信息会在 UI 中显示 */ }
    }

    private fun handleViewStatus(status: ViewStatus) {
        when (status) {
            ViewStatus.LOADING -> showLoading()
            ViewStatus.EMPTY -> hideLoading()
            ViewStatus.REFRESH_ERROR -> hideLoading()
            ViewStatus.LOAD_MORE_FAILED -> hideLoading()
            ViewStatus.CONNECTION_FAILED -> hideLoading()
            else -> hideLoading()
        }
    }

    private fun showLoading() {
        if (loadingDialog == null) {
            loadingDialog = CustomProgressDialog(requireContext())
        }
        loadingDialog?.show()
    }

    private fun hideLoading() {
        loadingDialog?.dismiss()
    }

    @Composable
    protected open fun LoadingContent() {
        LoadingDialog()
    }

    @Composable
    protected open fun EmptyContent() {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("暂无数据")
        }
    }

    @Composable
    protected open fun ErrorContent(message: String?) {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text(message ?: "加载失败")
        }
    }

    @Composable
    protected open fun NetworkErrorContent() {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("网络连接失败")
        }
    }

    @Composable
    protected abstract fun Content(data: DATA?)

    protected abstract fun onNetworkResponded(data: DATA?)
} 