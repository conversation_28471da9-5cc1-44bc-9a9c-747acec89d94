package com.crrc.common.base

import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import java.lang.ref.WeakReference

abstract class BaseModel<LISTENER : IBaseModelListener<RESULT_DATA>, RESULT_DATA> {

    private var sIBaseModelListenerWeakReference: WeakReference<LISTENER>? = null
    private var sIsLoading = false
    protected var sIsPading = false
    protected var sPage = INIT_PAGE_NUMBER
    protected var sPageSize = INIT_PAGE_SIZE

    private val compositeDisposable = CompositeDisposable()

    /**
     * 注册监听
     */
    fun register(listener: LISTENER) {
        sIBaseModelListenerWeakReference = WeakReference(listener)
    }

    /**
     * 取消注册
     */
    fun cancel() {
        sIBaseModelListenerWeakReference?.clear()
        compositeDisposable.clear()
    }

    /**
     * 添加 RxJava 订阅
     */
    fun addDisposable(disposable: Disposable) {
        compositeDisposable.add(disposable)
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        if (!sIsLoading) {
            if (sIsPading) {
                sPage = INIT_PAGE_NUMBER
                sPageSize = INIT_PAGE_SIZE
            }
            sIsLoading = true
            loadModelData()
        }
    }

    /**
     * 加载下一页
     */
    fun refreshNextPageData() {
        if (!sIsLoading) {
            sIsLoading = true
            loadModelData()
        }
    }

    /**
     * 加载数据
     */
    protected open fun loadModelData() {
        // 子类实现具体加载逻辑
    }

    /**
     * 通知加载成功
     */
    protected fun notifyResultSuccessToListener(
        resultData: RESULT_DATA?,
        vararg pagingResult: PagingResult
    ) {
        sIBaseModelListenerWeakReference?.get()?.let { listener ->
            if (sIsPading) {
                listener.onLoadSuccess(
                    resultData,
                    PagingResult(
                        isFirstPage = sPage == INIT_PAGE_NUMBER,
                        isEmpty = pagingResult[0].isEmpty,
                        haveNext = pagingResult[0].haveNext
                    )
                )

                if (resultData != null && !pagingResult[0].isEmpty) {
                    sPage++
                }
            } else {
                listener.onLoadSuccess(resultData)
            }
            sIsLoading = false
        } ?: throw NullPointerException("sIBaseModelListenerWeakReference 为空")
    }

    /**
     * 通知加载失败
     */
    protected fun notifyResultFailureToListener(
        errorMessage: String,
        vararg pagingResult: PagingResult
    ) {
        sIBaseModelListenerWeakReference?.get()?.let { listener ->
            if (sIsPading) {
                listener.onLoadFail(
                    errorMessage,
                    PagingResult(
                        isFirstPage = sPage == INIT_PAGE_NUMBER,
                        isEmpty = true,
                        haveNext = false
                    )
                )
            } else {
                listener.onLoadFail(errorMessage)
            }
            sIsLoading = false
        } ?: throw NullPointerException("sIBaseModelListenerWeakReference 为空")
    }

    fun isPaging(): Boolean = sIsPading

    companion object {
        const val INIT_PAGE_NUMBER = 1
        const val INIT_PAGE_SIZE = 20
    }
}

interface IBaseModelListener<DATA> {
    fun onLoadSuccess(data: DATA?, vararg pagingResult: PagingResult)
    fun onLoadFail(errorMessage: String, vararg pagingResult: PagingResult)
}

data class PagingResult(
    val isFirstPage: Boolean,
    val isEmpty: Boolean,
    val haveNext: Boolean
) 