package com.crrc.common.base

import ViewStatus
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.runtime.livedata.observeAsState
import com.crrc.common.ui.CustomProgressDialog
import com.crrc.common.ui.LoadingDialog

abstract class BaseComposeActivity<VM : BaseViewModel<out BaseModel<IBaseModelListener<DATA>, DATA>, DATA>, DATA> : ComponentActivity() {
    protected abstract val viewModel: VM
    private var loadingDialog: CustomProgressDialog? = null

    /**
     * 是否为第一页数据
     */
    protected var isFirstPage: Boolean = false

    /**
     * 请求标记,多个请求界面使用model来区别是哪个接口
     */
    protected var mvvmModelTag: BaseModel<*, *>? = null

    /**
     * 某一个界面可能存在多个请求，在返回数据的时候可能在存在LoadService已经刷新界面的情况下
     * 重新给LoadService赋值，直接设置一个只能可以给LoadService赋值的model
     * 其他model不能给LoadService刷新界面
     */
    protected var sLoadServiceMvvmModelTag: BaseModel<*, *>? = null

    /**
     * 分页情况数据是否加载完毕
     */
    protected var isLoadOver: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initViewModel()
        setContent {
            val items by viewModel.sLiveDataLists.observeAsState()
            val viewStatus by viewModel.sLiveDataViewStatus.observeAsState()
            val errorMessage by viewModel.sLiveDataErrorMes.observeAsState()

            when (viewStatus) {
                ViewStatus.LOADING -> LoadingContent()
                ViewStatus.EMPTY -> EmptyContent()
                ViewStatus.REFRESH_ERROR -> ErrorContent(errorMessage)
                ViewStatus.CONNECTION_FAILED -> NetworkErrorContent()
                ViewStatus.SHOW_CONTENT -> Content(items)
                else -> Unit
            }
        }
    }

    private fun initViewModel() {
        viewModel.sLiveDataLists.observe(this) { data -> onNetworkResponded(data) }
        viewModel.isFirstPage.observe(this) { isFirst -> isFirstPage = isFirst }
        viewModel.sMvvmModelMutableLiveData.observe(this) { model -> mvvmModelTag = model }
        viewModel.sLiveDataViewStatus.observe(this) { status -> handleViewStatus(status) }
        viewModel.sLiveDataErrorMes.observe(this) { /* 错误信息会在 UI 中显示 */ }
    }

    private fun handleViewStatus(status: ViewStatus) {
        when (status) {
            ViewStatus.LOADING -> showLoading()
            ViewStatus.EMPTY -> hideLoading()
            ViewStatus.REFRESH_ERROR -> hideLoading()
            ViewStatus.LOAD_MORE_FAILED -> hideLoading()
            ViewStatus.CONNECTION_FAILED -> hideLoading()
            else -> hideLoading()
        }
    }

    private fun showLoading() {
        if (loadingDialog == null) {
            loadingDialog = CustomProgressDialog(this)
        }
        loadingDialog?.show()
    }

    private fun hideLoading() {
        loadingDialog?.dismiss()
    }

    @Composable
    protected open fun LoadingContent() {
        LoadingDialog()
    }

    @Composable
    protected open fun EmptyContent() {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("暂无数据")
        }
    }

    @Composable
    protected open fun ErrorContent(message: String?) {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text(message ?: "加载失败")
        }
    }

    @Composable
    protected open fun NetworkErrorContent() {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text("网络连接失败")
        }
    }

    @Composable
    protected abstract fun Content(data: DATA?)

    protected abstract fun onNetworkResponded(data: DATA?)
} 