package com.crrc.common;

import com.crrc.common.utils.PrefsUtil;

/**
 * <AUTHOR>
 * Date on 2021/10/12
 * Des:全局常量
 */
public class Constant {

    /**
     * siom
     */
    public static final String QR_FLAG = "0";
    public static final String ID_FLAG = "1";

    public static final int USER_TYPE = 0;

    public static final int GROUP_TYPE = 1;
    public static final int EXPERT_GROUP_TYPE = 2;


    /**
     * 现场环境
     */
//    public static final String BaseUrl = "http://192.168.50.10:7777";
//    public static final String RULE = "";
//    public static final String USERNAME = "";
//    public static final String PASS_WORDED = "";
//    public static final String WEB_SOCKET_URL = "ws://192.168.50.10:7777/im/websocket/";

    /**
     * 现场测试环境
     */
//    public static final String BaseUrl = "http://192.168.50.11:12041";
//    public static final String RULE = "";
//    public static final String USERNAME = "";
//    public static final String PASS_WORDED = "";
//    public static final String WEB_SOCKET_URL = "ws://192.168.50.11:12041/im/websocket/";

    /**
     * 阿里云开发环境
     */
//    public static final String BaseUrl = "http://47.104.169.50:7777";
//    public static final String RULE = "";
//    public static final String USERNAME = "002418";
//    public static final String PASS_WORDED = "Aa123456";
//    public static final String WEB_SOCKET_URL = "ws://47.104.169.50:7777/im/websocket/";

    /**
     * 123测试环境
     */
//    public static final String BaseUrl = "http://123.57.18.249:7777";
//    public static  String BaseUrl = "http://192.168.8.54:89";
    public static  String BaseUrl = PrefsUtil.getInstance(BaseApplication.sApplication).getBaseUrl();


    public static final String RULE =  "";//"mock/41c8cebf0ce0000";
    public static final String MOBILE_TERMINAL = "mobileTerminal";//"mock/41c8cebf0ce0000"; // "mobileTerminal";

//    public static final String RULE = "/prod-api/";

    public static final String USERNAME = "00001";
    public static final String PASS_WORDED = "Aa123456";
//    public static final String WEB_SOCKET_URL = "ws://123.57.18.249:7777/im/websocket/";
    public static final String WEB_SOCKET_URL = "ws://192.168.8.54:7777/im/websocket/";

    /**
     * 内网开发环境
     */
/*    public static final String BaseUrl = "http://172.30.121.195:7777";
    public static final String RULE = "";
    public static final String USERNAME = "16200000001";
    public static final String PASS_WORDED = "Imos123456";*/

    /**
     * 解析错误
     */
    public static String PARSE_ERROR = "解析错误";
    /**
     * 连接失败
     */
    public static String CONNECTION_FAILED = "连接失败";
    /**
     * 证书验证失败
     */
    public static String CERTIFICATE_VERIFICATION_FAILED = "证书验证失败";
    /**
     * 连接超时
     */
    public static String CONNECTION_TIMED_OUT = "连接超时";
    /**
     * 连接超时
     */
    public static String UNKNOWN_MISTAKE = "未知错误";

    /**
     * 消息待办——预警
     */
    public static String EARLY_WARNING = "1";

    /**
     * 消息待办——待办
     */
    public static String BACK_LOG = "2";

    /**
     * 消息待办——提示
     */
    public static String HINT = "3";

    /**
     * 消息待办——公告
     */
    public static String ANNOUNCEMENT = "4";
    /**
     * 消息 - 提醒
     */
    public static String REMIND = "5";
    /**
     * 待办-计划工单
     */
    public static String BACK_LOG_MODULE_PLAN = "plannedWorkOrder";
    /**
     * 待办-专项工单
     */
    public static String BACK_LOG_MODULE_SPECIAL = "specialWorkOrder";
    /**
     * 待办-故障工单工单
     */
    public static String BACK_LOG_MODULE_FAULT = "fault";

    /**
     * 消息等级 '较低'
     */
    public static String MESSAGE_LEVEL_LOWER = "0";

    /**
     * 消息等级 '一般'
     */
    public static String MESSAGE_LEVEL_GENERALLY = "1";

    /**
     * 消息等级 '紧急'
     */
    public static String MESSAGE_LEVEL_URGENT = "2";

    /**
     * 消息0未读
     */
    public static String READ_FLAG = "0";
    /**
     * 消息已读
     */
    public static String READ_FLAG_ED = "1";

    /**
     * 工单回传 资源回传间隔时间
     */
    public static final int SERVICE_INTERVAL = 120_000;
    /**
     * 列表按钮位移值 回填（执行）
     */
    public static final int IMPLEMENT_BTN = 1;
    /**
     * 列表按钮位移值 回退
     */
    public static final int GO_BACK_BTN = 2;
    /**
     * 列表按钮位移值 挂起
     */
    public static final int HANG_BTN = 3;
    /**
     * 列表按钮位移值 领取
     */
    public static final int RECEIVE_BTN = 4;
    /**
     * 列表按钮位移值 指派
     */
    public static final int ASSIGN_BTN = 5;
    /**
     * 列表按钮位移值 已完成
     */
    public static final int COMPLETED_BTN = 6;
    /**
     * 列表按钮位移值 审核
     */
    public static final int AUDIT_BTN = 7;
    /**
     * 列表按鈕位移值 转单
     */
    public static final int CHANGE_ORDER_BTN = 8;
    /**
     * 消息提醒 位移值
     */
    public static final int MESSAGE_REMIND_BIN=0;

    /**
     * 服务器请求成功码  siom
     */
    public static final int RESPONSE_CODE = 100;

    /**
     * 99
     */
    public static final int NINETY_NINE = 99;

    /**
     * NULL
     */
    public static final String NULL = "null";
    /**
     * 判断登录标识
     */
    public static final String LOGIN_TAG = "user/login";

    /**
     * content_type  post请求
     */
    public static final String CONTENT_TYPE = "application/json;charset=UTF-8";
    /**
     * token AUTHORIZATION
     */
    public static final String AUTHORIZATION = "Authorization";
    /**
     * token AUTHORIZATION
     */
    public static final String BEARER = "Bearer ";
    /**
     * multipart_type
     */
    public static final String MULTIPART_TYPE = "multipart/form-data";
    /**
     * multipart_type
     */
    public static final String IMAGE = "image";
    /**
     * multipart_type
     */
    public static final String VIDEO = "video";

    /**
     * multipart_type
     */
    public static final String MULTIPART_TYPE_IMG = "image/jpeg";
    /**
     * multipart_type
     */
    public static final String MULTIPART_TYPE_VIDEO = "video/mp4";

    /**
     * 线路名称
     */
    public static final String LINE_NAME = "19";
    /**
     * 链接符号
     */
    public static final String LINK = "-";

    /**
     * id
     */
    public static final String ID = "id";

    /**
     * Response拦截器标识
     */
    public static final String RESPONSE_INTERCEPTOR_TAG = "ResponseInterceptor";
    /**
     * MMkV 登录存储登录数据key
     */
    public final static String LOGIN_INFO = "login_info";
    /**
     * MMkV 登录存储用户数据key
     */
    public final static String USER_INFO = "user_info";

    /**
     * MMkV 用户角色
     */
    public final static String USER_ROLE = "user_role";
    /**
     * MMkV 登录存储用户数据名
     */
    public final static String USER_NAME = "user_name";
    /**
     * MMkV 登录存储用户数据密码
     */
    public final static String USER_PASSWORD = "user_password";
    /**
     * MMkV 登录存储用户角色信息
     */
    public final static String AUTHORITIES_INFO = "authorities_info";

    /**
     * MMkV 登录存储TOKEN数据key
     */
    public final static String TOKEN_INFO = "token_info";
    /**
     * MMkV 登录存储班组数据key
     */
    public final static String TEAM_INFO = "team_info";
    /**
     * MMkV 登录存储线路数据key
     */
    public final static String LINE_INFO = "line_info";
    /**
     * MMkV 登录存储场段数据key
     */
    public final static String DEPOT_INFO = "depot_info";
    /**
     * MMkV 登录存储组织数据key
     */
    public final static String ORG_INFO = "org_info";
    /**
     * MMkV 登录存储专业数据key
     */
    public final static String PROFESSION_INFO = "profession_info";
    /**
     * MMkV 存储部门数据key
     */
    public final static String DEPT_INFO = "dept_info";
    /**
     * 车辆维修专业code
     */
    public final static String ELECTRIC_BUS = "ELECTRIC_BUS";
    /**
     * MMkV 登录存储检修类型key
     */
    public final static String REPAIR_TYPE_INFO = "repair_type_info";
    /**
     * 故障工单状态 草稿状态
     */
    public final static int FAULT_ORDER_DRAFT = 1;

    /**
     * 故障工单状态 待领取
     */
    public final static int FAULT_ORDER_PENDING = 2;

    /**
     * 故障工单状态 待回填
     */
    public final static int FAULT_ORDER_BACK_FILLED = 3;

    /**
     * 故障工单状态 待审核
     */
    public final static int FAULT_ORDER_REVIEW = 4;

    /**
     * 故障工单状态 已完成
     */
    public final static int FAULT_ORDER_COMPLETED = 5;
    /**
     * 故障工单状态 观察中
     */
    public final static int FAULT_OBSERVE = 9;

    /**
     * 故障工单状态 挂起中
     */
    public final static int FAULT_ORDER_HANG = 6;

    /**
     * 故障工单状态 驳回
     */
    public final static int FAULT_ORDER_TURN_DOWN = 7;

    /**
     * 工单提交审核状态 通过
     */
    public final static int OPERATION_TYPE_PASS_THROUGH = 1;

    /**
     * 工单提交审核状态 驳回
     */
    public final static int OPERATION_TYPE_TURN_DOWN = 2;
    /**
     * intent 传递 车体结构
     */
    public final static String CARRIAGE_TYPE = "carriageType";
    /**
     * 车体结构
     */
    public final static String TC1 = "TC1";
    /**
     * 车体结构
     */
    public final static String MP1 = "Mp1";
    /**
     * 车体结构
     */
    public final static String M1 = "M1";
    /**
     * 车体结构
     */
    public final static String MP2 = "Mp2";
    /**
     * 车体结构
     */
    public final static String M2 = "M2";
    /**
     * 车体结构
     */
    public final static String MP3 = "Mp3";
    /**
     * 车体结构
     */
    public final static String M3 = "M3";
    /**
     * 车体结构
     */
    public final static String TC2 = "Tc2";
    /**
     * 计划工单 菜单名
     */

    public final static String MAINTENANCE_ORDER_NAME = "maintenance_order";
    /**
     * 故障工单 菜单名
     */
    public final static String FAULT_REPAIR_ORDER_NAME = "fault_repair_order";

    /**
     * 质检问题 菜单名
     */
    public final static String FAULT_QUALITY_PROBLEM_MENU = "fault_quality_problem";
    /**
     * 质检工单 菜单名
     */
    public final static String QUALITY_ORDER = "quality_order";

    public final static String QUALITY_ORDER_NAME = "质检工单";
    /**
     * 消息提醒菜单名
     */
    public final static String MESSAGE_REMIND = "message_remind";
    /**
     * 故障工单 待审核状态下 提报审核
     */
    public final static String ACTIVITY_07958ZG = "Activity_07958zg";


    /**
     * 数据字典 专业类型 责任专业
     */
    public final static String PROFESSION_TYPE = "PROFESSION_TYPE";

    /**
     * 数据字典 故障等级
     */
    public final static String FAULT_LEVEL = "FAULT_LEVEL";
    /**
     * 数据字典 计量单位
     */
    public final static String UNIT_OF_MEASURE = "UNIT_OF_MEASURE";
    /**
     * 数据字典 位置类型
     */
    public final static String LOCATION_TYPE = "LOCATION_TYPE";

    /**
     * 数据字典 提报类型
     */
    public final static String FAULT_ORDER_DATA_TYPE = "FAULT_ORDER_DATA_TYPE";

    /**
     * 数据字典 文件类型
     */
    public final static String FILE_TYPE = "FILE_TYPE";

    /**
     * 数据字典 故障现象
     */
    public final static String FAULT_APPEARANCE = "FAULT_APPEARANCE";


    /**
     * 数据字典 班组类型
     */
    public final static String TEAM_TYPE = "teamType";

    /**
     * 数据字典 处理情况
     */
    public final static String CHU_LI_TYPE = "CHULI_TYPE";

    /**
     * 数据字典 打卡地址
     */
    public final static String SIGN_PLACE = "Signplace   ";

    /**
     * 菜单编码
     */
    public final static String REPAIR_TYPE = "REPAIR_TYPE";
    /**
     * 数据字典 检修类型
     */
    public final static String ROUTER_CODE = "router_code";
    /**
     * 数据字典 问题级别
     */
    public final static String PROBLEM_LEVEL = "PROBLEM_LEVEL";
    /**
     * 标题名称
     */
    public final static String TITLE = "title";

    /**
     * 时间格式
     */
    public final static String YMD_HMS_TIME = "yyyy-MM-dd HH:mm:ss";
    /**
     * 时间格式
     */
    public final static String YMD_TIME = "yyyy-MM-dd";
    /**
     * 时间格式
     */
    public final static String YMD_SHORT = "yyyy/M/d";
    /**
     * 时间格式
     */
    public final static String YMD_HM = "yyyy-MM-dd HH:mm";
    /**
     * 时间格式
     */
    public final static String YMD_TIME_S = "yyyyMMdd";
    /**
     * intent 工单数据 key
     */
    public final static String RECORDS_DTO = "recordsDTO";
    /**
     * intent 欠见欠修进入时带入的数据
     */
    public final static String RECORDS_LIST_DTO = "recordsListDTO";
    /**
     * intent 是否待办
     */
    public final static String IS_BACK_LOG = "isBackLog";
    /**
     * intent 是否重新提报进入
     */
    public final static String IS_MODIFY = "isModify";
    /**
     * intent 是否闭环
     *
     */
    public final static String IS_Close = "isClose";

    /**
     * item POSITION 用于intent传输 key
     */
    public final static String POSITION = "position";
    /**
     * 消息类别 intent传输
     */
    public final static String CATEGORY = "category";
    /**
     * 计划工单父工单id
     */
    public final static String FATHER_RECORD_ID = "fatherRecordId";

    /**
     * 00:00:00时分秒
     */
    public final static String ZERO_MINUTES_AND_SECONDS = " 00:00:00";
    /**
     * 审核类型 传递intent
     */
    public final static String OPERATION_TYPE = "operationType";
    /**
     * 0
     */
    public final static String ZERO = "0";
    /**
     * 1
     */
    public final static String ONE = "1";

    /**
     * 计划子工单待领取
     */
    public final static String WORK_ORDER_PENDING = "0";
    /**
     * 计划子工单待回填
     */
    public final static String WORK_ORDER_TO_BE_BACK_FILLED = "1";
    /**
     * 计划子工单挂起
     */
    public final static String WORK_ORDER_HANG = "2";
    /**
     * 计划子工单待互检
     */
    public final static String WORK_ORDER_PENDING_MUTUAL_INSPECTION = "3";
    /**
     * 计划子工单待审核
     */
    public final static String WORK_ORDER_PENDING_PENDING_REVIEW = "4";
    /**
     * 计划子工单驳回
     */
    public final static String WORK_ORDER_TURN_DOWN = "5";
    /**
     * 计划子工单已完成
     */
    public final static String WORK_ORDER_COMPLETED = "6";
    /**
     * 计划工单父工单状态 待检修
     */
    public final static String FATHER_ORDER_TOBE_OVERHAULED = "0";
    /**
     * 计划工单父工单状态 检修中
     */
    public final static String FATHER_ORDER_UNDER_REPAIR = "1";
    /**
     * 计划工单父工单状态 已完成
     */
    public final static String FATHER_ORDER_COMPLETED = "2";


    /**
     * 质检 -已完成
     */
    public final static String QUALITY_ORDER_COMPLETED = "3";
    /**
     * 子计划工单主检
     */
    public final static String MAIN_INSPECTION = "1";

    /**
     * 子计划工单互检
     */
    public final static String MUTUAL_INSPECTION = "2";

    /**
     * CARRIAGE_ID
     */
    public final static String CARRIAGE_ID = "carriageId";
    /**
     * 消耗品 -intent key
     */
    public final static String MATERIAL_LIST_DTO = "material_list_dto";

    /**
     * 计划工单详情 操作类型三选 正常异常选择
     */
    public final static String OPERATE_TYPE_1 = "1";

    /**
     * 计划工单详情 文本
     */
    public final static String OPERATE_TYPE_2 = "2";

    /**
     * 计划工单详情 数字文本
     */
    public final static String OPERATE_TYPE_3 = "3";

    /**
     * 计划工单详情 下拉框
     */
    public final static String OPERATE_TYPE_4 = "4";

    /**
     * 计划工单回填 不是必填
     */
    public final static String NOT_REQUIRED = "0";

    /**
     * 计划工单回填 必填
     */
    public final static String REQUIRED = "1";

    /**
     * 计划工单回填 异常项 只针对三选框和数字文本框
     */
    public final static String EXCEPTION = "0";

    /**
     * 计划工单回填 正常项 只针对三选框和数字文本框
     */
    public final static String NOT_EXCEPTION = "1";
    /**
     * 故障
     */
    public final static String FAULT = "fault";
    /**
     * 检修
     */
    public final static String MAINTENANCE = "maintenance";
    /**
     * 物资
     */
    public final static String SUPPLIES = "supplies";

    /**
     * DCC
     */
    public final static String DCC = "dcc";
    /**
     * 其他
     */
    public final static String OTHER = "other";

    /**
     * 培训
     */
    public final static String TRAIN = "train";

    /**
     * 专项
     */
    public final static String SPECIAL = "special";
    /**
     * 质检
     */
    public final static String QUALITY = "quality";
    /**
     * 检修-早出库检
     */
    public final static String MAINTENANCE_OUTBOUND = "早出库检";
    /**
     * 检修-晚出库检
     */
    public final static String MAINTENANCE_NIGHT_OUTBOUND = "晚出库检";
    /**
     * 检修-A检
     */
    public final static String MAINTENANCE_A = "A检";

    /**
     * 检修-B检
     */
    public final static String MAINTENANCE_B = "B检";
    /**
     * 检修-K检
     */
    public final static String MAINTENANCE_K = "K检";
    /**
     * 检修-列检
     */
    public final static String MAINTENANCE_COLUMN = "列检";
    /**
     * 检修-均衡修
     */
    public final static String MAINTENANCE_EQUILIBRIUM = "均衡修";
    /**
     * 检修-洗车
     */
    public final static String MAINTENANCE_CAR_WASHER = "洗车";
    /**
     * 检修-空调双周检
     */
    public final static String AIR_CONDITIONING_DOUBLE_WEEKLY_INSPECTION = "空调双周检";
    /**
     * 检修-早入库检
     */
    public final static String EARLY_INCOMING_INSPECTION = "早入库检";
    /**
     * 检修-晚入库检
     */
    public final static String EVENING_INCOMING_INSPECTION = "晚入库检";
    /**
     * 检修-倒班列检
     */
    public final static String SHIFT_WORK_COLUMN = "倒班列检";
    /**
     * 检修-常白列检
     */
    public final static String OFTEN_THE_WHITE_COLUMN = "常白列检";
    /**
     * 检修-月修
     */
    public final static String IN_REPAIRING = "月修";
    /**
     * 检修-架修
     */
    public final static String MAINTENANCE_FRAME = "架修";
    /**
     * 检修-大修
     */
    public final static String MAINTENANCE_BIG = "大修";
    /**
     * 检修-客室门检
     */
    public final static String MAINTENANCE_GUEST_ROOM_DOOR = "客室门检";
    /**
     * 检修-月检
     */
    public final static String MAINTENANCE_MONTHLY_INSPECTION = "月检";
    /**
     * 检修-季检
     */
    public final static String MAINTENANCE_QUARTERLY_INSPECTION = "季检";
    /**
     * 检修-半年检
     */
    public final static String MAINTENANCE_HALF_A_YEAR_INSPECTION = "半年检";
    /**
     * 检修-年检
     */
    public final static String MAINTENANCE_YEAR_INSPECTION = "年检";
    /**
     * 轨道车出库检
     */
    public final static String OUT_OF_STOCK_G = "出库检(轨)";
    /**
     * 轨道车入库检
     */
    public final static String INCOMING_INSPECTION_G = "入库检(轨)";
    /**
     * 轨道车月检
     */
    public final static String MAINTENANCE_MONTHLY_INSPECTION_G = "月检(轨)";
    /**
     * 轨道车双月检
     */
    public final static String DOUBLE_MORNING_OUT_OF_STOCK_G = "双月(轨)";
    /**
     * 轨道车年检
     */
    public final static String YEAR_OUT_OF_STOCK_G = "年检(轨)";
    /**
     * 轨道车半年检
     */
    public final static String HALF_YEAR_OUT_OF_STOCK_G = "半年检(轨)";
    /**
     * 检修类型：月修 月修的逻辑与其他检修类型有出入，这里将月修检修编码写死，检修类型一般不会更改，如果数据库更改app需要更新版本来处理
     */
    public final static String BALANCE_REPAIR = "BALANCE_REPAIR";
    /**
     * 段场设备专业——月检
     */
    public final static String FIELD_EQUIPMENT_MONTHLY_INSPECTION = "月检(段)";
    /**
     * 段场设备专业——季检
     */
    public final static String FIELD_EQUIPMENT_QUARTERLY_INSPECTION = "季检(段)";
    /**
     * 段场设备专业——半年检
     */
    public final static String FIELD_EQUIPMENT_HALF_A_YEAR_INSPECTION = "半年检(段)";
    /**
     * 段场设备专业——年检
     */
    public final static String FIELD_EQUIPMENT_YEAR_INSPECTION = "年检(段)";
    /**
     * 段场设备专业——双月检
     */
    public final static String FIELD_EQUIPMENT_MORNING_OUT_OF_STOCK_G_INSPECTION = "双月(段)";
    /**
     * 新增工单
     */
    public final static String MAINTENANCE_ORDER_ADD = "新增工单";
    /**
     * 欠检欠修
     */
    public final static String LACK_INSPECT_REPAIR_CODE = "maintenance_lack_inspect_repair";
    public final static String LACK_INSPECT_REPAIR_NAME = "欠检欠修";
    /**
     * 故障-故障工单
     */
    public final static String FAULT_REPAIR_ORDER = "故障工单";
    /**
     * 故障-快速提报
     */
    public final static String FAULT_RAPID_REPORT = "快速提报";
    /**
     * 故障-故障知识库
     */
    public final static String FAULT_KNOWLEDGE_BASE = "故障知识库";
    /**
     * 故障-质检问题
     */
    public final static String FAULT_QUALITY_PROBLEM = "质检问题";
    /**
     * 故障-转单中
     */
    public final static String CHANGE_ORDERING = "1";
    /**
     * 故障-转单完成
     */
    public final static String CHANGE_ORDER_SUCCESS = "2";
    /**
     * 物资-物资入库
     */
    public static final String MATERIAL_WAREHOUSING = "物资入库";
    /**
     * 物资-物资盘点
     */
    public static final String MATERIAL_PANDIAN = "物资盘点";
    /**
     * 物资-物资出库
     */
    public static final String MATERIAL_WAREHOUSE = "物资出库";
    /**
     * 物资-物资申请
     */
    public final static String MATERIAL_APPLY = "物资申请";
    /**
     * 物资-物资借还
     */
    public final static String MATERIAL_BORROW = "物资借还";
    /**
     * 物资-物资报废
     */
    public final static String MATERIAL_SCRAP = "物资报废";
    /**
     * 物资-库存信息
     */
    public final static String MATERIAL_INVENTORY_INFORMATION = "库存信息";
    /**
     * DCC-登车工单
     */
    public static final String DCC_BOARD = "登车工单";
    /**
     * DCC-断电工单
     */
    public static final String DCC_OUTAGE = "断电工单";
    /**
     * DCC-送电工单
     */
    public static final String DCC_GIVE = "送电工单";
    /**
     * DCC-试车
     */
    public static final String DCC_TEST_RUN = "试车工单";
    /**
     * DCC-调车
     */
    public static final String DCC_SHUNTING = "调车工单";
    /**
     * DCC-洗车工单
     */
    public static final String DCC_CAR_WASH = "洗车工单";
    /**
     * DCC-车内保洁
     */
    public static final String DCC_INTERIOR_CLEANING = "车内保洁";

    /**
     * DCC-送电申请
     */
    public static final String DCC_GIVE_APPLY = "送电申请";
    /**
     * DCC-断电申请
     */
    public static final String DCC_POWER_OFF = "断电申请";
    /**
     * 培训-培训管理
     */
    public static final String TRAIN_MANAGER = "培训管理";
    /**
     * 其他-设备信息
     */
    public static final String  EQUIPMENT_INFO="设备信息";

    /**
     * 其他-文件管理
     */
    public static final String  FILE_MANAGER="文件管理";
    /**
     * 质检-常白列检质检
     *
     */
    public static final String QUALITY_TRAIN_REPAIR="常白列检质检";

    /**
     * 质检-均衡修质检
     *
     */
    public static final String QUALITY_EQUILIBRIUM="均衡修质检";

    /**
     * 质检-架修
     */
    public final static String QUALITY_FRAME = "架修质检";
    /**
     * 质检-大修
     */
    public final static String QUALITY_BIG = "大修质检";


    /**
     * 作业单元添加附件 rv item type 添加数据
     */
    public static final int SEL_RECOURSE_TYPE_IMAGE = 0;
    /**
     * 作业单元添加附件 rv item type 显示添加之后的数据
     */
    public static final int SEL_ED_RECOURSE_TYPE_IMAGE = 1;
    /**
     * 文件上传类型 图片
     */
    public static final String FILE_TYPE_IMAGE = "image";
    /**
     * 文件上传类型 视频
     */
    public static final String FILE_TYPE_VIDEO = "video";
    /**
     * 文件上传类型 音频
     */
    public static final String FILE_TYPE_AUDIO = "audio";

    /**
     * intent 传递类型-作业单元
     */
    public static final String VAR_LIST_BEAN = "var_list_bean";
    /**
     * 文件归属   1作业包
     */
    public static final String FILE_ATTRIBUTION_TYPE_WORK_PACKAGE = "1";
    /**
     * 文件归属   2工单
     */
    public static final String FILE_ATTRIBUTION_TYPE_WORK_ORDER = "2";
    /**
     * 文件归属   质检作业包
     */
    public static final String FILE_QUALITY_TYPE_WORK_PACKAGE = "QUA1";

    /**
     * 文件归属   质检工单
     */
    public static final String FILE_ORDER_TYPE_WORK_PACKAGE = "QUA2";
    /**
     * 文件归属   故障工单提报
     */
    public static final String FILE_ATTRIBUTION_TYPE_FAULT_SUBMIT = "3";
    /**
     * 文件归属   故障工单回填
     */
    public static final String FILE_ATTRIBUTION_TYPE_FAULT_BACK = "4";

    /**
     * 文件归属   欠检欠修提报
     */
    public static final String FILE_ATTRIBUTION_TYPE_LACK_INSPECT_REPAIR_SUBMIT = "LACK_INSPECT_REPAIR_SUBMIT";
    /**
     * 文件归属   欠检欠修修改
     */
    public static final String FILE_ATTRIBUTION_TYPE_LACK_INSPECT_REPAIR_MODIFY = "LACK_INSPECT_REPAIR_MODIFY";




    /**
     *
     */
    public static final String FILE_ATTRIBUTION_TYPE_LACK_INSPECT_REPAIR_TRACK_SUBMIT = "LACK_INSPECT_REPAIR_TRACK_SUBMIT";

    /**
     * DCC-文件归属   登车工单回填
     */
    public static final String FILE_DCC_BOARD_TYPE_BACK = "DCC1";
    /**
     * DCC-文件归属   送电工单回填
     */
    public static final String FILE_DCC_GIVE_TYPE_BACK = "DCC2";
    /**
     * DCC-文件归属   断电工单回填
     */
    public static final String FILE_DCC_SNAP_TYPE_BACK = "DCC3";
    /**
     * DCC-文件归属   试车工单回填
     */
    public static final String FILE_DCC_TRIAL_TYPE_BACK = "DCC4";
    /**
     * DCC-文件归属   调车工单回填
     */
    public static final String FILE_DCC_SHUNT_TYPE_BACK = "DCC5";
    /**
     * DCC-文件归属   洗车工单回填
     */
    public static final String FILE_DCC_WASH_TYPE_BACK = "DCC6";
    /**
     * DCC-文件归属   保洁工单回填
     */
    public static final String FILE_DCC_CLEAN_TYPE_BACK = "DCC7";
    /**
     * 文件归属   质检问题工单回填
     */
    public static final String FILE_TYPE_QUALITY_QUESTION_BACK = "quality_question";
    /**
     * intent 传递标识-上传资源文件是从数据库还是从ui上传
     */
    public static final String UPLOAD_RESOURCE_TYPE = "upload_resource_type";

    /**
     * 上传资源文件——从数据库取数据
     */
    public static final int UPLOAD_RESOURCE_TYPE_CACHE = 0;

    /**
     * 上传资源文件——从上传界面直接上传
     */
    public static final int UPLOAD_RESOURCE_TYPE_UI = 1;
    //故障工单回填
    public static final int FAULT_BACK_FILL = 3;

    /**
     * 工单回填 存储数据库工单回填类型——计划工单
     */
    public static final String BACK_FILL_PLAN_ORDER = "1";
    /**
     * 工单回填 存储数据库工单回填类型——质检工单
     */
    public static final String BACK_FILL_QUALITY_ORDER = "3";
    /**
     * 故障工单创建
     */
    public static final String SUBMIT_FAULT_ORDER = "2";

    /**
     * intent 标识 附件上传资源
     */
    public static final String UPLOAD_FILE_BEAN = "UPLOAD_FILE_BEAN";
    /**
     * intent 标识 工单回传数据
     */
    public static final String BACK_FILL_BEAN = "BACK_FILL_BEAN";
    /**
     * intent 标识 备注信息
     */
    public static final String REMAKE_BEAN = "REMAKE_BEAN";
    /**
     * intent 标识 工单作业单元数据回填数据
     */
    public static final String BACK_FILL_ORDER_VAR_DATA = "BACK_FILL_ORDER_VAR_DATA";
    /**
     * intent 标识 工单数据详情数据
     */
    public static final String BACK_FILL_ORDER_INFO_DATA = "BACK_FILL_ORDER_INFO_DATA";
    /**
     * intent 标识 工单回传类型
     */
    public static final String BACK_FILL_TYPE = "BACK_FILL_TYPE";
    /**
     * 计划工单回填 存储数据库类型
     */
    public static final String BACK_FILL_PLAN = "1";
    /**
     * 故障工单回填 存储数据库类型
     */
    public static final String BACK_FILL_FAULT = "2";
    /**
     * 质检工单回填 存储数据库类型
     */
    public static final String BACK_FILL_QUALITY = "3";
    /**
     * intent 标识 计划工单穿件工单的时候eventbus回调数据
     */
    public static final String EVEN_BUS_SUBMIT_FAULT = "even_bus_submit_fault";
    /**
     * intent 标识 计划工单创建欠检工单的时候eventbus回调数据
     */
    public static final String EVEN_BUS_LACK_INSPECT_REPAIR_SUBMIT = "EVEN_BUS_LACK_INSPECT_REPAIR_SUBMIT";

    /**
     * 文件类型 file
     */
    public static final String FILE = "file";

    /**
     * 已经上传的资源文件的拼接
     */
//    public static final String ORDER_FILE = "/order/file/receive?id=";
    public static final String ORDER_FILE = "/file/file?id=";
    //视频拼接（只在设备信息详情使用）
    public static final String VIDEO_URL="/file/file/getVideoForPda?id=";

    /**
     * 跳转附件上传activity
     */
    public static final int START_APPENDIX_ACTIVITY = 0;

    /**
     * VAR_LIST_BEAN_ID 附件上传activity退出intent标识
     */
    public static final String VAR_LIST_BEAN_ID = "var_list_bean_id";
    /**
     * NOTIFY_APPENDIX_FRAGMENT_UI_MEDIA_DATA 附件上传activity退出intent标识
     */
    public static final String NOTIFY_APPENDIX_FRAGMENT_UI_MEDIA_DATA = "notify_appendix_fragment_ui_media_data";

    public static final String PACKAGE_POSITION = "packagePosition";

    public static final String VAR_POSITION = "varPosition";

    /**
     * 预览文件 intent标识
     */
    public static final String FILE_PATH = "file_path";
    public static final String FILE_NAME = "file_name";


    /**
     * 文件上传状态 intent标识
     */
    public static final String UP_LOADED_TYPE = "upLoadedType";
    /**
     * 未上传
     */
    public static final int NOT_UP_LOAD_ED = -1;
    /**
     * 没有上传完毕
     */
    public static final int NOT_UP_LOAD = 0;
    /**
     * 上传完毕
     */
    public static final int UP_LOADED = 1;
    /**
     * 预警备注
     */
    public static final String EARLY_WARNING_REMAKE = "1";

    /**
     * 前台服务 渠道id 文件上传服务
     */
    public static final String NOTIFICATION_ID_O1 = "my_channel_01";
    /**
     * 前台服务 渠道id 工单计时服务运行
     */
    public static final String NOTIFICATION_ID_O2 = "my_channel_02";

    /**
     * 前台服务 渠道id 工单计时超时提醒
     */
    public static final String NOTIFICATION_ID_O3 = "my_channel_03";
    /**
     * 前台服务 渠道id 消息推送（待办、消息）
     */
    public static final String NOTIFICATION_ID_O4 = "my_channel_04";
    /**
     * 前台服务 渠道id 工单回填服务
     */
    public static final String NOTIFICATION_ID_O5 = "my_channel_05";

    /**
     * 服务 渠道id 消息、待办
     */
    public static final String NOTIFICATION_ID_O6 = "my_channel_06";
    /**
     * 服务 渠道id 备注
     */
    public static final String NOTIFICATION_ID_O7 = "my_channel_07";
    /**
     *离线回传失败
     */
    public static final String NOTIFICATION_ID_O8 = "my_channel_08";
    /**
     * 是否录像 :是
     */
    public static final String WHETHER_TORE_CORD = "1";
    /**
     * 计划工单父工单 列表状态  待检修
     */
    public static final String PLAN_ORDER_FATHER_TO_BE_OVERHAULED = "0";
    /**
     * 计划工单父工单 列表状态  检修中
     */
    public static final String PLAN_ORDER_FATHER_UNDER_REPAIR = "1";
    /**
     * 计划工单父工单 列表状态  已完成
     */
    public static final String PLAN_ORDER_FATHER_COMPLETED = "2";
    /**
     * WPS包名
     */
    public static final String WPS = "cn.wps.moffice_eng";
    /**
     * 备注回填 1预警
     */
    public static final String WARNING = "1";
    /**
     * 备注回填 0未预警,
     */
    public static final String NOT_WARNING = "0";

    /**
     * 通知标识——附件上传
     */
    public static final int SERVICE_ID_UP_LOAD_SOURCE = 1;
    /**
     * 通知标识——工单计时
     */
    public static final int SERVICE_ID_NOTIFICATION_WORK_ORDER_TIMEOUT = 2;
    /**
     * 通知标识——消息推送
     */
    public static final int SERVICE_ID_MESSAGE_AND_BACKLOG = 3;
    /**
     * 通知标识——工单离线回传
     */
    public static final int SERVICE_ID_BACK_FILL = 4;
    /**
     * 通知标识——计划工单备注回传
     */
    public static final int SERVICE_ID_REMAKE_FILL = 5;

    /**
     * intent 标识
     */
    public static final String ORDER_SOURCE = "orderSource";
    public static final String ORDER_SOURCE_FAULT = "1";
    public static final String ORDER_SOURCE_PLAN = "2";
    public static final String ORDER_SOURCE_MODIFY = "3";
    public static final String ORDER_SOURCE_PLAN_LIST = "4";

    /**
     * intent 标识
     */
    public static final String BUNDLE = "bundle";
    /**
     * intent 标识
     */
    public static final String MESSAGE_DATA = "message_data";
    /**
     * 分钟
     */
    public static final String MIN = "min";
    /**
     * 小时
     */
    public static final String H = "h";
    /**
     * 计划工单审核 通过
     */
    public static final String EXAMINATION_PASSED = "1";
    /**
     * 计划工单审核 未通过
     */
    public static final String NOT_EXAMINATION_PASSED = "0";


    /**
     * 进入登录页
     */
    public static final String START_LOGIN_TAG = "start_login_tag";
    /**
     * 重新登录
     */
    public static final String LOGIN_RE_LOGIN = "1";
    /**
     * 欢迎页进入、退出登录
     */
    public static final String LOGIN_SPLASH = "0";

    /**
     * 物资入库状态-----上架中
     */
    public static final String WAREHOUSING_GROUDING = "2";
    /**
     * 物资入库状态-----待上架
     */
    public static final String WAREHOUSING_GROUDING_WAIT = "1";
    /**
     * 物资入库状态-----已完成
     */
    public static final String WAREHOUSING_FINISHED = "3";
    /**
     * 物资入库类型-----手工单入库 //入库类型 0 手工单入库 1旧件入库 2物资申请入库 3班组退回入库 4调拨入库
     */
    public static final String MATERIAL_ENTER_TYPE_MANUAL = "0";
    /**
     * 物资入库类型-----旧件入库
     */
    public static final String MATERIAL_ENTER_TYPE_USED = "1";
    /**
     * 物资入库类型-----物资申请入库
     */
    public static final String MATERIAL_ENTER_TYPE_APPLY = "2";
    /**
     * 物资入库类型-----班组退回入库
     */
    public static final String MATERIAL_ENTER_TYPE_BACK = "3";
    /**
     * 物资入库类型-----调拨入库
     */
    public static final String MATERIAL_ENTER_TYPE_ALLOT = "4";
    /**
     * 物资出库状态-----待拣货 10 待拣货 20 待物资交接 30 已完成
     */
    public static final String MATERIAL_OUT_STATUS_PICK = "10";
    /**
     * 物资出库状态-----待物资交接
     */
    public static final String MATERIAL_OUT_STATUS_EXCHANGE = "20";
    /**
     * 物资出库状态-----已完成
     */
    public static final String MATERIAL_OUT_STATUS_DONE = "30";
    /**
     * 物资出库类型-----手工出库单 //出库单类型 10 手工出库单 20 班组领用出库 30 调拨出库 40 物品借用出库
     */
    public static final String MATERIAL_OUT_TYPE_MANUAL = "10";
    /**
     * 物资出库类型-----班组领用出库
     */
    public static final String MATERIAL_OUT_TYPE_USE = "20";
    /**
     * 物资出库类型-----调拨出库
     */
    public static final String MATERIAL_OUT_TYPE_ALLOT = "30";
    /**
     * 物资出库类型-----物品借用出库
     */
    public static final String MATERIAL_OUT_TYPE_BORROW = "40";
    /**
     * 物资出库类型-----PDA出库
     */
    public static final String MATERIAL_OUT_TYPE_PDA = "50";

    /**
     * 物资盘点 ------ 草稿
     */
    public static final String PANDIAN_DRAFT = "0";

    /**
     * 物资盘点 ------ 待盘点审核
     */
    public static final String PANDIAN_PENGING_REVIEW = "1";

    /**
     * 物资盘点 ------ 审核完成
     */
    public static final String PANDIAN_FINISH_REVIEW = "2";

    /**
     * 物资盘点 ------ 驳回
     */
    public static final String PANDIAN_REJECT = "3";



    /**
     * 班组物资申请-----待领取
     */
    public static final int MATERIALAPPLY_GROUDING = 0;

    /**
     * 班组物资申请-----领取中
     */
    public static final int MATERIALAPPLY_GROUDING_WAIT = 1;
    /**
     * 班组物资申请-----已完成
     */
    public static final int MATERIALAPPLY_FINISHED = 2;
    /**
     * 班组物资申请id
     */
    public static final String MATERIAL_APPLY_ID = "MATERIAL_APPLY_ID";

    /**
     * 物资类型
     */
    public static final String MATERIAL_TYPE_ID = "MATERIAL_TYPE_ID";

    /**
     * 物资是否form pick
     */
    public static final String MATERIAL_IS_FORM_PICK = "MATERIAL_IS_FORM_PICK";


    /**
     * 物资借还状态-----待审批  //状态 1、草稿 2、待审批 3、待出库 4、待归还 5、待归还确认 6、待入库 7、已归还 8、驳回
     */
    public static final String MATERIAL_BORROW_EXAMINE = "2";
    /**
     * 物资借还状态-----驳回
     */
    public static final String MATERIAL_BORROW_REJECT = "8";
    /**
     * 物资借还状态-----待出库
     */
    public static final String MATERIAL_BORROW_WAREHOUSE = "3";
    /**
     * 物资借还状态-----待归还
     */
    public static final String MATERIAL_BORROW_REVERT = "4";
    /**
     * 物资借还状态-----待确认
     */
    public static final String MATERIAL_BORROW_CONFIRM = "5";
    /**
     * 物资借还状态-----待入库
     */
    public static final String MATERIAL_BORROW_ENTER = "6";
    /**
     * 物资借还状态-----已归还
     */
    public static final String MATERIAL_BORROW_BACK = "7";
    /**
     * 物资报废类型-----物资库存报废
     */
    public static final String MATERIAL_STOCK_SCRAP = "0";
    /**
     * 物资报废类型-----班组物资班报废
     */
    public static final String MATERIAL_TEAM_SCRAP = "1";
    /**
     * 物资报废状态-----待审核 //状态0=草稿；1=待审核；2=已完成；3=驳回
     */
    public static final String MATERIAL_SCRAP_EXAMINE = "1";
    /**
     * 物资报废状态-----已完成
     */
    public static final String MATERIAL_SCRAP_DONE = "2";
    /**
     * 物资报废状态-----驳回
     */
    public static final String MATERIAL_SCRAP_REJECT = "3";
    /**
     * activity forresult 返回后刷新列表
     */
    public static final int INTENT_APPLY_REFRESH_ACTIVITY = 0;
    /**
     * activity 返回物资数据
     */
    public static final int INTENT_MATERIAL_DATA_ACTIVITY = 1;
    /**
     * 选择物资列表
     */
    public static String SELECT_MATERIAL_LIST = "SELECT_MATERIAL_LIST";
    /**
     * DCC-登车工单-----待领取
     */
    public static final String DCC_BOARD_RECEIVE = "0";
    /**
     * DCC-登车工单-----待回填
     */
    public static final String DCC_BOARD_BACK_FILL = "1";
    /**
     * DCC-登车工单-----已完成
     */
    public static final String DCC_BOARD_DONE = "2";
    /**
     * DCC-送电申请-----初稿
     */
    public static final String DCC_GIVE_FIRST_DRAFT= "0";
    /**
     * DCC-送电申请-----申请中
     */
    public static final String DCC_GIVE_APPLYING = "1";
    /**
     * DCC-送电申请-----已完成
     */
    public static final String DCC_GIVE_APPLY_DONE = "2";
    /**
     * DCC-送电申请-----注销
     */
    public static final String DCC_GIVE_APPLY_CANCEL = "3";
    /**
     * DCC-详情信息
     */
    public static final String DCC_DETAIL_INFO = "DCC_DETAIL_INFO";
    /**
     * dcc-list bean
     */
    public static final String DCC_LIST_INFO = "DCC_LIST_INFO";
    /**
     * dcc-is Details
     */
    public static final String DCC_IS_DETAILS = "DCC_IS_DETAILS";
    /**
     * dcc-is DetailsInfo
     */
    public static final String DCC_DETAILS_INFO = "DCC_DETAILS_INFO";
    /**
     * dcc-is DetailsInfo
     */
    public static final String DCC_DETAILS_BACK_CHECK = "DCC_DETAILS_BACK_CHECK";
    /**
     * dcc-is position
     */
    public static final String DCC_POSITION = "DCC_POSITION";
    /**
     * dcc-is category
     */
    public static final String DCC_CATEGORY = "DCC_CATEGORY";
    /**
     * dcc-is listInfo
     */
    public static final String DCC_LIST_DATA = "dcc_list_data";


    /**
     * DCC-车内保洁工单-- 状态:  0 草稿 1 待审核 2 待领取 3 待回填  4待抽查  5 已完成 6驳回
     */
    /*草稿*/
    public static final String DCC_CLEAN_CAOGAO = "0";
    /*待审核*/
    public static final String DCC_CLEAN_DAIHENHE = "1";
    /*待领取*/
    public static final String DCC_CLEAN_UNCLAIMED = "2";
    /**
     * DCC-车内保洁工单-----待回填
     */
    public static final String DCC_CLEAN_BACK_FILL = "3";
    /**
     * DCC-车内保洁工单-----待抽查
     */
    public static final String DCC_CLEAN_CHECK = "4";
    /**
     * DCC-车内保洁工单-----已完成
     */
    public static final String DCC_CLEAN_DONE = "5";
    /*驳回*/
    public static final String DCC_CLEAN_BOHUI = "6";


    /**
     * 培训-培训管理-----正式考试
     */
    public static final String TRAIN_EXAM_FORMAL = "FORMAL";
    /**
     * 培训-培训管理-----模拟考试
     */
    public static final String TRAIN_EXAM_SIMULATION = "SIMULATION";
    /**
     * 培训-培训管理-----自测练习
     */
    public static final String TRAIN_EXAM_TEST = "SELF-TEST";
    /**
     * 培训-页面传值-----考试信息
     */
    public static final String TRAIN_EXAM_INFO = "train_exam_info";
    /**
     * 培训-页面传值-----开始考试
     */
    public static final String TRAIN_EXAM_START = "train_exam_start";
    /**
     * 培训-页面传值-----考试详情
     */
    public static final String TRAIN_EXAM_DETAILS = "train_exam_details";
    /**
     * 专项-技改
     */
    public static final String TECHNOLOGICAL_TRANSFORMATION = "技改";
    /**
     * 专项-普查
     */
    public static final String GENERAL_SURVEY = "普查";
    /**
     * 专项的工单列表传参的code
     */
    public static final String SPECIAL_CODE = "11";


    /**
     * 物资盘点
     */
    public static final String UPDATE_ID = "updateId";
    public static final String AREA_IDS = "areaIds";
    public static final String AREA_NAMES = "areaNames";

    /**
     * 物资盘点
     */
    public static final String INVENTORY_NO = "inventoryNo";

    /**
     * 物资盘点
     */
    public static final String LOC_IDS = "locIds";
    public static final String LOC_NAMES = "locNames";

    /**
     * 物资盘点
     */
    public static final String MATERIAL_TYPES = "materialTypes";
    public static final String MATERIAL_NAME_TYPES = "materialNameTypes";

    /**
     * 物资盘点
     */
    public static final String AREA_PART = "areaPart";

    /**
     * 物资盘点
     */
    public static final String SPECIAL_VALUE = "specialValue";

    /**
     * 物资盘点
     */
    public static final String OPER_TYPE = "opertype";

    /**
     * 物资盘点
     */
    public static final String PANDIAN_ADD_BUNDLE = "pandian_add_bundle";

    //sh16  write by zkslr
    /**
     * 故障类型字典
     */
    public static final String FAULT_ORDER_TYPE = "FAULT_ORDER_TYPE";
    /**
     * 运营状态字典
     */
    public static final String WARRANTY_STATUS = "WARRANTY_STATUS";

    public static final String OPERATION_STATUS  = "OPERATION_STATUS ";



    public static final String DAY_REPAIR_CAR_ON = "日检-仅车顶";
    public static final String DAY_REPAIR_EXCEPT_CAR_ON = "日检-除车顶";
    public static final String DAY_REPAIR_ALL = "日检-全工位";


    public static final String KEY_IS_LOGGED_IN = "is_logged_in";
    public static final String KEY_USER_INFO = "user_info";
    public static final String KEY_TOKEN = "user_token";
    public static final String KEY_USER_ID = "user_id";
}
