package com.crrc.common.observer;

import com.crrc.common.base.BaseModel;

/**
 * <AUTHOR>
 * Date on 2021-09-28
 * Des: model层调用，获取到后台数据后可以接受到数据
 */
public abstract class MvvmDataObserver<T> {

    private BaseModel baseModel;

    public MvvmDataObserver(BaseModel baseModel) {
        this.baseModel = baseModel;
    }

    public void onSuccess(T t, boolean isFromCache) {
        onSuccess(t);
    }

    public abstract void onSuccess(T t);

    public abstract void onFailure(Throwable e);
} 