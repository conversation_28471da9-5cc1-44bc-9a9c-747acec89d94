package com.crrc.common.ui;

import android.app.Dialog;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.crrc.common.R;


/**
 * <AUTHOR>
 * Date on 2022-02-09
 * Des:
 */
public class CustomProgressDialog extends Dialog {
    public static CustomProgressDialog dialog;

    public CustomProgressDialog(@NonNull Context context) {
        super(context);

    }

    public CustomProgressDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected CustomProgressDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }


    public static CustomProgressDialog createDialog(Context context) {
        if (dialog == null) {
            dialog = new CustomProgressDialog(context);
            dialog.setContentView(R.layout.layout_loading);
        }
        return dialog;
    }

    /**
     * 启动加载进度条
     */
    public static void showProgressDialog() {
        try {
            if (dialog != null&&!dialog.isShowing()) {
                dialog.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 关闭加载进度条
     */
    public static void stopProgressDialog() {
        try {
            if (dialog != null) {
                dialog.dismiss();
                dialog = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

