plugins {
    id("com.android.library")
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

android {
    namespace 'com.crrc.common'
    compileSdk 35

    defaultConfig {
        minSdk 28
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion "1.5.1"
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api libs.androidx.appcompat
    api libs.material
    api libs.androidx.core.ktx
    androidTestImplementation libs.androidx.espresso.core
    def composeBom = platform('androidx.compose:compose-bom:2025.02.00')
    api composeBom

    // Compose UI
    api 'androidx.navigation:navigation-compose:2.7.7'
    api 'androidx.compose.material:material-icons-core'
    api 'androidx.compose.material:material-icons-extended'
    api libs.androidx.ui
    api libs.androidx.material3
    api libs.androidx.ui.tooling.preview
    api libs.androidx.activity.compose
    api libs.androidx.runtime.livedata
    api libs.androidx.lifecycle.viewmodel.compose
    api "com.google.accompanist:accompanist-permissions:0.34.0"

    // Fragment 专用viewmodel
    api 'androidx.fragment:fragment-ktx:1.6.2'

    debugImplementation libs.androidx.ui.tooling
    // JSON
    api(libs.gson)

    // Retrofit2
    api(libs.retrofit)
    api(libs.adapter.rxjava2)
    api(libs.converter.gson)
    api(libs.converter.scalars)
    // OkHttp
    api(libs.okhttp)
    api(libs.okhttp3.logging.interceptor)
    // RxJava
    api(libs.rxjava)
    api(libs.rxandroid)
    api(libs.mmkv)

    // ViewModel
    api libs.androidx.lifecycle.viewmodel
    api libs.androidx.lifecycle.livedata

    //preference 设置
    api("androidx.preference:preference:1.0.0")
    //eventbus
    api("org.greenrobot:eventbus:3.2.0")

    //pic
    api "io.coil-kt:coil-compose:2.4.0"
}