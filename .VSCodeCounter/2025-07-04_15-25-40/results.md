# Summary

Date : 2025-07-04 15:25:40

Directory c:\\Users\\<USER>\\Desktop\\app\\siom

Total : 182 files,  9323 codes, 2870 comments, 2206 blanks, all 14399 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Java | 90 | 6,228 | 2,722 | 1,903 | 10,853 |
| XML | 67 | 1,592 | 45 | 90 | 1,727 |
| Markdown | 9 | 689 | 0 | 69 | 758 |
| Python | 4 | 490 | 37 | 88 | 615 |
| Groovy | 5 | 227 | 17 | 32 | 276 |
| Batch | 1 | 39 | 29 | 22 | 90 |
| JSON | 3 | 39 | 0 | 0 | 39 |
| Properties | 3 | 19 | 20 | 2 | 41 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 182 | 9,323 | 2,870 | 2,206 | 14,399 |
| . (Files) | 5 | 151 | 52 | 42 | 245 |
| .VSCodeCounter | 10 | 629 | 0 | 52 | 681 |
| .VSCodeCounter\\2025-04-14_14-24-47 | 5 | 235 | 0 | 26 | 261 |
| .VSCodeCounter\\2025-05-15_10-14-38 | 5 | 394 | 0 | 26 | 420 |
| .idea | 12 | 227 | 0 | 0 | 227 |
| .idea (Files) | 11 | 166 | 0 | 0 | 166 |
| .idea\\inspectionProfiles | 1 | 61 | 0 | 0 | 61 |
| app | 54 | 2,637 | 312 | 410 | 3,359 |
| app (Files) | 1 | 79 | 3 | 10 | 92 |
| app\\libs | 1 | 10 | 0 | 1 | 11 |
| app\\release | 1 | 37 | 0 | 0 | 37 |
| app\\src | 51 | 2,511 | 309 | 399 | 3,219 |
| app\\src\\main | 51 | 2,511 | 309 | 399 | 3,219 |
| app\\src\\main (Files) | 1 | 125 | 4 | 1 | 130 |
| app\\src\\main\\java | 13 | 1,458 | 264 | 321 | 2,043 |
| app\\src\\main\\java\\com | 13 | 1,458 | 264 | 321 | 2,043 |
| app\\src\\main\\java\\com\\crrc | 13 | 1,458 | 264 | 321 | 2,043 |
| app\\src\\main\\java\\com\\crrc\\siom | 13 | 1,458 | 264 | 321 | 2,043 |
| app\\src\\main\\java\\com\\crrc\\siom (Files) | 2 | 100 | 26 | 34 | 160 |
| app\\src\\main\\java\\com\\crrc\\siom\\data | 2 | 66 | 0 | 18 | 84 |
| app\\src\\main\\java\\com\\crrc\\siom\\data\\model | 2 | 66 | 0 | 18 | 84 |
| app\\src\\main\\java\\com\\crrc\\siom\\ui | 9 | 1,292 | 238 | 269 | 1,799 |
| app\\src\\main\\java\\com\\crrc\\siom\\ui\\line | 8 | 1,142 | 221 | 244 | 1,607 |
| app\\src\\main\\java\\com\\crrc\\siom\\ui\\qrcode | 1 | 150 | 17 | 25 | 192 |
| app\\src\\main\\res | 37 | 928 | 41 | 77 | 1,046 |
| app\\src\\main\\res\\drawable | 17 | 335 | 0 | 4 | 339 |
| app\\src\\main\\res\\layout | 8 | 425 | 4 | 55 | 484 |
| app\\src\\main\\res\\menu | 1 | 15 | 0 | 4 | 19 |
| app\\src\\main\\res\\mipmap-anydpi | 2 | 12 | 0 | 0 | 12 |
| app\\src\\main\\res\\values | 4 | 99 | 7 | 8 | 114 |
| app\\src\\main\\res\\values-night | 1 | 17 | 6 | 5 | 28 |
| app\\src\\main\\res\\xml | 4 | 25 | 24 | 1 | 50 |
| common | 66 | 3,657 | 1,496 | 1,191 | 6,344 |
| common (Files) | 1 | 70 | 10 | 12 | 92 |
| common\\src | 65 | 3,587 | 1,486 | 1,179 | 6,252 |
| common\\src\\main | 65 | 3,587 | 1,486 | 1,179 | 6,252 |
| common\\src\\main (Files) | 1 | 4 | 0 | 1 | 5 |
| common\\src\\main\\java | 57 | 3,347 | 1,486 | 1,174 | 6,007 |
| common\\src\\main\\java\\com | 57 | 3,347 | 1,486 | 1,174 | 6,007 |
| common\\src\\main\\java\\com\\crrc | 57 | 3,347 | 1,486 | 1,174 | 6,007 |
| common\\src\\main\\java\\com\\crrc\\common | 57 | 3,347 | 1,486 | 1,174 | 6,007 |
| common\\src\\main\\java\\com\\crrc\\common (Files) | 3 | 439 | 1,173 | 160 | 1,772 |
| common\\src\\main\\java\\com\\crrc\\common\\bean | 45 | 2,514 | 133 | 880 | 3,527 |
| common\\src\\main\\java\\com\\crrc\\common\\bean\\request | 2 | 54 | 3 | 19 | 76 |
| common\\src\\main\\java\\com\\crrc\\common\\bean\\response | 43 | 2,460 | 130 | 861 | 3,451 |
| common\\src\\main\\java\\com\\crrc\\common\\observer | 1 | 13 | 5 | 7 | 25 |
| common\\src\\main\\java\\com\\crrc\\common\\ui | 1 | 44 | 11 | 15 | 70 |
| common\\src\\main\\java\\com\\crrc\\common\\utils | 7 | 337 | 164 | 112 | 613 |
| common\\src\\main\\res | 7 | 236 | 0 | 4 | 240 |
| common\\src\\main\\res\\drawable | 2 | 200 | 0 | 1 | 201 |
| common\\src\\main\\res\\layout | 1 | 10 | 0 | 3 | 13 |
| common\\src\\main\\res\\mipmap-anydpi | 2 | 12 | 0 | 0 | 12 |
| common\\src\\main\\res\\values | 2 | 14 | 0 | 0 | 14 |
| gradle | 1 | 5 | 1 | 1 | 7 |
| gradle\\wrapper | 1 | 5 | 1 | 1 | 7 |
| network | 30 | 1,527 | 972 | 422 | 2,921 |
| network (Files) | 1 | 32 | 0 | 7 | 39 |
| network\\src | 29 | 1,495 | 972 | 415 | 2,882 |
| network\\src\\main | 29 | 1,495 | 972 | 415 | 2,882 |
| network\\src\\main (Files) | 1 | 3 | 0 | 3 | 6 |
| network\\src\\main\\java | 20 | 1,423 | 972 | 408 | 2,803 |
| network\\src\\main\\java\\com | 20 | 1,423 | 972 | 408 | 2,803 |
| network\\src\\main\\java\\com\\crrc | 20 | 1,423 | 972 | 408 | 2,803 |
| network\\src\\main\\java\\com\\crrc\\network | 20 | 1,423 | 972 | 408 | 2,803 |
| network\\src\\main\\java\\com\\crrc\\network (Files) | 1 | 55 | 4 | 13 | 72 |
| network\\src\\main\\java\\com\\crrc\\network\\api | 3 | 605 | 760 | 209 | 1,574 |
| network\\src\\main\\java\\com\\crrc\\network\\base | 2 | 101 | 65 | 22 | 188 |
| network\\src\\main\\java\\com\\crrc\\network\\commoninterceptor | 3 | 85 | 15 | 29 | 129 |
| network\\src\\main\\java\\com\\crrc\\network\\download | 4 | 260 | 55 | 65 | 380 |
| network\\src\\main\\java\\com\\crrc\\network\\environment | 2 | 61 | 19 | 18 | 98 |
| network\\src\\main\\java\\com\\crrc\\network\\errorhandler | 2 | 133 | 30 | 23 | 186 |
| network\\src\\main\\java\\com\\crrc\\network\\observer | 2 | 75 | 9 | 16 | 100 |
| network\\src\\main\\java\\com\\crrc\\network\\upload | 1 | 48 | 15 | 13 | 76 |
| network\\src\\main\\res | 8 | 69 | 0 | 4 | 73 |
| network\\src\\main\\res\\drawable | 1 | 15 | 0 | 1 | 16 |
| network\\src\\main\\res\\layout | 1 | 7 | 0 | 0 | 7 |
| network\\src\\main\\res\\mipmap-anydpi | 2 | 12 | 0 | 0 | 12 |
| network\\src\\main\\res\\values | 3 | 24 | 0 | 1 | 25 |
| network\\src\\main\\res\\xml | 1 | 11 | 0 | 2 | 13 |
| tools | 4 | 490 | 37 | 88 | 615 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)