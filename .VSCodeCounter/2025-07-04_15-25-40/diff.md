# Diff Summary

Date : 2025-07-04 15:25:40

Directory c:\\Users\\<USER>\\Desktop\\app\\siom

Total : 50 files,  2335 codes, 631 comments, 547 blanks, all 3513 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Java | 28 | 1,543 | 624 | 485 | 2,652 |
| Markdown | 9 | 635 | 0 | 51 | 686 |
| XML | 9 | 144 | 5 | 10 | 159 |
| Groovy | 2 | 11 | 2 | 1 | 14 |
| JSON | 2 | 2 | 0 | 0 | 2 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 50 | 2,335 | 631 | 547 | 3,513 |
| . (Files) | 1 | 8 | 0 | -1 | 7 |
| .VSCodeCounter | 10 | 629 | 0 | 52 | 681 |
| .V<PERSON>ode<PERSON>ounter\\2025-04-14_14-24-47 | 5 | 235 | 0 | 26 | 261 |
| .VSCodeCounter\\2025-05-15_10-14-38 | 5 | 394 | 0 | 26 | 420 |
| .idea | 1 | 10 | 0 | 0 | 10 |
| app | 11 | 336 | 25 | 43 | 404 |
| app (Files) | 1 | 10 | 2 | 1 | 13 |
| app\\src | 10 | 326 | 23 | 42 | 391 |
| app\\src\\main | 10 | 326 | 23 | 42 | 391 |
| app\\src\\main (Files) | 1 | 37 | 1 | 0 | 38 |
| app\\src\\main\\java | 2 | 192 | 18 | 32 | 242 |
| app\\src\\main\\java\\com | 2 | 192 | 18 | 32 | 242 |
| app\\src\\main\\java\\com\\crrc | 2 | 192 | 18 | 32 | 242 |
| app\\src\\main\\java\\com\\crrc\\siom | 2 | 192 | 18 | 32 | 242 |
| app\\src\\main\\java\\com\\crrc\\siom (Files) | 1 | 42 | 1 | 7 | 50 |
| app\\src\\main\\java\\com\\crrc\\siom\\ui | 1 | 150 | 17 | 25 | 192 |
| app\\src\\main\\java\\com\\crrc\\siom\\ui\\qrcode | 1 | 150 | 17 | 25 | 192 |
| app\\src\\main\\res | 7 | 97 | 4 | 10 | 111 |
| app\\src\\main\\res\\drawable | 4 | 25 | 0 | 0 | 25 |
| app\\src\\main\\res\\layout | 1 | 55 | 4 | 9 | 68 |
| app\\src\\main\\res\\xml | 2 | 17 | 0 | 1 | 18 |
| common | 23 | 866 | 2 | 301 | 1,169 |
| common (Files) | 1 | 1 | 0 | 0 | 1 |
| common\\src | 22 | 865 | 2 | 301 | 1,168 |
| common\\src\\main | 22 | 865 | 2 | 301 | 1,168 |
| common\\src\\main\\java | 22 | 865 | 2 | 301 | 1,168 |
| common\\src\\main\\java\\com | 22 | 865 | 2 | 301 | 1,168 |
| common\\src\\main\\java\\com\\crrc | 22 | 865 | 2 | 301 | 1,168 |
| common\\src\\main\\java\\com\\crrc\\common | 22 | 865 | 2 | 301 | 1,168 |
| common\\src\\main\\java\\com\\crrc\\common (Files) | 1 | 5 | 3 | 5 | 13 |
| common\\src\\main\\java\\com\\crrc\\common\\bean | 21 | 860 | -1 | 296 | 1,155 |
| common\\src\\main\\java\\com\\crrc\\common\\bean\\request | 1 | 17 | 0 | 6 | 23 |
| common\\src\\main\\java\\com\\crrc\\common\\bean\\response | 20 | 843 | -1 | 290 | 1,132 |
| network | 4 | 486 | 604 | 152 | 1,242 |
| network\\src | 4 | 486 | 604 | 152 | 1,242 |
| network\\src\\main | 4 | 486 | 604 | 152 | 1,242 |
| network\\src\\main\\java | 4 | 486 | 604 | 152 | 1,242 |
| network\\src\\main\\java\\com | 4 | 486 | 604 | 152 | 1,242 |
| network\\src\\main\\java\\com\\crrc | 4 | 486 | 604 | 152 | 1,242 |
| network\\src\\main\\java\\com\\crrc\\network | 4 | 486 | 604 | 152 | 1,242 |
| network\\src\\main\\java\\com\\crrc\\network\\api | 3 | 486 | 602 | 152 | 1,240 |
| network\\src\\main\\java\\com\\crrc\\network\\commoninterceptor | 1 | 0 | 2 | 0 | 2 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)