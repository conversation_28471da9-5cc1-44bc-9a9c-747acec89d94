{"file:///c%3A/Users/<USER>/Desktop/app/siom/readme.md": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 17}, "file:///c%3A/Users/<USER>/Desktop/app/siom/tools/web_scraper.py": {"language": "Python", "code": 157, "comment": 20, "blank": 30}, "file:///c%3A/Users/<USER>/Desktop/app/siom/settings.gradle": {"language": "Groovy", "code": 41, "comment": 3, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/build.gradle": {"language": "Groovy", "code": 32, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/tools/search_engine.py": {"language": "Python", "code": 64, "comment": 1, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/build.gradle": {"language": "Groovy", "code": 70, "comment": 10, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/app/siom/gradle.properties": {"language": "Properties", "code": 4, "comment": 19, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/build.gradle": {"language": "Groovy", "code": 5, "comment": 1, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/gradlew.bat": {"language": "<PERSON><PERSON>", "code": 39, "comment": 29, "blank": 22}, "file:///c%3A/Users/<USER>/Desktop/app/siom/tools/screenshot_utils.py": {"language": "Python", "code": 44, "comment": 2, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/app/siom/tools/llm_api.py": {"language": "Python", "code": 225, "comment": 14, "blank": 33}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/app/siom/gradle/wrapper/gradle-wrapper.properties": {"language": "Properties", "code": 5, "comment": 1, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/values/array.xml": {"language": "XML", "code": 11, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/build.gradle": {"language": "Groovy", "code": 79, "comment": 3, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/values/colors.xml": {"language": "XML", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/values/strings.xml": {"language": "XML", "code": 3, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/mipmap-anydpi/ic_launcher_round.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/mipmap-anydpi/ic_launcher.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/xml/network_security_config.xml": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/xml/file_paths.xml": {"language": "XML", "code": 13, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/xml/data_extraction_rules.xml": {"language": "XML", "code": 5, "comment": 14, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/values/strings.xml": {"language": "XML", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/xml/backup_rules.xml": {"language": "XML", "code": 3, "comment": 10, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/values/dimens.xml": {"language": "XML", "code": 4, "comment": 1, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_dot.xml": {"language": "XML", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/NetworkRequestInfo.java": {"language": "Java", "code": 25, "comment": 3, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/values-night/colors.xml": {"language": "XML", "code": 17, "comment": 6, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/values/themes.xml": {"language": "XML", "code": 13, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_dashboard_black_24dp.xml": {"language": "XML", "code": 9, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/menu/bottom_nav_menu.xml": {"language": "XML", "code": 15, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/values/colors.xml": {"language": "XML", "code": 75, "comment": 6, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/fragment_storage.xml": {"language": "XML", "code": 18, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/item_alarm.xml": {"language": "XML", "code": 56, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_build.xml": {"language": "XML", "code": 11, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_arrow_back.xml": {"language": "XML", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/item_storage.xml": {"language": "XML", "code": 95, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_flashlight_off.xml": {"language": "XML", "code": 9, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/custom_marker_info.xml": {"language": "XML", "code": 22, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/custom_info_window.xml": {"language": "XML", "code": 26, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/fragment_alarm.xml": {"language": "XML", "code": 18, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/bg_marker_info_bubble.xml": {"language": "XML", "code": 15, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/bg_marker_info_oval.xml": {"language": "XML", "code": 8, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/activity_line_map.xml": {"language": "XML", "code": 135, "comment": 0, "blank": 18}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/AppApplication.java": {"language": "Java", "code": 75, "comment": 23, "blank": 22}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/layout/activity_qr_scanner.xml": {"language": "XML", "code": 55, "comment": 4, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/bg_marker_info.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_flashlight_on.xml": {"language": "XML", "code": 12, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/bg_current_location.xml": {"language": "XML", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/bg_info_window.xml": {"language": "XML", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/round_button_bg.xml": {"language": "XML", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_notifications_black_24dp.xml": {"language": "XML", "code": 9, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_launcher_foreground.xml": {"language": "XML", "code": 30, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_home_black_24dp.xml": {"language": "XML", "code": 9, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_warning.xml": {"language": "XML", "code": 11, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/res/drawable/ic_launcher_background.xml": {"language": "XML", "code": 170, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/data/model/AlarmInfo.java": {"language": "Java", "code": 29, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/data/model/StorageItem.java": {"language": "Java", "code": 37, "comment": 0, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 125, "comment": 4, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/qrcode/QRScannerActivity.java": {"language": "Java", "code": 150, "comment": 17, "blank": 25}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/MapMarkerManager.java": {"language": "Java", "code": 223, "comment": 61, "blank": 55}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/StorageFragment.java": {"language": "Java", "code": 118, "comment": 0, "blank": 22}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/MapMarkerUtils.java": {"language": "Java", "code": 60, "comment": 13, "blank": 16}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/StationDetailAdapter.java": {"language": "Java", "code": 44, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaActivity.java": {"language": "Java", "code": 310, "comment": 68, "blank": 54}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/DetailPanelManager.java": {"language": "Java", "code": 128, "comment": 43, "blank": 29}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/AlarmFragment.java": {"language": "Java", "code": 90, "comment": 0, "blank": 20}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaViewModel.java": {"language": "Java", "code": 169, "comment": 36, "blank": 40}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/libs/build.properties": {"language": "Properties", "code": 10, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/xml/environment_preference.xml": {"language": "XML", "code": 11, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/res/values/strings.xml": {"language": "XML", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/res/values/colors.xml": {"language": "XML", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/res/mipmap-anydpi/ic_launcher_round.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/res/layout/layout_loading.xml": {"language": "XML", "code": 10, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/res/mipmap-anydpi/ic_launcher.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/app/release/output-metadata.json": {"language": "JSON", "code": 37, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/res/drawable/ic_launcher_foreground.xml": {"language": "XML", "code": 30, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/res/drawable/ic_launcher_background.xml": {"language": "XML", "code": 170, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/mipmap-anydpi/ic_launcher.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/AndroidManifest.xml": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/mipmap-anydpi/ic_launcher_round.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/layout/activity_environment.xml": {"language": "XML", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-04-14_14-24-47/diff.md": {"language": "<PERSON><PERSON>", "code": 12, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/NetworkApi.java": {"language": "Java", "code": 55, "comment": 4, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/upload/FileProgressRequestBody.java": {"language": "Java", "code": 48, "comment": 15, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/res/drawable/ic_controls.xml": {"language": "XML", "code": 15, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-04-14_14-24-47/diff-details.md": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-05-15_10-14-38/results.md": {"language": "<PERSON><PERSON>", "code": 97, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/observer/BaseObserver.java": {"language": "Java", "code": 38, "comment": 5, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-04-14_14-24-47/details.md": {"language": "<PERSON><PERSON>", "code": 123, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-05-15_10-14-38/diff.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-05-15_10-14-38/details.md": {"language": "<PERSON><PERSON>", "code": 157, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-05-15_10-14-38/diff-details.md": {"language": "<PERSON><PERSON>", "code": 70, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/observer/BaseObserverForBasic.java": {"language": "Java", "code": 37, "comment": 4, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-04-14_14-24-47/results.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-05-15_10-14-38/results.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.VSCodeCounter/2025-04-14_14-24-47/results.md": {"language": "<PERSON><PERSON>", "code": 90, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/errorhandler/HttpErrorHandler.java": {"language": "Java", "code": 9, "comment": 6, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/environment/IEnvironment.java": {"language": "Java", "code": 4, "comment": 9, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/errorhandler/ExceptionHandle.java": {"language": "Java", "code": 124, "comment": 24, "blank": 19}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/commoninterceptor/DynamicTimeoutInterceptor.java": {"language": "Java", "code": 38, "comment": 4, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/environment/EnvironmentActivity.java": {"language": "Java", "code": 57, "comment": 10, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/api/ApiInterface3.java": {"language": "Java", "code": 202, "comment": 243, "blank": 66}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonResponseInterceptor.java": {"language": "Java", "code": 12, "comment": 4, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java": {"language": "Java", "code": 35, "comment": 7, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/api/ApiInterface2.java": {"language": "Java", "code": 157, "comment": 232, "blank": 60}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/api/ApiInterface.java": {"language": "Java", "code": 246, "comment": 285, "blank": 83}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/base/INetworkRequiredInfo.java": {"language": "Java", "code": 8, "comment": 23, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/base/NetworkApi.java": {"language": "Java", "code": 93, "comment": 42, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/download/DownloadInfo.java": {"language": "Java", "code": 60, "comment": 0, "blank": 22}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/download/DownloadProgressHandler.java": {"language": "Java", "code": 47, "comment": 14, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/download/FileDownloader.java": {"language": "Java", "code": 145, "comment": 12, "blank": 21}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/AndroidProjectSystem.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/appInsightsSettings.xml": {"language": "XML", "code": 41, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/network/src/main/java/com/crrc/network/download/DownloadCallBack.java": {"language": "Java", "code": 8, "comment": 29, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/migrations.xml": {"language": "XML", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/misc.xml": {"language": "XML", "code": 13, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/vcs.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/runConfigurations.xml": {"language": "XML", "code": 17, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/compiler.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/kotlinc.xml": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/deploymentTargetSelector.xml": {"language": "XML", "code": 30, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/gradle.xml": {"language": "XML", "code": 21, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/inspectionProfiles/Project_Default.xml": {"language": "XML", "code": 61, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/utils/MMkvUtil.java": {"language": "Java", "code": 81, "comment": 42, "blank": 24}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/utils/ToastUtil.java": {"language": "Java", "code": 32, "comment": 8, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/utils/PrefsUtil.java": {"language": "Java", "code": 63, "comment": 0, "blank": 24}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/utils/LiveDataUtils.java": {"language": "Java", "code": 39, "comment": 12, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/utils/GsonUtil.java": {"language": "Java", "code": 70, "comment": 51, "blank": 17}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/utils/EventBusUtils.java": {"language": "Java", "code": 36, "comment": 46, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/ui/CustomProgressDialog.java": {"language": "Java", "code": 44, "comment": 11, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/BaseResponse.java": {"language": "Java", "code": 18, "comment": 3, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/utils/GetRequestBody.java": {"language": "Java", "code": 16, "comment": 5, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/observer/MvvmDataObserver.java": {"language": "Java", "code": 13, "comment": 5, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/BaseApplication.java": {"language": "Java", "code": 10, "comment": 3, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/LineTool.java": {"language": "Java", "code": 31, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/Constant.java": {"language": "Java", "code": 411, "comment": 1167, "blank": 146}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/LineInfoResponse.java": {"language": "Java", "code": 40, "comment": 3, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/LabelListResponse.java": {"language": "Java", "code": 17, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/LineDevice.java": {"language": "Java", "code": 31, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/FilesByLabelResponse.java": {"language": "Java", "code": 11, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/GroupMemberInfoResponse.java": {"language": "Java", "code": 24, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/FaultReportResponse.java": {"language": "Java", "code": 55, "comment": 3, "blank": 19}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/EvenBusGoLoginData.java": {"language": "Java", "code": 7, "comment": 5, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/FaultDetailResponse.java": {"language": "Java", "code": 10, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/LoginBean.java": {"language": "Java", "code": 31, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/FaultOrderListResponse.java": {"language": "Java", "code": 34, "comment": 0, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/EmergencyOrderListResponse.java": {"language": "Java", "code": 34, "comment": 0, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/EmergencyEventListResponse.java": {"language": "Java", "code": 41, "comment": 3, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/DocumentRecord.java": {"language": "Java", "code": 122, "comment": 0, "blank": 36}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/EmergencyDetailResponse.java": {"language": "Java", "code": 12, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/DirsListResponse.java": {"language": "Java", "code": 11, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/request/QRcodeRequest.java": {"language": "Java", "code": 17, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/MembersResponse.java": {"language": "Java", "code": 38, "comment": 0, "blank": 17}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/DeviceInfo.java": {"language": "Java", "code": 102, "comment": 3, "blank": 32}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/DeviceDetailResponse.java": {"language": "Java", "code": 101, "comment": 0, "blank": 33}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/CurrentDirsResponse.java": {"language": "Java", "code": 26, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/request/PickMaterialRequest.java": {"language": "Java", "code": 37, "comment": 3, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/ChatMessage.java": {"language": "Java", "code": 39, "comment": 0, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/ChatInfo.java": {"language": "Java", "code": 45, "comment": 0, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/CaseListResponse.java": {"language": "Java", "code": 101, "comment": 45, "blank": 41}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/BaseOrderRecord.java": {"language": "Java", "code": 76, "comment": 0, "blank": 30}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/AlarmResponse.java": {"language": "Java", "code": 20, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/CaseDetailResponse.java": {"language": "Java", "code": 123, "comment": 33, "blank": 47}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/PickMaterialResponse.java": {"language": "Java", "code": 63, "comment": 3, "blank": 22}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/AddressBookListResponse.java": {"language": "Java", "code": 101, "comment": 0, "blank": 39}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/UserInfoResponse.java": {"language": "Java", "code": 66, "comment": 0, "blank": 20}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/OrderFilterParamResponse.java": {"language": "Java", "code": 59, "comment": 0, "blank": 24}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderCountResponse.java": {"language": "Java", "code": 36, "comment": 3, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderDetailResponse.java": {"language": "Java", "code": 219, "comment": 7, "blank": 79}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/WorkorderStatics.java": {"language": "Java", "code": 31, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/TokenIndoData.java": {"language": "Java", "code": 150, "comment": 5, "blank": 37}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/TempWorkOrderParamResponse.java": {"language": "Java", "code": 171, "comment": 0, "blank": 53}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/TaskOverviewResponse.java": {"language": "Java", "code": 36, "comment": 3, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/StoreInfoResponse.java": {"language": "Java", "code": 47, "comment": 3, "blank": 16}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/SearchResponse.java": {"language": "Java", "code": 96, "comment": 3, "blank": 31}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/TempOrderListResponse.java": {"language": "Java", "code": 34, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/StationAlarmResponse.java": {"language": "Java", "code": 36, "comment": 3, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/SearchFileResponse.java": {"language": "Java", "code": 11, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/ProcedureResponse.java": {"language": "Java", "code": 83, "comment": 2, "blank": 30}, "file:///c%3A/Users/<USER>/Desktop/app/siom/common/src/main/java/com/crrc/common/bean/response/PlanOrderListResponse.java": {"language": "Java", "code": 39, "comment": 3, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/app/siom/.idea/AugmentWebviewStateStore.xml": {"language": "XML", "code": 10, "comment": 0, "blank": 0}}