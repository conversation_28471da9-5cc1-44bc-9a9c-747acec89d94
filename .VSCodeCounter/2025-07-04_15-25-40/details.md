# Details

Date : 2025-07-04 15:25:40

Directory c:\\Users\\<USER>\\Desktop\\app\\siom

Total : 182 files,  9323 codes, 2870 comments, 2206 blanks, all 14399 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/details.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/details.md) | Markdown | 123 | 0 | 6 | 129 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/diff-details.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/diff-details.md) | Markdown | 9 | 0 | 6 | 15 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/diff.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/diff.md) | Markdown | 12 | 0 | 7 | 19 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/results.json](/siom/.VSCodeCounter/2025-04-14_14-24-47/results.json) | JSON | 1 | 0 | 0 | 1 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/results.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/results.md) | Markdown | 90 | 0 | 7 | 97 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/details.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/details.md) | Markdown | 157 | 0 | 6 | 163 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/diff-details.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/diff-details.md) | Markdown | 70 | 0 | 6 | 76 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/diff.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/diff.md) | Markdown | 69 | 0 | 7 | 76 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/results.json](/siom/.VSCodeCounter/2025-05-15_10-14-38/results.json) | JSON | 1 | 0 | 0 | 1 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/results.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/results.md) | Markdown | 97 | 0 | 7 | 104 |
| [siom/.idea/AndroidProjectSystem.xml](/siom/.idea/AndroidProjectSystem.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/.idea/AugmentWebviewStateStore.xml](/siom/.idea/AugmentWebviewStateStore.xml) | XML | 10 | 0 | 0 | 10 |
| [siom/.idea/appInsightsSettings.xml](/siom/.idea/appInsightsSettings.xml) | XML | 41 | 0 | 0 | 41 |
| [siom/.idea/compiler.xml](/siom/.idea/compiler.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/.idea/deploymentTargetSelector.xml](/siom/.idea/deploymentTargetSelector.xml) | XML | 30 | 0 | 0 | 30 |
| [siom/.idea/gradle.xml](/siom/.idea/gradle.xml) | XML | 21 | 0 | 0 | 21 |
| [siom/.idea/inspectionProfiles/Project\_Default.xml](/siom/.idea/inspectionProfiles/Project_Default.xml) | XML | 61 | 0 | 0 | 61 |
| [siom/.idea/kotlinc.xml](/siom/.idea/kotlinc.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/.idea/migrations.xml](/siom/.idea/migrations.xml) | XML | 10 | 0 | 0 | 10 |
| [siom/.idea/misc.xml](/siom/.idea/misc.xml) | XML | 13 | 0 | 0 | 13 |
| [siom/.idea/runConfigurations.xml](/siom/.idea/runConfigurations.xml) | XML | 17 | 0 | 0 | 17 |
| [siom/.idea/vcs.xml](/siom/.idea/vcs.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/app/build.gradle](/siom/app/build.gradle) | Groovy | 79 | 3 | 10 | 92 |
| [siom/app/libs/build.properties](/siom/app/libs/build.properties) | Properties | 10 | 0 | 1 | 11 |
| [siom/app/release/output-metadata.json](/siom/app/release/output-metadata.json) | JSON | 37 | 0 | 0 | 37 |
| [siom/app/src/main/AndroidManifest.xml](/siom/app/src/main/AndroidManifest.xml) | XML | 125 | 4 | 1 | 130 |
| [siom/app/src/main/java/com/crrc/siom/AppApplication.java](/siom/app/src/main/java/com/crrc/siom/AppApplication.java) | Java | 75 | 23 | 22 | 120 |
| [siom/app/src/main/java/com/crrc/siom/NetworkRequestInfo.java](/siom/app/src/main/java/com/crrc/siom/NetworkRequestInfo.java) | Java | 25 | 3 | 12 | 40 |
| [siom/app/src/main/java/com/crrc/siom/data/model/AlarmInfo.java](/siom/app/src/main/java/com/crrc/siom/data/model/AlarmInfo.java) | Java | 29 | 0 | 8 | 37 |
| [siom/app/src/main/java/com/crrc/siom/data/model/StorageItem.java](/siom/app/src/main/java/com/crrc/siom/data/model/StorageItem.java) | Java | 37 | 0 | 10 | 47 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/AlarmFragment.java](/siom/app/src/main/java/com/crrc/siom/ui/line/AlarmFragment.java) | Java | 90 | 0 | 20 | 110 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/DetailPanelManager.java](/siom/app/src/main/java/com/crrc/siom/ui/line/DetailPanelManager.java) | Java | 128 | 43 | 29 | 200 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaActivity.java](/siom/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaActivity.java) | Java | 310 | 68 | 54 | 432 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaViewModel.java](/siom/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaViewModel.java) | Java | 169 | 36 | 40 | 245 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/MapMarkerManager.java](/siom/app/src/main/java/com/crrc/siom/ui/line/MapMarkerManager.java) | Java | 223 | 61 | 55 | 339 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/MapMarkerUtils.java](/siom/app/src/main/java/com/crrc/siom/ui/line/MapMarkerUtils.java) | Java | 60 | 13 | 16 | 89 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/StationDetailAdapter.java](/siom/app/src/main/java/com/crrc/siom/ui/line/StationDetailAdapter.java) | Java | 44 | 0 | 8 | 52 |
| [siom/app/src/main/java/com/crrc/siom/ui/line/StorageFragment.java](/siom/app/src/main/java/com/crrc/siom/ui/line/StorageFragment.java) | Java | 118 | 0 | 22 | 140 |
| [siom/app/src/main/java/com/crrc/siom/ui/qrcode/QRScannerActivity.java](/siom/app/src/main/java/com/crrc/siom/ui/qrcode/QRScannerActivity.java) | Java | 150 | 17 | 25 | 192 |
| [siom/app/src/main/res/drawable/bg\_current\_location.xml](/siom/app/src/main/res/drawable/bg_current_location.xml) | XML | 7 | 0 | 0 | 7 |
| [siom/app/src/main/res/drawable/bg\_info\_window.xml](/siom/app/src/main/res/drawable/bg_info_window.xml) | XML | 7 | 0 | 0 | 7 |
| [siom/app/src/main/res/drawable/bg\_marker\_info.xml](/siom/app/src/main/res/drawable/bg_marker_info.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/app/src/main/res/drawable/bg\_marker\_info\_bubble.xml](/siom/app/src/main/res/drawable/bg_marker_info_bubble.xml) | XML | 15 | 0 | 0 | 15 |
| [siom/app/src/main/res/drawable/bg\_marker\_info\_oval.xml](/siom/app/src/main/res/drawable/bg_marker_info_oval.xml) | XML | 8 | 0 | 0 | 8 |
| [siom/app/src/main/res/drawable/ic\_arrow\_back.xml](/siom/app/src/main/res/drawable/ic_arrow_back.xml) | XML | 10 | 0 | 0 | 10 |
| [siom/app/src/main/res/drawable/ic\_build.xml](/siom/app/src/main/res/drawable/ic_build.xml) | XML | 11 | 0 | 0 | 11 |
| [siom/app/src/main/res/drawable/ic\_dashboard\_black\_24dp.xml](/siom/app/src/main/res/drawable/ic_dashboard_black_24dp.xml) | XML | 9 | 0 | 1 | 10 |
| [siom/app/src/main/res/drawable/ic\_dot.xml](/siom/app/src/main/res/drawable/ic_dot.xml) | XML | 7 | 0 | 0 | 7 |
| [siom/app/src/main/res/drawable/ic\_flashlight\_off.xml](/siom/app/src/main/res/drawable/ic_flashlight_off.xml) | XML | 9 | 0 | 0 | 9 |
| [siom/app/src/main/res/drawable/ic\_flashlight\_on.xml](/siom/app/src/main/res/drawable/ic_flashlight_on.xml) | XML | 12 | 0 | 0 | 12 |
| [siom/app/src/main/res/drawable/ic\_home\_black\_24dp.xml](/siom/app/src/main/res/drawable/ic_home_black_24dp.xml) | XML | 9 | 0 | 1 | 10 |
| [siom/app/src/main/res/drawable/ic\_launcher\_background.xml](/siom/app/src/main/res/drawable/ic_launcher_background.xml) | XML | 170 | 0 | 1 | 171 |
| [siom/app/src/main/res/drawable/ic\_launcher\_foreground.xml](/siom/app/src/main/res/drawable/ic_launcher_foreground.xml) | XML | 30 | 0 | 0 | 30 |
| [siom/app/src/main/res/drawable/ic\_notifications\_black\_24dp.xml](/siom/app/src/main/res/drawable/ic_notifications_black_24dp.xml) | XML | 9 | 0 | 1 | 10 |
| [siom/app/src/main/res/drawable/ic\_warning.xml](/siom/app/src/main/res/drawable/ic_warning.xml) | XML | 11 | 0 | 0 | 11 |
| [siom/app/src/main/res/drawable/round\_button\_bg.xml](/siom/app/src/main/res/drawable/round_button_bg.xml) | XML | 5 | 0 | 0 | 5 |
| [siom/app/src/main/res/layout/activity\_line\_map.xml](/siom/app/src/main/res/layout/activity_line_map.xml) | XML | 135 | 0 | 18 | 153 |
| [siom/app/src/main/res/layout/activity\_qr\_scanner.xml](/siom/app/src/main/res/layout/activity_qr_scanner.xml) | XML | 55 | 4 | 9 | 68 |
| [siom/app/src/main/res/layout/custom\_info\_window.xml](/siom/app/src/main/res/layout/custom_info_window.xml) | XML | 26 | 0 | 3 | 29 |
| [siom/app/src/main/res/layout/custom\_marker\_info.xml](/siom/app/src/main/res/layout/custom_marker_info.xml) | XML | 22 | 0 | 3 | 25 |
| [siom/app/src/main/res/layout/fragment\_alarm.xml](/siom/app/src/main/res/layout/fragment_alarm.xml) | XML | 18 | 0 | 2 | 20 |
| [siom/app/src/main/res/layout/fragment\_storage.xml](/siom/app/src/main/res/layout/fragment_storage.xml) | XML | 18 | 0 | 2 | 20 |
| [siom/app/src/main/res/layout/item\_alarm.xml](/siom/app/src/main/res/layout/item_alarm.xml) | XML | 56 | 0 | 7 | 63 |
| [siom/app/src/main/res/layout/item\_storage.xml](/siom/app/src/main/res/layout/item_storage.xml) | XML | 95 | 0 | 11 | 106 |
| [siom/app/src/main/res/menu/bottom\_nav\_menu.xml](/siom/app/src/main/res/menu/bottom_nav_menu.xml) | XML | 15 | 0 | 4 | 19 |
| [siom/app/src/main/res/mipmap-anydpi/ic\_launcher.xml](/siom/app/src/main/res/mipmap-anydpi/ic_launcher.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/app/src/main/res/mipmap-anydpi/ic\_launcher\_round.xml](/siom/app/src/main/res/mipmap-anydpi/ic_launcher_round.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/app/src/main/res/values-night/colors.xml](/siom/app/src/main/res/values-night/colors.xml) | XML | 17 | 6 | 5 | 28 |
| [siom/app/src/main/res/values/colors.xml](/siom/app/src/main/res/values/colors.xml) | XML | 75 | 6 | 6 | 87 |
| [siom/app/src/main/res/values/dimens.xml](/siom/app/src/main/res/values/dimens.xml) | XML | 4 | 1 | 0 | 5 |
| [siom/app/src/main/res/values/strings.xml](/siom/app/src/main/res/values/strings.xml) | XML | 7 | 0 | 0 | 7 |
| [siom/app/src/main/res/values/themes.xml](/siom/app/src/main/res/values/themes.xml) | XML | 13 | 0 | 2 | 15 |
| [siom/app/src/main/res/xml/backup\_rules.xml](/siom/app/src/main/res/xml/backup_rules.xml) | XML | 3 | 10 | 0 | 13 |
| [siom/app/src/main/res/xml/data\_extraction\_rules.xml](/siom/app/src/main/res/xml/data_extraction_rules.xml) | XML | 5 | 14 | 0 | 19 |
| [siom/app/src/main/res/xml/file\_paths.xml](/siom/app/src/main/res/xml/file_paths.xml) | XML | 13 | 0 | 0 | 13 |
| [siom/app/src/main/res/xml/network\_security\_config.xml](/siom/app/src/main/res/xml/network_security_config.xml) | XML | 4 | 0 | 1 | 5 |
| [siom/build.gradle](/siom/build.gradle) | Groovy | 5 | 1 | 0 | 6 |
| [siom/common/build.gradle](/siom/common/build.gradle) | Groovy | 70 | 10 | 12 | 92 |
| [siom/common/src/main/AndroidManifest.xml](/siom/common/src/main/AndroidManifest.xml) | XML | 4 | 0 | 1 | 5 |
| [siom/common/src/main/java/com/crrc/common/BaseApplication.java](/siom/common/src/main/java/com/crrc/common/BaseApplication.java) | Java | 10 | 3 | 5 | 18 |
| [siom/common/src/main/java/com/crrc/common/BaseResponse.java](/siom/common/src/main/java/com/crrc/common/BaseResponse.java) | Java | 18 | 3 | 9 | 30 |
| [siom/common/src/main/java/com/crrc/common/Constant.java](/siom/common/src/main/java/com/crrc/common/Constant.java) | Java | 411 | 1,167 | 146 | 1,724 |
| [siom/common/src/main/java/com/crrc/common/bean/request/PickMaterialRequest.java](/siom/common/src/main/java/com/crrc/common/bean/request/PickMaterialRequest.java) | Java | 37 | 3 | 13 | 53 |
| [siom/common/src/main/java/com/crrc/common/bean/request/QRcodeRequest.java](/siom/common/src/main/java/com/crrc/common/bean/request/QRcodeRequest.java) | Java | 17 | 0 | 6 | 23 |
| [siom/common/src/main/java/com/crrc/common/bean/response/AddressBookListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/AddressBookListResponse.java) | Java | 101 | 0 | 39 | 140 |
| [siom/common/src/main/java/com/crrc/common/bean/response/AlarmResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/AlarmResponse.java) | Java | 20 | 0 | 5 | 25 |
| [siom/common/src/main/java/com/crrc/common/bean/response/BaseOrderRecord.java](/siom/common/src/main/java/com/crrc/common/bean/response/BaseOrderRecord.java) | Java | 76 | 0 | 30 | 106 |
| [siom/common/src/main/java/com/crrc/common/bean/response/CaseDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/CaseDetailResponse.java) | Java | 123 | 33 | 47 | 203 |
| [siom/common/src/main/java/com/crrc/common/bean/response/CaseListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/CaseListResponse.java) | Java | 101 | 45 | 41 | 187 |
| [siom/common/src/main/java/com/crrc/common/bean/response/ChatInfo.java](/siom/common/src/main/java/com/crrc/common/bean/response/ChatInfo.java) | Java | 45 | 0 | 14 | 59 |
| [siom/common/src/main/java/com/crrc/common/bean/response/ChatMessage.java](/siom/common/src/main/java/com/crrc/common/bean/response/ChatMessage.java) | Java | 39 | 0 | 14 | 53 |
| [siom/common/src/main/java/com/crrc/common/bean/response/CurrentDirsResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/CurrentDirsResponse.java) | Java | 26 | 0 | 11 | 37 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DeviceDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/DeviceDetailResponse.java) | Java | 101 | 0 | 33 | 134 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DeviceInfo.java](/siom/common/src/main/java/com/crrc/common/bean/response/DeviceInfo.java) | Java | 102 | 3 | 32 | 137 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DirsListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/DirsListResponse.java) | Java | 11 | 0 | 7 | 18 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DocumentRecord.java](/siom/common/src/main/java/com/crrc/common/bean/response/DocumentRecord.java) | Java | 122 | 0 | 36 | 158 |
| [siom/common/src/main/java/com/crrc/common/bean/response/EmergencyDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/EmergencyDetailResponse.java) | Java | 12 | 0 | 5 | 17 |
| [siom/common/src/main/java/com/crrc/common/bean/response/EmergencyEventListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/EmergencyEventListResponse.java) | Java | 41 | 3 | 15 | 59 |
| [siom/common/src/main/java/com/crrc/common/bean/response/EmergencyOrderListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/EmergencyOrderListResponse.java) | Java | 34 | 0 | 13 | 47 |
| [siom/common/src/main/java/com/crrc/common/bean/response/EvenBusGoLoginData.java](/siom/common/src/main/java/com/crrc/common/bean/response/EvenBusGoLoginData.java) | Java | 7 | 5 | 3 | 15 |
| [siom/common/src/main/java/com/crrc/common/bean/response/FaultDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/FaultDetailResponse.java) | Java | 10 | 0 | 4 | 14 |
| [siom/common/src/main/java/com/crrc/common/bean/response/FaultOrderListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/FaultOrderListResponse.java) | Java | 34 | 0 | 12 | 46 |
| [siom/common/src/main/java/com/crrc/common/bean/response/FaultReportResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/FaultReportResponse.java) | Java | 55 | 3 | 19 | 77 |
| [siom/common/src/main/java/com/crrc/common/bean/response/FilesByLabelResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/FilesByLabelResponse.java) | Java | 11 | 0 | 7 | 18 |
| [siom/common/src/main/java/com/crrc/common/bean/response/GroupMemberInfoResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/GroupMemberInfoResponse.java) | Java | 24 | 0 | 8 | 32 |
| [siom/common/src/main/java/com/crrc/common/bean/response/LabelListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/LabelListResponse.java) | Java | 17 | 0 | 6 | 23 |
| [siom/common/src/main/java/com/crrc/common/bean/response/LineDevice.java](/siom/common/src/main/java/com/crrc/common/bean/response/LineDevice.java) | Java | 31 | 0 | 11 | 42 |
| [siom/common/src/main/java/com/crrc/common/bean/response/LineInfoResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/LineInfoResponse.java) | Java | 40 | 3 | 14 | 57 |
| [siom/common/src/main/java/com/crrc/common/bean/response/LineTool.java](/siom/common/src/main/java/com/crrc/common/bean/response/LineTool.java) | Java | 31 | 0 | 11 | 42 |
| [siom/common/src/main/java/com/crrc/common/bean/response/LoginBean.java](/siom/common/src/main/java/com/crrc/common/bean/response/LoginBean.java) | Java | 31 | 0 | 11 | 42 |
| [siom/common/src/main/java/com/crrc/common/bean/response/MembersResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/MembersResponse.java) | Java | 38 | 0 | 17 | 55 |
| [siom/common/src/main/java/com/crrc/common/bean/response/OrderFilterParamResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/OrderFilterParamResponse.java) | Java | 59 | 0 | 24 | 83 |
| [siom/common/src/main/java/com/crrc/common/bean/response/PickMaterialResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/PickMaterialResponse.java) | Java | 63 | 3 | 22 | 88 |
| [siom/common/src/main/java/com/crrc/common/bean/response/PlanOrderListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/PlanOrderListResponse.java) | Java | 39 | 3 | 15 | 57 |
| [siom/common/src/main/java/com/crrc/common/bean/response/ProcedureResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/ProcedureResponse.java) | Java | 83 | 2 | 30 | 115 |
| [siom/common/src/main/java/com/crrc/common/bean/response/SearchFileResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/SearchFileResponse.java) | Java | 11 | 0 | 7 | 18 |
| [siom/common/src/main/java/com/crrc/common/bean/response/SearchResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/SearchResponse.java) | Java | 96 | 3 | 31 | 130 |
| [siom/common/src/main/java/com/crrc/common/bean/response/StationAlarmResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/StationAlarmResponse.java) | Java | 36 | 3 | 13 | 52 |
| [siom/common/src/main/java/com/crrc/common/bean/response/StoreInfoResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/StoreInfoResponse.java) | Java | 47 | 3 | 16 | 66 |
| [siom/common/src/main/java/com/crrc/common/bean/response/TaskOverviewResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/TaskOverviewResponse.java) | Java | 36 | 3 | 13 | 52 |
| [siom/common/src/main/java/com/crrc/common/bean/response/TempOrderListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/TempOrderListResponse.java) | Java | 34 | 0 | 11 | 45 |
| [siom/common/src/main/java/com/crrc/common/bean/response/TempWorkOrderParamResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/TempWorkOrderParamResponse.java) | Java | 171 | 0 | 53 | 224 |
| [siom/common/src/main/java/com/crrc/common/bean/response/TokenIndoData.java](/siom/common/src/main/java/com/crrc/common/bean/response/TokenIndoData.java) | Java | 150 | 5 | 37 | 192 |
| [siom/common/src/main/java/com/crrc/common/bean/response/UserInfoResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/UserInfoResponse.java) | Java | 66 | 0 | 20 | 86 |
| [siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderCountResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderCountResponse.java) | Java | 36 | 3 | 14 | 53 |
| [siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderDetailResponse.java) | Java | 219 | 7 | 79 | 305 |
| [siom/common/src/main/java/com/crrc/common/bean/response/WorkorderStatics.java](/siom/common/src/main/java/com/crrc/common/bean/response/WorkorderStatics.java) | Java | 31 | 0 | 11 | 42 |
| [siom/common/src/main/java/com/crrc/common/observer/MvvmDataObserver.java](/siom/common/src/main/java/com/crrc/common/observer/MvvmDataObserver.java) | Java | 13 | 5 | 7 | 25 |
| [siom/common/src/main/java/com/crrc/common/ui/CustomProgressDialog.java](/siom/common/src/main/java/com/crrc/common/ui/CustomProgressDialog.java) | Java | 44 | 11 | 15 | 70 |
| [siom/common/src/main/java/com/crrc/common/utils/EventBusUtils.java](/siom/common/src/main/java/com/crrc/common/utils/EventBusUtils.java) | Java | 36 | 46 | 15 | 97 |
| [siom/common/src/main/java/com/crrc/common/utils/GetRequestBody.java](/siom/common/src/main/java/com/crrc/common/utils/GetRequestBody.java) | Java | 16 | 5 | 9 | 30 |
| [siom/common/src/main/java/com/crrc/common/utils/GsonUtil.java](/siom/common/src/main/java/com/crrc/common/utils/GsonUtil.java) | Java | 70 | 51 | 17 | 138 |
| [siom/common/src/main/java/com/crrc/common/utils/LiveDataUtils.java](/siom/common/src/main/java/com/crrc/common/utils/LiveDataUtils.java) | Java | 39 | 12 | 13 | 64 |
| [siom/common/src/main/java/com/crrc/common/utils/MMkvUtil.java](/siom/common/src/main/java/com/crrc/common/utils/MMkvUtil.java) | Java | 81 | 42 | 24 | 147 |
| [siom/common/src/main/java/com/crrc/common/utils/PrefsUtil.java](/siom/common/src/main/java/com/crrc/common/utils/PrefsUtil.java) | Java | 63 | 0 | 24 | 87 |
| [siom/common/src/main/java/com/crrc/common/utils/ToastUtil.java](/siom/common/src/main/java/com/crrc/common/utils/ToastUtil.java) | Java | 32 | 8 | 10 | 50 |
| [siom/common/src/main/res/drawable/ic\_launcher\_background.xml](/siom/common/src/main/res/drawable/ic_launcher_background.xml) | XML | 170 | 0 | 1 | 171 |
| [siom/common/src/main/res/drawable/ic\_launcher\_foreground.xml](/siom/common/src/main/res/drawable/ic_launcher_foreground.xml) | XML | 30 | 0 | 0 | 30 |
| [siom/common/src/main/res/layout/layout\_loading.xml](/siom/common/src/main/res/layout/layout_loading.xml) | XML | 10 | 0 | 3 | 13 |
| [siom/common/src/main/res/mipmap-anydpi/ic\_launcher.xml](/siom/common/src/main/res/mipmap-anydpi/ic_launcher.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/common/src/main/res/mipmap-anydpi/ic\_launcher\_round.xml](/siom/common/src/main/res/mipmap-anydpi/ic_launcher_round.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/common/src/main/res/values/colors.xml](/siom/common/src/main/res/values/colors.xml) | XML | 10 | 0 | 0 | 10 |
| [siom/common/src/main/res/values/strings.xml](/siom/common/src/main/res/values/strings.xml) | XML | 4 | 0 | 0 | 4 |
| [siom/gradle.properties](/siom/gradle.properties) | Properties | 4 | 19 | 0 | 23 |
| [siom/gradle/wrapper/gradle-wrapper.properties](/siom/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 1 | 1 | 7 |
| [siom/gradlew.bat](/siom/gradlew.bat) | Batch | 39 | 29 | 22 | 90 |
| [siom/network/build.gradle](/siom/network/build.gradle) | Groovy | 32 | 0 | 7 | 39 |
| [siom/network/src/main/AndroidManifest.xml](/siom/network/src/main/AndroidManifest.xml) | XML | 3 | 0 | 3 | 6 |
| [siom/network/src/main/java/com/crrc/network/NetworkApi.java](/siom/network/src/main/java/com/crrc/network/NetworkApi.java) | Java | 55 | 4 | 13 | 72 |
| [siom/network/src/main/java/com/crrc/network/api/ApiInterface.java](/siom/network/src/main/java/com/crrc/network/api/ApiInterface.java) | Java | 246 | 285 | 83 | 614 |
| [siom/network/src/main/java/com/crrc/network/api/ApiInterface2.java](/siom/network/src/main/java/com/crrc/network/api/ApiInterface2.java) | Java | 157 | 232 | 60 | 449 |
| [siom/network/src/main/java/com/crrc/network/api/ApiInterface3.java](/siom/network/src/main/java/com/crrc/network/api/ApiInterface3.java) | Java | 202 | 243 | 66 | 511 |
| [siom/network/src/main/java/com/crrc/network/base/INetworkRequiredInfo.java](/siom/network/src/main/java/com/crrc/network/base/INetworkRequiredInfo.java) | Java | 8 | 23 | 7 | 38 |
| [siom/network/src/main/java/com/crrc/network/base/NetworkApi.java](/siom/network/src/main/java/com/crrc/network/base/NetworkApi.java) | Java | 93 | 42 | 15 | 150 |
| [siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java](/siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java) | Java | 35 | 7 | 11 | 53 |
| [siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonResponseInterceptor.java](/siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonResponseInterceptor.java) | Java | 12 | 4 | 8 | 24 |
| [siom/network/src/main/java/com/crrc/network/commoninterceptor/DynamicTimeoutInterceptor.java](/siom/network/src/main/java/com/crrc/network/commoninterceptor/DynamicTimeoutInterceptor.java) | Java | 38 | 4 | 10 | 52 |
| [siom/network/src/main/java/com/crrc/network/download/DownloadCallBack.java](/siom/network/src/main/java/com/crrc/network/download/DownloadCallBack.java) | Java | 8 | 29 | 9 | 46 |
| [siom/network/src/main/java/com/crrc/network/download/DownloadInfo.java](/siom/network/src/main/java/com/crrc/network/download/DownloadInfo.java) | Java | 60 | 0 | 22 | 82 |
| [siom/network/src/main/java/com/crrc/network/download/DownloadProgressHandler.java](/siom/network/src/main/java/com/crrc/network/download/DownloadProgressHandler.java) | Java | 47 | 14 | 13 | 74 |
| [siom/network/src/main/java/com/crrc/network/download/FileDownloader.java](/siom/network/src/main/java/com/crrc/network/download/FileDownloader.java) | Java | 145 | 12 | 21 | 178 |
| [siom/network/src/main/java/com/crrc/network/environment/EnvironmentActivity.java](/siom/network/src/main/java/com/crrc/network/environment/EnvironmentActivity.java) | Java | 57 | 10 | 15 | 82 |
| [siom/network/src/main/java/com/crrc/network/environment/IEnvironment.java](/siom/network/src/main/java/com/crrc/network/environment/IEnvironment.java) | Java | 4 | 9 | 3 | 16 |
| [siom/network/src/main/java/com/crrc/network/errorhandler/ExceptionHandle.java](/siom/network/src/main/java/com/crrc/network/errorhandler/ExceptionHandle.java) | Java | 124 | 24 | 19 | 167 |
| [siom/network/src/main/java/com/crrc/network/errorhandler/HttpErrorHandler.java](/siom/network/src/main/java/com/crrc/network/errorhandler/HttpErrorHandler.java) | Java | 9 | 6 | 4 | 19 |
| [siom/network/src/main/java/com/crrc/network/observer/BaseObserver.java](/siom/network/src/main/java/com/crrc/network/observer/BaseObserver.java) | Java | 38 | 5 | 8 | 51 |
| [siom/network/src/main/java/com/crrc/network/observer/BaseObserverForBasic.java](/siom/network/src/main/java/com/crrc/network/observer/BaseObserverForBasic.java) | Java | 37 | 4 | 8 | 49 |
| [siom/network/src/main/java/com/crrc/network/upload/FileProgressRequestBody.java](/siom/network/src/main/java/com/crrc/network/upload/FileProgressRequestBody.java) | Java | 48 | 15 | 13 | 76 |
| [siom/network/src/main/res/drawable/ic\_controls.xml](/siom/network/src/main/res/drawable/ic_controls.xml) | XML | 15 | 0 | 1 | 16 |
| [siom/network/src/main/res/layout/activity\_environment.xml](/siom/network/src/main/res/layout/activity_environment.xml) | XML | 7 | 0 | 0 | 7 |
| [siom/network/src/main/res/mipmap-anydpi/ic\_launcher.xml](/siom/network/src/main/res/mipmap-anydpi/ic_launcher.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/network/src/main/res/mipmap-anydpi/ic\_launcher\_round.xml](/siom/network/src/main/res/mipmap-anydpi/ic_launcher_round.xml) | XML | 6 | 0 | 0 | 6 |
| [siom/network/src/main/res/values/array.xml](/siom/network/src/main/res/values/array.xml) | XML | 11 | 0 | 1 | 12 |
| [siom/network/src/main/res/values/colors.xml](/siom/network/src/main/res/values/colors.xml) | XML | 10 | 0 | 0 | 10 |
| [siom/network/src/main/res/values/strings.xml](/siom/network/src/main/res/values/strings.xml) | XML | 3 | 0 | 0 | 3 |
| [siom/network/src/main/res/xml/environment\_preference.xml](/siom/network/src/main/res/xml/environment_preference.xml) | XML | 11 | 0 | 2 | 13 |
| [siom/readme.md](/siom/readme.md) | Markdown | 62 | 0 | 17 | 79 |
| [siom/settings.gradle](/siom/settings.gradle) | Groovy | 41 | 3 | 3 | 47 |
| [siom/tools/llm\_api.py](/siom/tools/llm_api.py) | Python | 225 | 14 | 33 | 272 |
| [siom/tools/screenshot\_utils.py](/siom/tools/screenshot_utils.py) | Python | 44 | 2 | 10 | 56 |
| [siom/tools/search\_engine.py](/siom/tools/search_engine.py) | Python | 64 | 1 | 15 | 80 |
| [siom/tools/web\_scraper.py](/siom/tools/web_scraper.py) | Python | 157 | 20 | 30 | 207 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)