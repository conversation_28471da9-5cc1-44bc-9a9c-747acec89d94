# Diff Details

Date : 2025-07-04 15:25:40

Directory c:\\Users\\<USER>\\Desktop\\app\\siom

Total : 50 files,  2335 codes, 631 comments, 547 blanks, all 3513 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/details.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/details.md) | Markdown | 123 | 0 | 6 | 129 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/diff-details.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/diff-details.md) | Markdown | 9 | 0 | 6 | 15 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/diff.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/diff.md) | Markdown | 12 | 0 | 7 | 19 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/results.json](/siom/.VSCodeCounter/2025-04-14_14-24-47/results.json) | JSON | 1 | 0 | 0 | 1 |
| [siom/.VSCodeCounter/2025-04-14\_14-24-47/results.md](/siom/.VSCodeCounter/2025-04-14_14-24-47/results.md) | Markdown | 90 | 0 | 7 | 97 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/details.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/details.md) | Markdown | 157 | 0 | 6 | 163 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/diff-details.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/diff-details.md) | Markdown | 70 | 0 | 6 | 76 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/diff.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/diff.md) | Markdown | 69 | 0 | 7 | 76 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/results.json](/siom/.VSCodeCounter/2025-05-15_10-14-38/results.json) | JSON | 1 | 0 | 0 | 1 |
| [siom/.VSCodeCounter/2025-05-15\_10-14-38/results.md](/siom/.VSCodeCounter/2025-05-15_10-14-38/results.md) | Markdown | 97 | 0 | 7 | 104 |
| [siom/.idea/AugmentWebviewStateStore.xml](/siom/.idea/AugmentWebviewStateStore.xml) | XML | 10 | 0 | 0 | 10 |
| [siom/app/build.gradle](/siom/app/build.gradle) | Groovy | 10 | 2 | 1 | 13 |
| [siom/app/src/main/AndroidManifest.xml](/siom/app/src/main/AndroidManifest.xml) | XML | 37 | 1 | 0 | 38 |
| [siom/app/src/main/java/com/crrc/siom/AppApplication.java](/siom/app/src/main/java/com/crrc/siom/AppApplication.java) | Java | 42 | 1 | 7 | 50 |
| [siom/app/src/main/java/com/crrc/siom/ui/qrcode/QRScannerActivity.java](/siom/app/src/main/java/com/crrc/siom/ui/qrcode/QRScannerActivity.java) | Java | 150 | 17 | 25 | 192 |
| [siom/app/src/main/res/drawable/ic\_arrow\_back.xml](/siom/app/src/main/res/drawable/ic_arrow_back.xml) | XML | -1 | 0 | 0 | -1 |
| [siom/app/src/main/res/drawable/ic\_flashlight\_off.xml](/siom/app/src/main/res/drawable/ic_flashlight_off.xml) | XML | 9 | 0 | 0 | 9 |
| [siom/app/src/main/res/drawable/ic\_flashlight\_on.xml](/siom/app/src/main/res/drawable/ic_flashlight_on.xml) | XML | 12 | 0 | 0 | 12 |
| [siom/app/src/main/res/drawable/round\_button\_bg.xml](/siom/app/src/main/res/drawable/round_button_bg.xml) | XML | 5 | 0 | 0 | 5 |
| [siom/app/src/main/res/layout/activity\_qr\_scanner.xml](/siom/app/src/main/res/layout/activity_qr_scanner.xml) | XML | 55 | 4 | 9 | 68 |
| [siom/app/src/main/res/xml/file\_paths.xml](/siom/app/src/main/res/xml/file_paths.xml) | XML | 13 | 0 | 0 | 13 |
| [siom/app/src/main/res/xml/network\_security\_config.xml](/siom/app/src/main/res/xml/network_security_config.xml) | XML | 4 | 0 | 1 | 5 |
| [siom/common/build.gradle](/siom/common/build.gradle) | Groovy | 1 | 0 | 0 | 1 |
| [siom/common/src/main/java/com/crrc/common/Constant.java](/siom/common/src/main/java/com/crrc/common/Constant.java) | Java | 5 | 3 | 5 | 13 |
| [siom/common/src/main/java/com/crrc/common/bean/request/QRcodeRequest.java](/siom/common/src/main/java/com/crrc/common/bean/request/QRcodeRequest.java) | Java | 17 | 0 | 6 | 23 |
| [siom/common/src/main/java/com/crrc/common/bean/response/AddressBookListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/AddressBookListResponse.java) | Java | 101 | 0 | 39 | 140 |
| [siom/common/src/main/java/com/crrc/common/bean/response/CaseDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/CaseDetailResponse.java) | Java | 37 | -3 | 12 | 46 |
| [siom/common/src/main/java/com/crrc/common/bean/response/ChatInfo.java](/siom/common/src/main/java/com/crrc/common/bean/response/ChatInfo.java) | Java | 45 | 0 | 14 | 59 |
| [siom/common/src/main/java/com/crrc/common/bean/response/ChatMessage.java](/siom/common/src/main/java/com/crrc/common/bean/response/ChatMessage.java) | Java | 39 | 0 | 14 | 53 |
| [siom/common/src/main/java/com/crrc/common/bean/response/CurrentDirsResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/CurrentDirsResponse.java) | Java | 26 | 0 | 11 | 37 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DeviceDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/DeviceDetailResponse.java) | Java | 101 | 0 | 33 | 134 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DirsListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/DirsListResponse.java) | Java | 11 | 0 | 7 | 18 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DocumentRecord.java](/siom/common/src/main/java/com/crrc/common/bean/response/DocumentRecord.java) | Java | 122 | 0 | 36 | 158 |
| [siom/common/src/main/java/com/crrc/common/bean/response/DocumentResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/DocumentResponse.java) | Java | -44 | 0 | -16 | -60 |
| [siom/common/src/main/java/com/crrc/common/bean/response/EmergencyEventListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/EmergencyEventListResponse.java) | Java | -6 | 0 | -3 | -9 |
| [siom/common/src/main/java/com/crrc/common/bean/response/FilesByLabelResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/FilesByLabelResponse.java) | Java | 11 | 0 | 7 | 18 |
| [siom/common/src/main/java/com/crrc/common/bean/response/GroupMemberInfoResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/GroupMemberInfoResponse.java) | Java | 24 | 0 | 8 | 32 |
| [siom/common/src/main/java/com/crrc/common/bean/response/LabelListResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/LabelListResponse.java) | Java | 17 | 0 | 6 | 23 |
| [siom/common/src/main/java/com/crrc/common/bean/response/PickMaterialResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/PickMaterialResponse.java) | Java | 8 | 0 | 3 | 11 |
| [siom/common/src/main/java/com/crrc/common/bean/response/ProcedureResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/ProcedureResponse.java) | Java | 83 | 2 | 30 | 115 |
| [siom/common/src/main/java/com/crrc/common/bean/response/SearchFileResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/SearchFileResponse.java) | Java | 11 | 0 | 7 | 18 |
| [siom/common/src/main/java/com/crrc/common/bean/response/SearchResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/SearchResponse.java) | Java | 47 | 0 | 15 | 62 |
| [siom/common/src/main/java/com/crrc/common/bean/response/TempWorkOrderParamResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/TempWorkOrderParamResponse.java) | Java | 171 | 0 | 53 | 224 |
| [siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderDetailResponse.java](/siom/common/src/main/java/com/crrc/common/bean/response/WorkOrderDetailResponse.java) | Java | 8 | 0 | 3 | 11 |
| [siom/common/src/main/java/com/crrc/common/bean/response/WorkorderStatics.java](/siom/common/src/main/java/com/crrc/common/bean/response/WorkorderStatics.java) | Java | 31 | 0 | 11 | 42 |
| [siom/network/src/main/java/com/crrc/network/api/ApiInterface.java](/siom/network/src/main/java/com/crrc/network/api/ApiInterface.java) | Java | 127 | 127 | 26 | 280 |
| [siom/network/src/main/java/com/crrc/network/api/ApiInterface2.java](/siom/network/src/main/java/com/crrc/network/api/ApiInterface2.java) | Java | 157 | 232 | 60 | 449 |
| [siom/network/src/main/java/com/crrc/network/api/ApiInterface3.java](/siom/network/src/main/java/com/crrc/network/api/ApiInterface3.java) | Java | 202 | 243 | 66 | 511 |
| [siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java](/siom/network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java) | Java | 0 | 2 | 0 | 2 |
| [siom/readme.md](/siom/readme.md) | Markdown | 8 | 0 | -1 | 7 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details