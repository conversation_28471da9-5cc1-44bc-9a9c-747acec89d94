"filename", "language", "Markdown", "Java", "XML", "Properties", "JSON", "Groovy", "comment", "blank", "total"
"c:\Users\<USER>\Desktop\app\siom\.idea\appInsightsSettings.xml", "XML", 0, 0, 15, 0, 0, 0, 0, 0, 15
"c:\Users\<USER>\Desktop\app\siom\.idea\deploymentTargetSelector.xml", "XML", 0, 0, 11, 0, 0, 0, 0, 0, 11
"c:\Users\<USER>\Desktop\app\siom\app\build.gradle", "Groovy", 0, 0, 0, 0, 0, 18, 1, 3, 22
"c:\Users\<USER>\Desktop\app\siom\app\libs\build.properties", "Properties", 0, 0, 0, 10, 0, 0, 0, 1, 11
"c:\Users\<USER>\Desktop\app\siom\app\release\output-metadata.json", "JSON", 0, 0, 0, 0, 37, 0, 0, 0, 37
"c:\Users\<USER>\Desktop\app\siom\app\src\main\AndroidManifest.xml", "XML", 0, 0, 12, 0, 0, 0, 3, 0, 15
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\AppApplication.java", "Java", 0, 9, 0, 0, 0, 0, 4, 0, 13
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\data\model\AlarmInfo.java", "Java", 0, 29, 0, 0, 0, 0, 0, 8, 37
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\data\model\StorageItem.java", "Java", 0, 37, 0, 0, 0, 0, 0, 10, 47
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\AlarmFragment.java", "Java", 0, 90, 0, 0, 0, 0, 0, 20, 110
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\DetailPanelManager.java", "Java", 0, 128, 0, 0, 0, 0, 43, 29, 200
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\LineMapJavaActivity.java", "Java", 0, 310, 0, 0, 0, 0, 68, 54, 432
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\LineMapJavaViewModel.java", "Java", 0, 169, 0, 0, 0, 0, 36, 40, 245
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\MapMarkerManager.java", "Java", 0, 223, 0, 0, 0, 0, 61, 55, 339
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\MapMarkerUtils.java", "Java", 0, 60, 0, 0, 0, 0, 13, 16, 89
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\StationDetailAdapter.java", "Java", 0, 44, 0, 0, 0, 0, 0, 8, 52
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\StorageFragment.java", "Java", 0, 118, 0, 0, 0, 0, 0, 22, 140
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_current_location.xml", "XML", 0, 0, 7, 0, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_info_window.xml", "XML", 0, 0, 7, 0, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_marker_info.xml", "XML", 0, 0, 6, 0, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_marker_info_bubble.xml", "XML", 0, 0, 15, 0, 0, 0, 0, 0, 15
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_marker_info_oval.xml", "XML", 0, 0, 8, 0, 0, 0, 0, 0, 8
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_arrow_back.xml", "XML", 0, 0, 11, 0, 0, 0, 0, 0, 11
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_build.xml", "XML", 0, 0, 11, 0, 0, 0, 0, 0, 11
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_dot.xml", "XML", 0, 0, 7, 0, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_warning.xml", "XML", 0, 0, 11, 0, 0, 0, 0, 0, 11
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\activity_line_map.xml", "XML", 0, 0, 135, 0, 0, 0, 0, 18, 153
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\custom_info_window.xml", "XML", 0, 0, 26, 0, 0, 0, 0, 3, 29
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\custom_marker_info.xml", "XML", 0, 0, 22, 0, 0, 0, 0, 3, 25
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\fragment_alarm.xml", "XML", 0, 0, 18, 0, 0, 0, 0, 2, 20
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\fragment_storage.xml", "XML", 0, 0, 18, 0, 0, 0, 0, 2, 20
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\item_alarm.xml", "XML", 0, 0, 56, 0, 0, 0, 0, 7, 63
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\item_storage.xml", "XML", 0, 0, 95, 0, 0, 0, 0, 11, 106
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values-night\themes.xml", "XML", 0, 0, -20, 0, 0, 0, -1, -7, -28
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values\colors.xml", "XML", 0, 0, 22, 0, 0, 0, 6, 6, 34
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values\strings.xml", "XML", 0, 0, 1, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values\themes.xml", "XML", 0, 0, -9, 0, 0, 0, 0, 1, -8
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\Constant.java", "Java", 0, 3, 0, 0, 0, 0, -4, 1, 0
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\AlarmInfoResponse.java", "Java", 0, -36, 0, 0, 0, 0, -3, -13, -52
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\AlarmResponse.java", "Java", 0, 20, 0, 0, 0, 0, 0, 5, 25
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\DeviceInfo.java", "Java", 0, 102, 0, 0, 0, 0, 3, 32, 137
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\EmergencyDetailResponse.java", "Java", 0, 12, 0, 0, 0, 0, 0, 5, 17
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\EmergencyEventListResponse.java", "Java", 0, 47, 0, 0, 0, 0, 3, 18, 68
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\EmergencyOrderListResponse.java", "Java", 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\FaultDetailResponse.java", "Java", 0, 10, 0, 0, 0, 0, 0, 4, 14
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LineDevice.java", "Java", 0, 31, 0, 0, 0, 0, 0, 11, 42
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LineInfoResponse.java", "Java", 0, -33, 0, 0, 0, 0, 0, -11, -44
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LineTool.java", "Java", 0, 31, 0, 0, 0, 0, 0, 11, 42
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LoginBean.java", "Java", 0, 31, 0, 0, 0, 0, 0, 11, 42
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\MembersResponse.java", "Java", 0, 38, 0, 0, 0, 0, 0, 17, 55
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\OrderFilterParamResponse.java", "Java", 0, 59, 0, 0, 0, 0, 0, 24, 83
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\UserInfoResponse.java", "Java", 0, 66, 0, 0, 0, 0, 0, 20, 86
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\GetRequestBody.java", "Java", 0, 16, 0, 0, 0, 0, 5, 9, 30
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\values-night\themes.xml", "XML", 0, 0, -11, 0, 0, 0, -5, 0, -16
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\values\themes.xml", "XML", 0, 0, -11, 0, 0, 0, -5, 0, -16
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\api\ApiInterface.java", "Java", 0, 24, 0, 0, 0, 0, 66, 18, 108
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\base\NetworkApi.java", "Java", 0, 1, 0, 0, 0, 0, -1, 0, 0
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\commoninterceptor\CommonRequestInterceptor.java", "Java", 0, 1, 0, 0, 0, 0, 1, 0, 2
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\values-night\themes.xml", "XML", 0, 0, -11, 0, 0, 0, -5, 0, -16
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\values\themes.xml", "XML", 0, 0, -11, 0, 0, 0, -5, 0, -16
"c:\Users\<USER>\Desktop\app\siom\readme.md", "Markdown", 50, 0, 0, 0, 0, 0, 0, 15, 65
"Total", "-", 50, 1640, 441, 10, 37, 18, 284, 490, 2970