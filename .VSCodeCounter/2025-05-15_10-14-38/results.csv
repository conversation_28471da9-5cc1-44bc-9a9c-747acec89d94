"filename", "language", "Batch", "Groovy", "Markdown", "Python", "Properties", "XML", "Java", "JSON", "comment", "blank", "total"
"c:\Users\<USER>\Desktop\app\siom\.idea\AndroidProjectSystem.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\.idea\appInsightsSettings.xml", "XML", 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 41
"c:\Users\<USER>\Desktop\app\siom\.idea\compiler.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\.idea\deploymentTargetSelector.xml", "XML", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 30
"c:\Users\<USER>\Desktop\app\siom\.idea\gradle.xml", "XML", 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 21
"c:\Users\<USER>\Desktop\app\siom\.idea\inspectionProfiles\Project_Default.xml", "XML", 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 61
"c:\Users\<USER>\Desktop\app\siom\.idea\kotlinc.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\.idea\migrations.xml", "XML", 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 10
"c:\Users\<USER>\Desktop\app\siom\.idea\misc.xml", "XML", 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 13
"c:\Users\<USER>\Desktop\app\siom\.idea\runConfigurations.xml", "XML", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 17
"c:\Users\<USER>\Desktop\app\siom\.idea\vcs.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\app\build.gradle", "Groovy", 0, 69, 0, 0, 0, 0, 0, 0, 1, 9, 79
"c:\Users\<USER>\Desktop\app\siom\app\libs\build.properties", "Properties", 0, 0, 0, 0, 10, 0, 0, 0, 0, 1, 11
"c:\Users\<USER>\Desktop\app\siom\app\release\output-metadata.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 37, 0, 0, 37
"c:\Users\<USER>\Desktop\app\siom\app\src\main\AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 88, 0, 0, 3, 1, 92
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\AppApplication.java", "Java", 0, 0, 0, 0, 0, 0, 33, 0, 22, 15, 70
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\NetworkRequestInfo.java", "Java", 0, 0, 0, 0, 0, 0, 25, 0, 3, 12, 40
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\data\model\AlarmInfo.java", "Java", 0, 0, 0, 0, 0, 0, 29, 0, 0, 8, 37
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\data\model\StorageItem.java", "Java", 0, 0, 0, 0, 0, 0, 37, 0, 0, 10, 47
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\AlarmFragment.java", "Java", 0, 0, 0, 0, 0, 0, 90, 0, 0, 20, 110
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\DetailPanelManager.java", "Java", 0, 0, 0, 0, 0, 0, 128, 0, 43, 29, 200
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\LineMapJavaActivity.java", "Java", 0, 0, 0, 0, 0, 0, 310, 0, 68, 54, 432
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\LineMapJavaViewModel.java", "Java", 0, 0, 0, 0, 0, 0, 169, 0, 36, 40, 245
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\MapMarkerManager.java", "Java", 0, 0, 0, 0, 0, 0, 223, 0, 61, 55, 339
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\MapMarkerUtils.java", "Java", 0, 0, 0, 0, 0, 0, 60, 0, 13, 16, 89
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\StationDetailAdapter.java", "Java", 0, 0, 0, 0, 0, 0, 44, 0, 0, 8, 52
"c:\Users\<USER>\Desktop\app\siom\app\src\main\java\com\crrc\siom\ui\line\StorageFragment.java", "Java", 0, 0, 0, 0, 0, 0, 118, 0, 0, 22, 140
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_current_location.xml", "XML", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_info_window.xml", "XML", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_marker_info.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_marker_info_bubble.xml", "XML", 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 15
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\bg_marker_info_oval.xml", "XML", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_arrow_back.xml", "XML", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 11
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_build.xml", "XML", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 11
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_dashboard_black_24dp.xml", "XML", 0, 0, 0, 0, 0, 9, 0, 0, 0, 1, 10
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_dot.xml", "XML", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_home_black_24dp.xml", "XML", 0, 0, 0, 0, 0, 9, 0, 0, 0, 1, 10
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_launcher_background.xml", "XML", 0, 0, 0, 0, 0, 170, 0, 0, 0, 1, 171
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_launcher_foreground.xml", "XML", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 30
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_notifications_black_24dp.xml", "XML", 0, 0, 0, 0, 0, 9, 0, 0, 0, 1, 10
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\drawable\ic_warning.xml", "XML", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 11
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\activity_line_map.xml", "XML", 0, 0, 0, 0, 0, 135, 0, 0, 0, 18, 153
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\custom_info_window.xml", "XML", 0, 0, 0, 0, 0, 26, 0, 0, 0, 3, 29
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\custom_marker_info.xml", "XML", 0, 0, 0, 0, 0, 22, 0, 0, 0, 3, 25
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\fragment_alarm.xml", "XML", 0, 0, 0, 0, 0, 18, 0, 0, 0, 2, 20
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\fragment_storage.xml", "XML", 0, 0, 0, 0, 0, 18, 0, 0, 0, 2, 20
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\item_alarm.xml", "XML", 0, 0, 0, 0, 0, 56, 0, 0, 0, 7, 63
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\layout\item_storage.xml", "XML", 0, 0, 0, 0, 0, 95, 0, 0, 0, 11, 106
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\menu\bottom_nav_menu.xml", "XML", 0, 0, 0, 0, 0, 15, 0, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\mipmap-anydpi\ic_launcher.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values-night\colors.xml", "XML", 0, 0, 0, 0, 0, 17, 0, 0, 6, 5, 28
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values\colors.xml", "XML", 0, 0, 0, 0, 0, 75, 0, 0, 6, 6, 87
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values\dimens.xml", "XML", 0, 0, 0, 0, 0, 4, 0, 0, 1, 0, 5
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values\strings.xml", "XML", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\values\themes.xml", "XML", 0, 0, 0, 0, 0, 13, 0, 0, 0, 2, 15
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\xml\backup_rules.xml", "XML", 0, 0, 0, 0, 0, 3, 0, 0, 10, 0, 13
"c:\Users\<USER>\Desktop\app\siom\app\src\main\res\xml\data_extraction_rules.xml", "XML", 0, 0, 0, 0, 0, 5, 0, 0, 14, 0, 19
"c:\Users\<USER>\Desktop\app\siom\build.gradle", "Groovy", 0, 5, 0, 0, 0, 0, 0, 0, 1, 0, 6
"c:\Users\<USER>\Desktop\app\siom\common\build.gradle", "Groovy", 0, 69, 0, 0, 0, 0, 0, 0, 10, 12, 91
"c:\Users\<USER>\Desktop\app\siom\common\src\main\AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 4, 0, 0, 0, 1, 5
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\BaseApplication.java", "Java", 0, 0, 0, 0, 0, 0, 10, 0, 3, 5, 18
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\BaseResponse.java", "Java", 0, 0, 0, 0, 0, 0, 18, 0, 3, 9, 30
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\Constant.java", "Java", 0, 0, 0, 0, 0, 0, 406, 0, 1164, 141, 1711
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\request\PickMaterialRequest.java", "Java", 0, 0, 0, 0, 0, 0, 37, 0, 3, 13, 53
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\AlarmResponse.java", "Java", 0, 0, 0, 0, 0, 0, 20, 0, 0, 5, 25
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\BaseOrderRecord.java", "Java", 0, 0, 0, 0, 0, 0, 76, 0, 0, 30, 106
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\CaseDetailResponse.java", "Java", 0, 0, 0, 0, 0, 0, 86, 0, 36, 35, 157
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\CaseListResponse.java", "Java", 0, 0, 0, 0, 0, 0, 101, 0, 45, 41, 187
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\DeviceInfo.java", "Java", 0, 0, 0, 0, 0, 0, 102, 0, 3, 32, 137
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\DocumentResponse.java", "Java", 0, 0, 0, 0, 0, 0, 44, 0, 0, 16, 60
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\EmergencyDetailResponse.java", "Java", 0, 0, 0, 0, 0, 0, 12, 0, 0, 5, 17
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\EmergencyEventListResponse.java", "Java", 0, 0, 0, 0, 0, 0, 47, 0, 3, 18, 68
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\EmergencyOrderListResponse.java", "Java", 0, 0, 0, 0, 0, 0, 34, 0, 0, 13, 47
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\EvenBusGoLoginData.java", "Java", 0, 0, 0, 0, 0, 0, 7, 0, 5, 3, 15
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\FaultDetailResponse.java", "Java", 0, 0, 0, 0, 0, 0, 10, 0, 0, 4, 14
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\FaultOrderListResponse.java", "Java", 0, 0, 0, 0, 0, 0, 34, 0, 0, 12, 46
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\FaultReportResponse.java", "Java", 0, 0, 0, 0, 0, 0, 55, 0, 3, 19, 77
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LineDevice.java", "Java", 0, 0, 0, 0, 0, 0, 31, 0, 0, 11, 42
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LineInfoResponse.java", "Java", 0, 0, 0, 0, 0, 0, 40, 0, 3, 14, 57
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LineTool.java", "Java", 0, 0, 0, 0, 0, 0, 31, 0, 0, 11, 42
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\LoginBean.java", "Java", 0, 0, 0, 0, 0, 0, 31, 0, 0, 11, 42
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\MembersResponse.java", "Java", 0, 0, 0, 0, 0, 0, 38, 0, 0, 17, 55
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\OrderFilterParamResponse.java", "Java", 0, 0, 0, 0, 0, 0, 59, 0, 0, 24, 83
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\PickMaterialResponse.java", "Java", 0, 0, 0, 0, 0, 0, 55, 0, 3, 19, 77
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\PlanOrderListResponse.java", "Java", 0, 0, 0, 0, 0, 0, 39, 0, 3, 15, 57
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\SearchResponse.java", "Java", 0, 0, 0, 0, 0, 0, 49, 0, 3, 16, 68
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\StationAlarmResponse.java", "Java", 0, 0, 0, 0, 0, 0, 36, 0, 3, 13, 52
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\StoreInfoResponse.java", "Java", 0, 0, 0, 0, 0, 0, 47, 0, 3, 16, 66
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\TaskOverviewResponse.java", "Java", 0, 0, 0, 0, 0, 0, 36, 0, 3, 13, 52
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\TempOrderListResponse.java", "Java", 0, 0, 0, 0, 0, 0, 34, 0, 0, 11, 45
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\TokenIndoData.java", "Java", 0, 0, 0, 0, 0, 0, 150, 0, 5, 37, 192
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\UserInfoResponse.java", "Java", 0, 0, 0, 0, 0, 0, 66, 0, 0, 20, 86
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\WorkOrderCountResponse.java", "Java", 0, 0, 0, 0, 0, 0, 36, 0, 3, 14, 53
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\bean\response\WorkOrderDetailResponse.java", "Java", 0, 0, 0, 0, 0, 0, 211, 0, 7, 76, 294
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\observer\MvvmDataObserver.java", "Java", 0, 0, 0, 0, 0, 0, 13, 0, 5, 7, 25
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\ui\CustomProgressDialog.java", "Java", 0, 0, 0, 0, 0, 0, 44, 0, 11, 15, 70
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\EventBusUtils.java", "Java", 0, 0, 0, 0, 0, 0, 36, 0, 46, 15, 97
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\GetRequestBody.java", "Java", 0, 0, 0, 0, 0, 0, 16, 0, 5, 9, 30
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\GsonUtil.java", "Java", 0, 0, 0, 0, 0, 0, 70, 0, 51, 17, 138
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\LiveDataUtils.java", "Java", 0, 0, 0, 0, 0, 0, 39, 0, 12, 13, 64
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\MMkvUtil.java", "Java", 0, 0, 0, 0, 0, 0, 81, 0, 42, 24, 147
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\PrefsUtil.java", "Java", 0, 0, 0, 0, 0, 0, 63, 0, 0, 24, 87
"c:\Users\<USER>\Desktop\app\siom\common\src\main\java\com\crrc\common\utils\ToastUtil.java", "Java", 0, 0, 0, 0, 0, 0, 32, 0, 8, 10, 50
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\drawable\ic_launcher_background.xml", "XML", 0, 0, 0, 0, 0, 170, 0, 0, 0, 1, 171
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\drawable\ic_launcher_foreground.xml", "XML", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 30
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\layout\layout_loading.xml", "XML", 0, 0, 0, 0, 0, 10, 0, 0, 0, 3, 13
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\mipmap-anydpi\ic_launcher.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\mipmap-anydpi\ic_launcher_round.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\values\colors.xml", "XML", 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 10
"c:\Users\<USER>\Desktop\app\siom\common\src\main\res\values\strings.xml", "XML", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 4
"c:\Users\<USER>\Desktop\app\siom\gradle.properties", "Properties", 0, 0, 0, 0, 4, 0, 0, 0, 19, 0, 23
"c:\Users\<USER>\Desktop\app\siom\gradle\wrapper\gradle-wrapper.properties", "Properties", 0, 0, 0, 0, 5, 0, 0, 0, 1, 1, 7
"c:\Users\<USER>\Desktop\app\siom\gradlew.bat", "Batch", 39, 0, 0, 0, 0, 0, 0, 0, 29, 22, 90
"c:\Users\<USER>\Desktop\app\siom\network\build.gradle", "Groovy", 0, 32, 0, 0, 0, 0, 0, 0, 0, 7, 39
"c:\Users\<USER>\Desktop\app\siom\network\src\main\AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 3, 0, 0, 0, 3, 6
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\NetworkApi.java", "Java", 0, 0, 0, 0, 0, 0, 55, 0, 4, 13, 72
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\api\ApiInterface.java", "Java", 0, 0, 0, 0, 0, 0, 119, 0, 158, 57, 334
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\base\INetworkRequiredInfo.java", "Java", 0, 0, 0, 0, 0, 0, 8, 0, 23, 7, 38
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\base\NetworkApi.java", "Java", 0, 0, 0, 0, 0, 0, 93, 0, 42, 15, 150
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\commoninterceptor\CommonRequestInterceptor.java", "Java", 0, 0, 0, 0, 0, 0, 35, 0, 5, 11, 51
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\commoninterceptor\CommonResponseInterceptor.java", "Java", 0, 0, 0, 0, 0, 0, 12, 0, 4, 8, 24
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\commoninterceptor\DynamicTimeoutInterceptor.java", "Java", 0, 0, 0, 0, 0, 0, 38, 0, 4, 10, 52
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\download\DownloadCallBack.java", "Java", 0, 0, 0, 0, 0, 0, 8, 0, 29, 9, 46
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\download\DownloadInfo.java", "Java", 0, 0, 0, 0, 0, 0, 60, 0, 0, 22, 82
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\download\DownloadProgressHandler.java", "Java", 0, 0, 0, 0, 0, 0, 47, 0, 14, 13, 74
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\download\FileDownloader.java", "Java", 0, 0, 0, 0, 0, 0, 145, 0, 12, 21, 178
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\environment\EnvironmentActivity.java", "Java", 0, 0, 0, 0, 0, 0, 57, 0, 10, 15, 82
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\environment\IEnvironment.java", "Java", 0, 0, 0, 0, 0, 0, 4, 0, 9, 3, 16
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\errorhandler\ExceptionHandle.java", "Java", 0, 0, 0, 0, 0, 0, 124, 0, 24, 19, 167
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\errorhandler\HttpErrorHandler.java", "Java", 0, 0, 0, 0, 0, 0, 9, 0, 6, 4, 19
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\observer\BaseObserver.java", "Java", 0, 0, 0, 0, 0, 0, 38, 0, 5, 8, 51
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\observer\BaseObserverForBasic.java", "Java", 0, 0, 0, 0, 0, 0, 37, 0, 4, 8, 49
"c:\Users\<USER>\Desktop\app\siom\network\src\main\java\com\crrc\network\upload\FileProgressRequestBody.java", "Java", 0, 0, 0, 0, 0, 0, 48, 0, 15, 13, 76
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\drawable\ic_controls.xml", "XML", 0, 0, 0, 0, 0, 15, 0, 0, 0, 1, 16
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\layout\activity_environment.xml", "XML", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\mipmap-anydpi\ic_launcher.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\mipmap-anydpi\ic_launcher_round.xml", "XML", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\values\array.xml", "XML", 0, 0, 0, 0, 0, 11, 0, 0, 0, 1, 12
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\values\colors.xml", "XML", 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 10
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\values\strings.xml", "XML", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3
"c:\Users\<USER>\Desktop\app\siom\network\src\main\res\xml\environment_preference.xml", "XML", 0, 0, 0, 0, 0, 11, 0, 0, 0, 2, 13
"c:\Users\<USER>\Desktop\app\siom\readme.md", "Markdown", 0, 0, 54, 0, 0, 0, 0, 0, 0, 18, 72
"c:\Users\<USER>\Desktop\app\siom\settings.gradle", "Groovy", 0, 41, 0, 0, 0, 0, 0, 0, 3, 3, 47
"c:\Users\<USER>\Desktop\app\siom\tools\llm_api.py", "Python", 0, 0, 0, 225, 0, 0, 0, 0, 14, 33, 272
"c:\Users\<USER>\Desktop\app\siom\tools\screenshot_utils.py", "Python", 0, 0, 0, 44, 0, 0, 0, 0, 2, 10, 56
"c:\Users\<USER>\Desktop\app\siom\tools\search_engine.py", "Python", 0, 0, 0, 64, 0, 0, 0, 0, 1, 15, 80
"c:\Users\<USER>\Desktop\app\siom\tools\web_scraper.py", "Python", 0, 0, 0, 157, 0, 0, 0, 0, 20, 30, 207
"Total", "-", 39, 216, 54, 490, 19, 1448, 4685, 37, 2239, 1659, 10886