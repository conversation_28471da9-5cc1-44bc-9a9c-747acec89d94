# Diff Summary

Date : 2025-05-15 10:14:38

Directory c:\\Users\\<USER>\\Desktop\\app\\siom

Total : 61 files,  2196 codes, 284 comments, 490 blanks, all 2970 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Java | 30 | 1,640 | 295 | 425 | 2,360 |
| XML | 27 | 441 | -12 | 46 | 475 |
| Markdown | 1 | 50 | 0 | 15 | 65 |
| JSON | 1 | 37 | 0 | 0 | 37 |
| Groovy | 1 | 18 | 1 | 3 | 22 |
| Properties | 1 | 10 | 0 | 1 | 11 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 61 | 2,196 | 284 | 490 | 2,970 |
| . (Files) | 1 | 50 | 0 | 15 | 65 |
| .idea | 2 | 26 | 0 | 0 | 26 |
| app | 35 | 1,741 | 234 | 312 | 2,287 |
| app (Files) | 1 | 18 | 1 | 3 | 22 |
| app\\libs | 1 | 10 | 0 | 1 | 11 |
| app\\release | 1 | 37 | 0 | 0 | 37 |
| app\\src | 32 | 1,676 | 233 | 308 | 2,217 |
| app\\src\\main | 32 | 1,676 | 233 | 308 | 2,217 |
| app\\src\\main (Files) | 1 | 12 | 3 | 0 | 15 |
| app\\src\\main\\java | 11 | 1,217 | 225 | 262 | 1,704 |
| app\\src\\main\\java\\com | 11 | 1,217 | 225 | 262 | 1,704 |
| app\\src\\main\\java\\com\\crrc | 11 | 1,217 | 225 | 262 | 1,704 |
| app\\src\\main\\java\\com\\crrc\\siom | 11 | 1,217 | 225 | 262 | 1,704 |
| app\\src\\main\\java\\com\\crrc\\siom (Files) | 1 | 9 | 4 | 0 | 13 |
| app\\src\\main\\java\\com\\crrc\\siom\\data | 2 | 66 | 0 | 18 | 84 |
| app\\src\\main\\java\\com\\crrc\\siom\\data\\model | 2 | 66 | 0 | 18 | 84 |
| app\\src\\main\\java\\com\\crrc\\siom\\ui | 8 | 1,142 | 221 | 244 | 1,607 |
| app\\src\\main\\java\\com\\crrc\\siom\\ui\\line | 8 | 1,142 | 221 | 244 | 1,607 |
| app\\src\\main\\res | 20 | 447 | 5 | 46 | 498 |
| app\\src\\main\\res\\drawable | 9 | 83 | 0 | 0 | 83 |
| app\\src\\main\\res\\layout | 7 | 370 | 0 | 46 | 416 |
| app\\src\\main\\res\\values | 3 | 14 | 6 | 7 | 27 |
| app\\src\\main\\res\\values-night | 1 | -20 | -1 | -7 | -28 |
| common | 18 | 375 | -6 | 145 | 514 |
| common\\src | 18 | 375 | -6 | 145 | 514 |
| common\\src\\main | 18 | 375 | -6 | 145 | 514 |
| common\\src\\main\\java | 16 | 397 | 4 | 145 | 546 |
| common\\src\\main\\java\\com | 16 | 397 | 4 | 145 | 546 |
| common\\src\\main\\java\\com\\crrc | 16 | 397 | 4 | 145 | 546 |
| common\\src\\main\\java\\com\\crrc\\common | 16 | 397 | 4 | 145 | 546 |
| common\\src\\main\\java\\com\\crrc\\common (Files) | 1 | 3 | -4 | 1 | 0 |
| common\\src\\main\\java\\com\\crrc\\common\\bean | 14 | 378 | 3 | 135 | 516 |
| common\\src\\main\\java\\com\\crrc\\common\\bean\\response | 14 | 378 | 3 | 135 | 516 |
| common\\src\\main\\java\\com\\crrc\\common\\utils | 1 | 16 | 5 | 9 | 30 |
| common\\src\\main\\res | 2 | -22 | -10 | 0 | -32 |
| common\\src\\main\\res\\values | 1 | -11 | -5 | 0 | -16 |
| common\\src\\main\\res\\values-night | 1 | -11 | -5 | 0 | -16 |
| network | 5 | 4 | 56 | 18 | 78 |
| network\\src | 5 | 4 | 56 | 18 | 78 |
| network\\src\\main | 5 | 4 | 56 | 18 | 78 |
| network\\src\\main\\java | 3 | 26 | 66 | 18 | 110 |
| network\\src\\main\\java\\com | 3 | 26 | 66 | 18 | 110 |
| network\\src\\main\\java\\com\\crrc | 3 | 26 | 66 | 18 | 110 |
| network\\src\\main\\java\\com\\crrc\\network | 3 | 26 | 66 | 18 | 110 |
| network\\src\\main\\java\\com\\crrc\\network\\api | 1 | 24 | 66 | 18 | 108 |
| network\\src\\main\\java\\com\\crrc\\network\\base | 1 | 1 | -1 | 0 | 0 |
| network\\src\\main\\java\\com\\crrc\\network\\commoninterceptor | 1 | 1 | 1 | 0 | 2 |
| network\\src\\main\\res | 2 | -22 | -10 | 0 | -32 |
| network\\src\\main\\res\\values | 1 | -11 | -5 | 0 | -16 |
| network\\src\\main\\res\\values-night | 1 | -11 | -5 | 0 | -16 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)