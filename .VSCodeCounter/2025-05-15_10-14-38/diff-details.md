# Diff Details

Date : 2025-05-15 10:14:38

Directory c:\\Users\\<USER>\\Desktop\\app\\siom

Total : 61 files,  2196 codes, 284 comments, 490 blanks, all 2970 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.idea/appInsightsSettings.xml](/.idea/appInsightsSettings.xml) | XML | 15 | 0 | 0 | 15 |
| [.idea/deploymentTargetSelector.xml](/.idea/deploymentTargetSelector.xml) | XML | 11 | 0 | 0 | 11 |
| [app/build.gradle](/app/build.gradle) | Groovy | 18 | 1 | 3 | 22 |
| [app/libs/build.properties](/app/libs/build.properties) | Properties | 10 | 0 | 1 | 11 |
| [app/release/output-metadata.json](/app/release/output-metadata.json) | JSON | 37 | 0 | 0 | 37 |
| [app/src/main/AndroidManifest.xml](/app/src/main/AndroidManifest.xml) | XML | 12 | 3 | 0 | 15 |
| [app/src/main/java/com/crrc/siom/AppApplication.java](/app/src/main/java/com/crrc/siom/AppApplication.java) | Java | 9 | 4 | 0 | 13 |
| [app/src/main/java/com/crrc/siom/data/model/AlarmInfo.java](/app/src/main/java/com/crrc/siom/data/model/AlarmInfo.java) | Java | 29 | 0 | 8 | 37 |
| [app/src/main/java/com/crrc/siom/data/model/StorageItem.java](/app/src/main/java/com/crrc/siom/data/model/StorageItem.java) | Java | 37 | 0 | 10 | 47 |
| [app/src/main/java/com/crrc/siom/ui/line/AlarmFragment.java](/app/src/main/java/com/crrc/siom/ui/line/AlarmFragment.java) | Java | 90 | 0 | 20 | 110 |
| [app/src/main/java/com/crrc/siom/ui/line/DetailPanelManager.java](/app/src/main/java/com/crrc/siom/ui/line/DetailPanelManager.java) | Java | 128 | 43 | 29 | 200 |
| [app/src/main/java/com/crrc/siom/ui/line/LineMapJavaActivity.java](/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaActivity.java) | Java | 310 | 68 | 54 | 432 |
| [app/src/main/java/com/crrc/siom/ui/line/LineMapJavaViewModel.java](/app/src/main/java/com/crrc/siom/ui/line/LineMapJavaViewModel.java) | Java | 169 | 36 | 40 | 245 |
| [app/src/main/java/com/crrc/siom/ui/line/MapMarkerManager.java](/app/src/main/java/com/crrc/siom/ui/line/MapMarkerManager.java) | Java | 223 | 61 | 55 | 339 |
| [app/src/main/java/com/crrc/siom/ui/line/MapMarkerUtils.java](/app/src/main/java/com/crrc/siom/ui/line/MapMarkerUtils.java) | Java | 60 | 13 | 16 | 89 |
| [app/src/main/java/com/crrc/siom/ui/line/StationDetailAdapter.java](/app/src/main/java/com/crrc/siom/ui/line/StationDetailAdapter.java) | Java | 44 | 0 | 8 | 52 |
| [app/src/main/java/com/crrc/siom/ui/line/StorageFragment.java](/app/src/main/java/com/crrc/siom/ui/line/StorageFragment.java) | Java | 118 | 0 | 22 | 140 |
| [app/src/main/res/drawable/bg\_current\_location.xml](/app/src/main/res/drawable/bg_current_location.xml) | XML | 7 | 0 | 0 | 7 |
| [app/src/main/res/drawable/bg\_info\_window.xml](/app/src/main/res/drawable/bg_info_window.xml) | XML | 7 | 0 | 0 | 7 |
| [app/src/main/res/drawable/bg\_marker\_info.xml](/app/src/main/res/drawable/bg_marker_info.xml) | XML | 6 | 0 | 0 | 6 |
| [app/src/main/res/drawable/bg\_marker\_info\_bubble.xml](/app/src/main/res/drawable/bg_marker_info_bubble.xml) | XML | 15 | 0 | 0 | 15 |
| [app/src/main/res/drawable/bg\_marker\_info\_oval.xml](/app/src/main/res/drawable/bg_marker_info_oval.xml) | XML | 8 | 0 | 0 | 8 |
| [app/src/main/res/drawable/ic\_arrow\_back.xml](/app/src/main/res/drawable/ic_arrow_back.xml) | XML | 11 | 0 | 0 | 11 |
| [app/src/main/res/drawable/ic\_build.xml](/app/src/main/res/drawable/ic_build.xml) | XML | 11 | 0 | 0 | 11 |
| [app/src/main/res/drawable/ic\_dot.xml](/app/src/main/res/drawable/ic_dot.xml) | XML | 7 | 0 | 0 | 7 |
| [app/src/main/res/drawable/ic\_warning.xml](/app/src/main/res/drawable/ic_warning.xml) | XML | 11 | 0 | 0 | 11 |
| [app/src/main/res/layout/activity\_line\_map.xml](/app/src/main/res/layout/activity_line_map.xml) | XML | 135 | 0 | 18 | 153 |
| [app/src/main/res/layout/custom\_info\_window.xml](/app/src/main/res/layout/custom_info_window.xml) | XML | 26 | 0 | 3 | 29 |
| [app/src/main/res/layout/custom\_marker\_info.xml](/app/src/main/res/layout/custom_marker_info.xml) | XML | 22 | 0 | 3 | 25 |
| [app/src/main/res/layout/fragment\_alarm.xml](/app/src/main/res/layout/fragment_alarm.xml) | XML | 18 | 0 | 2 | 20 |
| [app/src/main/res/layout/fragment\_storage.xml](/app/src/main/res/layout/fragment_storage.xml) | XML | 18 | 0 | 2 | 20 |
| [app/src/main/res/layout/item\_alarm.xml](/app/src/main/res/layout/item_alarm.xml) | XML | 56 | 0 | 7 | 63 |
| [app/src/main/res/layout/item\_storage.xml](/app/src/main/res/layout/item_storage.xml) | XML | 95 | 0 | 11 | 106 |
| [app/src/main/res/values-night/themes.xml](/app/src/main/res/values-night/themes.xml) | XML | -20 | -1 | -7 | -28 |
| [app/src/main/res/values/colors.xml](/app/src/main/res/values/colors.xml) | XML | 22 | 6 | 6 | 34 |
| [app/src/main/res/values/strings.xml](/app/src/main/res/values/strings.xml) | XML | 1 | 0 | 0 | 1 |
| [app/src/main/res/values/themes.xml](/app/src/main/res/values/themes.xml) | XML | -9 | 0 | 1 | -8 |
| [common/src/main/java/com/crrc/common/Constant.java](/common/src/main/java/com/crrc/common/Constant.java) | Java | 3 | -4 | 1 | 0 |
| [common/src/main/java/com/crrc/common/bean/response/AlarmInfoResponse.java](/common/src/main/java/com/crrc/common/bean/response/AlarmInfoResponse.java) | Java | -36 | -3 | -13 | -52 |
| [common/src/main/java/com/crrc/common/bean/response/AlarmResponse.java](/common/src/main/java/com/crrc/common/bean/response/AlarmResponse.java) | Java | 20 | 0 | 5 | 25 |
| [common/src/main/java/com/crrc/common/bean/response/DeviceInfo.java](/common/src/main/java/com/crrc/common/bean/response/DeviceInfo.java) | Java | 102 | 3 | 32 | 137 |
| [common/src/main/java/com/crrc/common/bean/response/EmergencyDetailResponse.java](/common/src/main/java/com/crrc/common/bean/response/EmergencyDetailResponse.java) | Java | 12 | 0 | 5 | 17 |
| [common/src/main/java/com/crrc/common/bean/response/EmergencyEventListResponse.java](/common/src/main/java/com/crrc/common/bean/response/EmergencyEventListResponse.java) | Java | 47 | 3 | 18 | 68 |
| [common/src/main/java/com/crrc/common/bean/response/EmergencyOrderListResponse.java](/common/src/main/java/com/crrc/common/bean/response/EmergencyOrderListResponse.java) | Java | 0 | 0 | 1 | 1 |
| [common/src/main/java/com/crrc/common/bean/response/FaultDetailResponse.java](/common/src/main/java/com/crrc/common/bean/response/FaultDetailResponse.java) | Java | 10 | 0 | 4 | 14 |
| [common/src/main/java/com/crrc/common/bean/response/LineDevice.java](/common/src/main/java/com/crrc/common/bean/response/LineDevice.java) | Java | 31 | 0 | 11 | 42 |
| [common/src/main/java/com/crrc/common/bean/response/LineInfoResponse.java](/common/src/main/java/com/crrc/common/bean/response/LineInfoResponse.java) | Java | -33 | 0 | -11 | -44 |
| [common/src/main/java/com/crrc/common/bean/response/LineTool.java](/common/src/main/java/com/crrc/common/bean/response/LineTool.java) | Java | 31 | 0 | 11 | 42 |
| [common/src/main/java/com/crrc/common/bean/response/LoginBean.java](/common/src/main/java/com/crrc/common/bean/response/LoginBean.java) | Java | 31 | 0 | 11 | 42 |
| [common/src/main/java/com/crrc/common/bean/response/MembersResponse.java](/common/src/main/java/com/crrc/common/bean/response/MembersResponse.java) | Java | 38 | 0 | 17 | 55 |
| [common/src/main/java/com/crrc/common/bean/response/OrderFilterParamResponse.java](/common/src/main/java/com/crrc/common/bean/response/OrderFilterParamResponse.java) | Java | 59 | 0 | 24 | 83 |
| [common/src/main/java/com/crrc/common/bean/response/UserInfoResponse.java](/common/src/main/java/com/crrc/common/bean/response/UserInfoResponse.java) | Java | 66 | 0 | 20 | 86 |
| [common/src/main/java/com/crrc/common/utils/GetRequestBody.java](/common/src/main/java/com/crrc/common/utils/GetRequestBody.java) | Java | 16 | 5 | 9 | 30 |
| [common/src/main/res/values-night/themes.xml](/common/src/main/res/values-night/themes.xml) | XML | -11 | -5 | 0 | -16 |
| [common/src/main/res/values/themes.xml](/common/src/main/res/values/themes.xml) | XML | -11 | -5 | 0 | -16 |
| [network/src/main/java/com/crrc/network/api/ApiInterface.java](/network/src/main/java/com/crrc/network/api/ApiInterface.java) | Java | 24 | 66 | 18 | 108 |
| [network/src/main/java/com/crrc/network/base/NetworkApi.java](/network/src/main/java/com/crrc/network/base/NetworkApi.java) | Java | 1 | -1 | 0 | 0 |
| [network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java](/network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java) | Java | 1 | 1 | 0 | 2 |
| [network/src/main/res/values-night/themes.xml](/network/src/main/res/values-night/themes.xml) | XML | -11 | -5 | 0 | -16 |
| [network/src/main/res/values/themes.xml](/network/src/main/res/values/themes.xml) | XML | -11 | -5 | 0 | -16 |
| [readme.md](/readme.md) | Markdown | 50 | 0 | 15 | 65 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details