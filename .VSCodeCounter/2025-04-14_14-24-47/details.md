# Details

Date : 2025-04-14 14:24:47

Directory c:\\Users\\<USER>\\Desktop\\app\\siom

Total : 114 files,  4792 codes, 1955 comments, 1169 blanks, all 7916 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.idea/AndroidProjectSystem.xml](/.idea/AndroidProjectSystem.xml) | XML | 6 | 0 | 0 | 6 |
| [.idea/appInsightsSettings.xml](/.idea/appInsightsSettings.xml) | XML | 26 | 0 | 0 | 26 |
| [.idea/compiler.xml](/.idea/compiler.xml) | XML | 6 | 0 | 0 | 6 |
| [.idea/deploymentTargetSelector.xml](/.idea/deploymentTargetSelector.xml) | XML | 19 | 0 | 0 | 19 |
| [.idea/gradle.xml](/.idea/gradle.xml) | XML | 21 | 0 | 0 | 21 |
| [.idea/inspectionProfiles/Project\_Default.xml](/.idea/inspectionProfiles/Project_Default.xml) | XML | 61 | 0 | 0 | 61 |
| [.idea/kotlinc.xml](/.idea/kotlinc.xml) | XML | 6 | 0 | 0 | 6 |
| [.idea/migrations.xml](/.idea/migrations.xml) | XML | 10 | 0 | 0 | 10 |
| [.idea/misc.xml](/.idea/misc.xml) | XML | 13 | 0 | 0 | 13 |
| [.idea/runConfigurations.xml](/.idea/runConfigurations.xml) | XML | 17 | 0 | 0 | 17 |
| [.idea/vcs.xml](/.idea/vcs.xml) | XML | 6 | 0 | 0 | 6 |
| [app/build.gradle](/app/build.gradle) | Groovy | 51 | 0 | 6 | 57 |
| [app/src/main/AndroidManifest.xml](/app/src/main/AndroidManifest.xml) | XML | 76 | 0 | 1 | 77 |
| [app/src/main/java/com/crrc/siom/AppApplication.java](/app/src/main/java/com/crrc/siom/AppApplication.java) | Java | 24 | 18 | 15 | 57 |
| [app/src/main/java/com/crrc/siom/NetworkRequestInfo.java](/app/src/main/java/com/crrc/siom/NetworkRequestInfo.java) | Java | 25 | 3 | 12 | 40 |
| [app/src/main/res/drawable/ic\_dashboard\_black\_24dp.xml](/app/src/main/res/drawable/ic_dashboard_black_24dp.xml) | XML | 9 | 0 | 1 | 10 |
| [app/src/main/res/drawable/ic\_home\_black\_24dp.xml](/app/src/main/res/drawable/ic_home_black_24dp.xml) | XML | 9 | 0 | 1 | 10 |
| [app/src/main/res/drawable/ic\_launcher\_background.xml](/app/src/main/res/drawable/ic_launcher_background.xml) | XML | 170 | 0 | 1 | 171 |
| [app/src/main/res/drawable/ic\_launcher\_foreground.xml](/app/src/main/res/drawable/ic_launcher_foreground.xml) | XML | 30 | 0 | 0 | 30 |
| [app/src/main/res/drawable/ic\_notifications\_black\_24dp.xml](/app/src/main/res/drawable/ic_notifications_black_24dp.xml) | XML | 9 | 0 | 1 | 10 |
| [app/src/main/res/menu/bottom\_nav\_menu.xml](/app/src/main/res/menu/bottom_nav_menu.xml) | XML | 15 | 0 | 4 | 19 |
| [app/src/main/res/mipmap-anydpi/ic\_launcher.xml](/app/src/main/res/mipmap-anydpi/ic_launcher.xml) | XML | 6 | 0 | 0 | 6 |
| [app/src/main/res/mipmap-anydpi/ic\_launcher\_round.xml](/app/src/main/res/mipmap-anydpi/ic_launcher_round.xml) | XML | 6 | 0 | 0 | 6 |
| [app/src/main/res/values-night/colors.xml](/app/src/main/res/values-night/colors.xml) | XML | 17 | 6 | 5 | 28 |
| [app/src/main/res/values-night/themes.xml](/app/src/main/res/values-night/themes.xml) | XML | 20 | 1 | 7 | 28 |
| [app/src/main/res/values/colors.xml](/app/src/main/res/values/colors.xml) | XML | 53 | 0 | 0 | 53 |
| [app/src/main/res/values/dimens.xml](/app/src/main/res/values/dimens.xml) | XML | 4 | 1 | 0 | 5 |
| [app/src/main/res/values/strings.xml](/app/src/main/res/values/strings.xml) | XML | 6 | 0 | 0 | 6 |
| [app/src/main/res/values/themes.xml](/app/src/main/res/values/themes.xml) | XML | 22 | 0 | 1 | 23 |
| [app/src/main/res/xml/backup\_rules.xml](/app/src/main/res/xml/backup_rules.xml) | XML | 3 | 10 | 0 | 13 |
| [app/src/main/res/xml/data\_extraction\_rules.xml](/app/src/main/res/xml/data_extraction_rules.xml) | XML | 5 | 14 | 0 | 19 |
| [build.gradle](/build.gradle) | Groovy | 5 | 1 | 0 | 6 |
| [common/build.gradle](/common/build.gradle) | Groovy | 69 | 10 | 12 | 91 |
| [common/src/main/AndroidManifest.xml](/common/src/main/AndroidManifest.xml) | XML | 4 | 0 | 1 | 5 |
| [common/src/main/java/com/crrc/common/BaseApplication.java](/common/src/main/java/com/crrc/common/BaseApplication.java) | Java | 10 | 3 | 5 | 18 |
| [common/src/main/java/com/crrc/common/BaseResponse.java](/common/src/main/java/com/crrc/common/BaseResponse.java) | Java | 18 | 3 | 9 | 30 |
| [common/src/main/java/com/crrc/common/Constant.java](/common/src/main/java/com/crrc/common/Constant.java) | Java | 403 | 1,168 | 140 | 1,711 |
| [common/src/main/java/com/crrc/common/bean/request/PickMaterialRequest.java](/common/src/main/java/com/crrc/common/bean/request/PickMaterialRequest.java) | Java | 37 | 3 | 13 | 53 |
| [common/src/main/java/com/crrc/common/bean/response/AlarmInfoResponse.java](/common/src/main/java/com/crrc/common/bean/response/AlarmInfoResponse.java) | Java | 36 | 3 | 13 | 52 |
| [common/src/main/java/com/crrc/common/bean/response/BaseOrderRecord.java](/common/src/main/java/com/crrc/common/bean/response/BaseOrderRecord.java) | Java | 76 | 0 | 30 | 106 |
| [common/src/main/java/com/crrc/common/bean/response/CaseDetailResponse.java](/common/src/main/java/com/crrc/common/bean/response/CaseDetailResponse.java) | Java | 86 | 36 | 35 | 157 |
| [common/src/main/java/com/crrc/common/bean/response/CaseListResponse.java](/common/src/main/java/com/crrc/common/bean/response/CaseListResponse.java) | Java | 101 | 45 | 41 | 187 |
| [common/src/main/java/com/crrc/common/bean/response/DocumentResponse.java](/common/src/main/java/com/crrc/common/bean/response/DocumentResponse.java) | Java | 44 | 0 | 16 | 60 |
| [common/src/main/java/com/crrc/common/bean/response/EmergencyOrderListResponse.java](/common/src/main/java/com/crrc/common/bean/response/EmergencyOrderListResponse.java) | Java | 34 | 0 | 12 | 46 |
| [common/src/main/java/com/crrc/common/bean/response/EvenBusGoLoginData.java](/common/src/main/java/com/crrc/common/bean/response/EvenBusGoLoginData.java) | Java | 7 | 5 | 3 | 15 |
| [common/src/main/java/com/crrc/common/bean/response/FaultOrderListResponse.java](/common/src/main/java/com/crrc/common/bean/response/FaultOrderListResponse.java) | Java | 34 | 0 | 12 | 46 |
| [common/src/main/java/com/crrc/common/bean/response/FaultReportResponse.java](/common/src/main/java/com/crrc/common/bean/response/FaultReportResponse.java) | Java | 55 | 3 | 19 | 77 |
| [common/src/main/java/com/crrc/common/bean/response/LineInfoResponse.java](/common/src/main/java/com/crrc/common/bean/response/LineInfoResponse.java) | Java | 73 | 3 | 25 | 101 |
| [common/src/main/java/com/crrc/common/bean/response/PickMaterialResponse.java](/common/src/main/java/com/crrc/common/bean/response/PickMaterialResponse.java) | Java | 55 | 3 | 19 | 77 |
| [common/src/main/java/com/crrc/common/bean/response/PlanOrderListResponse.java](/common/src/main/java/com/crrc/common/bean/response/PlanOrderListResponse.java) | Java | 39 | 3 | 15 | 57 |
| [common/src/main/java/com/crrc/common/bean/response/SearchResponse.java](/common/src/main/java/com/crrc/common/bean/response/SearchResponse.java) | Java | 49 | 3 | 16 | 68 |
| [common/src/main/java/com/crrc/common/bean/response/StationAlarmResponse.java](/common/src/main/java/com/crrc/common/bean/response/StationAlarmResponse.java) | Java | 36 | 3 | 13 | 52 |
| [common/src/main/java/com/crrc/common/bean/response/StoreInfoResponse.java](/common/src/main/java/com/crrc/common/bean/response/StoreInfoResponse.java) | Java | 47 | 3 | 16 | 66 |
| [common/src/main/java/com/crrc/common/bean/response/TaskOverviewResponse.java](/common/src/main/java/com/crrc/common/bean/response/TaskOverviewResponse.java) | Java | 36 | 3 | 13 | 52 |
| [common/src/main/java/com/crrc/common/bean/response/TempOrderListResponse.java](/common/src/main/java/com/crrc/common/bean/response/TempOrderListResponse.java) | Java | 34 | 0 | 11 | 45 |
| [common/src/main/java/com/crrc/common/bean/response/TokenIndoData.java](/common/src/main/java/com/crrc/common/bean/response/TokenIndoData.java) | Java | 150 | 5 | 37 | 192 |
| [common/src/main/java/com/crrc/common/bean/response/WorkOrderCountResponse.java](/common/src/main/java/com/crrc/common/bean/response/WorkOrderCountResponse.java) | Java | 36 | 3 | 14 | 53 |
| [common/src/main/java/com/crrc/common/bean/response/WorkOrderDetailResponse.java](/common/src/main/java/com/crrc/common/bean/response/WorkOrderDetailResponse.java) | Java | 211 | 7 | 76 | 294 |
| [common/src/main/java/com/crrc/common/observer/MvvmDataObserver.java](/common/src/main/java/com/crrc/common/observer/MvvmDataObserver.java) | Java | 13 | 5 | 7 | 25 |
| [common/src/main/java/com/crrc/common/ui/CustomProgressDialog.java](/common/src/main/java/com/crrc/common/ui/CustomProgressDialog.java) | Java | 44 | 11 | 15 | 70 |
| [common/src/main/java/com/crrc/common/utils/EventBusUtils.java](/common/src/main/java/com/crrc/common/utils/EventBusUtils.java) | Java | 36 | 46 | 15 | 97 |
| [common/src/main/java/com/crrc/common/utils/GsonUtil.java](/common/src/main/java/com/crrc/common/utils/GsonUtil.java) | Java | 70 | 51 | 17 | 138 |
| [common/src/main/java/com/crrc/common/utils/LiveDataUtils.java](/common/src/main/java/com/crrc/common/utils/LiveDataUtils.java) | Java | 39 | 12 | 13 | 64 |
| [common/src/main/java/com/crrc/common/utils/MMkvUtil.java](/common/src/main/java/com/crrc/common/utils/MMkvUtil.java) | Java | 81 | 42 | 24 | 147 |
| [common/src/main/java/com/crrc/common/utils/PrefsUtil.java](/common/src/main/java/com/crrc/common/utils/PrefsUtil.java) | Java | 63 | 0 | 24 | 87 |
| [common/src/main/java/com/crrc/common/utils/ToastUtil.java](/common/src/main/java/com/crrc/common/utils/ToastUtil.java) | Java | 32 | 8 | 10 | 50 |
| [common/src/main/res/drawable/ic\_launcher\_background.xml](/common/src/main/res/drawable/ic_launcher_background.xml) | XML | 170 | 0 | 1 | 171 |
| [common/src/main/res/drawable/ic\_launcher\_foreground.xml](/common/src/main/res/drawable/ic_launcher_foreground.xml) | XML | 30 | 0 | 0 | 30 |
| [common/src/main/res/layout/layout\_loading.xml](/common/src/main/res/layout/layout_loading.xml) | XML | 10 | 0 | 3 | 13 |
| [common/src/main/res/mipmap-anydpi/ic\_launcher.xml](/common/src/main/res/mipmap-anydpi/ic_launcher.xml) | XML | 6 | 0 | 0 | 6 |
| [common/src/main/res/mipmap-anydpi/ic\_launcher\_round.xml](/common/src/main/res/mipmap-anydpi/ic_launcher_round.xml) | XML | 6 | 0 | 0 | 6 |
| [common/src/main/res/values-night/themes.xml](/common/src/main/res/values-night/themes.xml) | XML | 11 | 5 | 0 | 16 |
| [common/src/main/res/values/colors.xml](/common/src/main/res/values/colors.xml) | XML | 10 | 0 | 0 | 10 |
| [common/src/main/res/values/strings.xml](/common/src/main/res/values/strings.xml) | XML | 4 | 0 | 0 | 4 |
| [common/src/main/res/values/themes.xml](/common/src/main/res/values/themes.xml) | XML | 11 | 5 | 0 | 16 |
| [gradle.properties](/gradle.properties) | Properties | 4 | 19 | 0 | 23 |
| [gradle/wrapper/gradle-wrapper.properties](/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 1 | 1 | 7 |
| [gradlew.bat](/gradlew.bat) | Batch | 39 | 29 | 22 | 90 |
| [network/build.gradle](/network/build.gradle) | Groovy | 32 | 0 | 7 | 39 |
| [network/src/main/AndroidManifest.xml](/network/src/main/AndroidManifest.xml) | XML | 3 | 0 | 3 | 6 |
| [network/src/main/java/com/crrc/network/NetworkApi.java](/network/src/main/java/com/crrc/network/NetworkApi.java) | Java | 55 | 4 | 13 | 72 |
| [network/src/main/java/com/crrc/network/api/ApiInterface.java](/network/src/main/java/com/crrc/network/api/ApiInterface.java) | Java | 95 | 92 | 39 | 226 |
| [network/src/main/java/com/crrc/network/base/INetworkRequiredInfo.java](/network/src/main/java/com/crrc/network/base/INetworkRequiredInfo.java) | Java | 8 | 23 | 7 | 38 |
| [network/src/main/java/com/crrc/network/base/NetworkApi.java](/network/src/main/java/com/crrc/network/base/NetworkApi.java) | Java | 92 | 43 | 15 | 150 |
| [network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java](/network/src/main/java/com/crrc/network/commoninterceptor/CommonRequestInterceptor.java) | Java | 34 | 4 | 11 | 49 |
| [network/src/main/java/com/crrc/network/commoninterceptor/CommonResponseInterceptor.java](/network/src/main/java/com/crrc/network/commoninterceptor/CommonResponseInterceptor.java) | Java | 12 | 4 | 8 | 24 |
| [network/src/main/java/com/crrc/network/commoninterceptor/DynamicTimeoutInterceptor.java](/network/src/main/java/com/crrc/network/commoninterceptor/DynamicTimeoutInterceptor.java) | Java | 38 | 4 | 10 | 52 |
| [network/src/main/java/com/crrc/network/download/DownloadCallBack.java](/network/src/main/java/com/crrc/network/download/DownloadCallBack.java) | Java | 8 | 29 | 9 | 46 |
| [network/src/main/java/com/crrc/network/download/DownloadInfo.java](/network/src/main/java/com/crrc/network/download/DownloadInfo.java) | Java | 60 | 0 | 22 | 82 |
| [network/src/main/java/com/crrc/network/download/DownloadProgressHandler.java](/network/src/main/java/com/crrc/network/download/DownloadProgressHandler.java) | Java | 47 | 14 | 13 | 74 |
| [network/src/main/java/com/crrc/network/download/FileDownloader.java](/network/src/main/java/com/crrc/network/download/FileDownloader.java) | Java | 145 | 12 | 21 | 178 |
| [network/src/main/java/com/crrc/network/environment/EnvironmentActivity.java](/network/src/main/java/com/crrc/network/environment/EnvironmentActivity.java) | Java | 57 | 10 | 15 | 82 |
| [network/src/main/java/com/crrc/network/environment/IEnvironment.java](/network/src/main/java/com/crrc/network/environment/IEnvironment.java) | Java | 4 | 9 | 3 | 16 |
| [network/src/main/java/com/crrc/network/errorhandler/ExceptionHandle.java](/network/src/main/java/com/crrc/network/errorhandler/ExceptionHandle.java) | Java | 124 | 24 | 19 | 167 |
| [network/src/main/java/com/crrc/network/errorhandler/HttpErrorHandler.java](/network/src/main/java/com/crrc/network/errorhandler/HttpErrorHandler.java) | Java | 9 | 6 | 4 | 19 |
| [network/src/main/java/com/crrc/network/observer/BaseObserver.java](/network/src/main/java/com/crrc/network/observer/BaseObserver.java) | Java | 38 | 5 | 8 | 51 |
| [network/src/main/java/com/crrc/network/observer/BaseObserverForBasic.java](/network/src/main/java/com/crrc/network/observer/BaseObserverForBasic.java) | Java | 37 | 4 | 8 | 49 |
| [network/src/main/java/com/crrc/network/upload/FileProgressRequestBody.java](/network/src/main/java/com/crrc/network/upload/FileProgressRequestBody.java) | Java | 48 | 15 | 13 | 76 |
| [network/src/main/res/drawable/ic\_controls.xml](/network/src/main/res/drawable/ic_controls.xml) | XML | 15 | 0 | 1 | 16 |
| [network/src/main/res/layout/activity\_environment.xml](/network/src/main/res/layout/activity_environment.xml) | XML | 7 | 0 | 0 | 7 |
| [network/src/main/res/mipmap-anydpi/ic\_launcher.xml](/network/src/main/res/mipmap-anydpi/ic_launcher.xml) | XML | 6 | 0 | 0 | 6 |
| [network/src/main/res/mipmap-anydpi/ic\_launcher\_round.xml](/network/src/main/res/mipmap-anydpi/ic_launcher_round.xml) | XML | 6 | 0 | 0 | 6 |
| [network/src/main/res/values-night/themes.xml](/network/src/main/res/values-night/themes.xml) | XML | 11 | 5 | 0 | 16 |
| [network/src/main/res/values/array.xml](/network/src/main/res/values/array.xml) | XML | 11 | 0 | 1 | 12 |
| [network/src/main/res/values/colors.xml](/network/src/main/res/values/colors.xml) | XML | 10 | 0 | 0 | 10 |
| [network/src/main/res/values/strings.xml](/network/src/main/res/values/strings.xml) | XML | 3 | 0 | 0 | 3 |
| [network/src/main/res/values/themes.xml](/network/src/main/res/values/themes.xml) | XML | 11 | 5 | 0 | 16 |
| [network/src/main/res/xml/environment\_preference.xml](/network/src/main/res/xml/environment_preference.xml) | XML | 11 | 0 | 2 | 13 |
| [readme.md](/readme.md) | Markdown | 4 | 0 | 3 | 7 |
| [settings.gradle](/settings.gradle) | Groovy | 41 | 3 | 3 | 47 |
| [tools/llm\_api.py](/tools/llm_api.py) | Python | 225 | 14 | 33 | 272 |
| [tools/screenshot\_utils.py](/tools/screenshot_utils.py) | Python | 44 | 2 | 10 | 56 |
| [tools/search\_engine.py](/tools/search_engine.py) | Python | 64 | 1 | 15 | 80 |
| [tools/web\_scraper.py](/tools/web_scraper.py) | Python | 157 | 20 | 30 | 207 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)